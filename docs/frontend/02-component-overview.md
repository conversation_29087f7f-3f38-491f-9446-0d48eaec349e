# Frontend Component Overview

This document provides a high-level overview of key reusable React components in the LLM Evaluation Platform frontend. It focuses on their purpose and primary functionalities rather than detailed props or implementation.

## 1. Core Application Components

-   **`App.tsx`**
    -   **Purpose:** The root component of the application.
    -   **Functionality:** Sets up the main layout (header, content area, footer), initializes global contexts (like `ThemeProvider`), and likely handles top-level routing if a library like React Router is integrated.

-   **`AgentEvaluationLayout.tsx` & `LLMEvaluationLayout.tsx` (`pages/`)
    -   **Purpose:** Layout orchestrators for different evaluation types (Agent and LLM evaluations).
    -   **Functionality:** Manages the layout between the `EvaluationSidebar` and the main content area. Fetches initial data like available models and evaluation history. Provides unified navigation and breadcrumb management for their respective evaluation types.

-   **`NewConversationPage.tsx` (`pages/`)
    -   **Purpose:** Provides the UI for starting a new evaluation task.
    -   **Functionality:** Contains the `PromptInput` component to allow users to enter a new prompt and select generator models. Handles the submission of new tasks to the backend via the Zustand store.

-   **`ViewTaskPage.tsx` (`pages/`)
    -   **Purpose:** Displays the detailed view of a selected task, including its prompt, generated outputs, and evaluation reports.
    -   **Functionality:** Fetches and displays task details based on the `taskId`. Orchestrates the `OutputDisplay`, `EvaluateArea`, and `ReportViewer` components. Allows users to switch between different generated outputs and different evaluator reports.

## 2. Input & Control Components

-   **`PromptInput.tsx` (`components/`)
    -   **Purpose:** Allows users to input their main prompt and select generator LLMs.
    -   **Functionality:** Provides a textarea for the prompt and uses the `ModelSelector` (or similar logic) to list and allow selection of available generator models. Handles form submission and calls the appropriate action in the Zustand store to create a new task. Can also display prompt and selected models in a read-only mode for historical tasks.

-   **`ModelSelector.tsx` (`components/`)
    -   **Purpose:** A reusable component for selecting one or more models from a list.
    -   **Functionality:** Renders a list of available models (passed as props) typically as checkboxes or selectable tags. Manages the state of selected models and calls a callback function (`onSelectionChange`) when the selection changes. Used for both generator and evaluator model selection.

-   **`EvaluateArea.tsx` (`components/`)
    -   **Purpose:** Allows users to select evaluator LLMs and trigger the evaluation process for the current task's outputs.
    -   **Functionality:** Uses the `ModelSelector` to display available models for evaluation. Provides a button to submit the evaluation request, which calls an action in the Zustand store.

## 3. Display Components

-   **`OutputDisplay.tsx` (`components/`)
    -   **Purpose:** Displays the generated outputs from the LLMs for the current task.
    -   **Functionality:** Manages the presentation of multiple generated outputs. Typically allows switching between outputs if multiple generator models were used (e.g., via tabs or a dropdown that updates `selectedModelIndex`). Uses `SingleOutputDisplay` to render the content of the currently selected model's output. Includes a resizable panel for the output area.

-   **`SingleOutputDisplay.tsx` (`components/`)
    -   **Purpose:** Renders the output (and optional reasoning) from a single LLM generation.
    -   **Functionality:** Handles Markdown rendering (using `react-markdown`) for formatted text and code block highlighting (using `react-syntax-highlighter`). Provides a copy-to-clipboard feature for code blocks. Can display generation errors if they occurred. Reasoning text is often shown in a collapsible section.

-   **`ReportViewer.tsx` (`components/`)
    -   **Purpose:** Displays the evaluation report from one or more evaluator LLMs.
    -   **Functionality:** Shows the ranked list of generator models and the reasoning provided by each selected evaluator. If multiple evaluators are part of the report, it might allow switching views or present them sequentially. Handles cases where an evaluator might have failed and displays error messages.

## 4. Navigation & UI Shell Components

-   **`EvaluationSidebar.tsx` (`components/`)
    -   **Purpose:** A unified sidebar component for displaying evaluation history and navigation.
    -   **Functionality:** Lists evaluations with their titles, status, and creation time. Allows users to select an evaluation to view its details, delete evaluations (with confirmation), and start new evaluations. Includes a search/filter bar for the history. Supports both Agent and LLM evaluation types with configurable display options.

-   **`ThemeToggle.tsx` (`components/`)
    -   **Purpose:** Allows users to switch between light and dark color themes for the application.
    -   **Functionality:** Interacts with the `ThemeContext` to change the current theme and persists the preference.

-   **`LoadingSpinner.tsx` (`components/`)
    -   **Purpose:** A reusable visual indicator for loading states.
    -   **Functionality:** Displays an animated spinner and an optional loading message. Used in various parts of the UI when data is being fetched or processed.

## 5. Context Providers

-   **`ThemeContext.tsx` (`context/`)
    -   **Purpose:** Provides theme-related state (current theme, resolved theme) and functions to the entire application.
    -   **Functionality:** Manages theme changes (light, dark, system preference) and applies the appropriate CSS classes to the root HTML element. Persists theme preference in local storage. 