# Frontend Performance Optimization

## Overview

This document describes the performance optimization strategies implemented in the LLM evaluation platform frontend, specifically addressing high-frequency streaming data challenges.

## Problem Statement

While developing the LLM evaluation platform, we encountered a serious performance issue: when multiple AI models simultaneously performed streaming text generation, the frontend interface would experience severe lag or even freeze. The core problems were:

- **High-frequency data updates**: Each model generates a text chunk every few tens of milliseconds
- **Multi-model concurrency**: The interface becomes unusable with 2-3 models running simultaneously  
- **React state management**: Each chunk triggers React re-rendering

## Performance Bottleneck Analysis

### Issues with Initial Implementation

```typescript
// Problematic code: every chunk triggers state update
(streamData: StreamEventData) => {
  set(state => {
    const currentTask = state.tasks[taskId];
    // Each chunk triggers complete component tree re-render
    return { 
      tasks: { 
        ...state.tasks, 
        [taskId]: { 
          ...currentTask, 
          outputs: newOutputs 
        }
      }
    };
  });
}
```

### Root Causes

1. **Frequent React re-renders** - Each chunk triggers a complete virtual DOM diff
2. **JavaScript main thread blocking** - High-frequency state updates consume excessive CPU time
3. **Zustand state propagation** - Every update notifies all subscribed components
4. **Component cascade updates** - Parent component updates cause indiscriminate child re-renders

## Solution: Chunk Buffering Mechanism

We implemented a **chunk buffering mechanism** that converts high-frequency data updates into low-frequency UI updates.

### Core Concept

- **Data layer**: Immediately receive and cache chunk data
- **Rendering layer**: Batch UI updates, control rendering frequency
- **User experience**: Maintain real-time feel while ensuring smooth interface

### Technical Implementation

#### 1. State Structure Extension

```typescript
export interface TaskState {
  // Existing fields...
  
  // New buffering-related fields
  chunkBuffer: Record<string, { 
    output_text: string; 
    reasoning_text: string 
  }>;
  bufferFlushTimer: number | null;
  lastFlushTime: number;
}
```

#### 2. Buffering Mechanism

```typescript
// When receiving chunks, only update buffer
(streamData: StreamEventData) => {
  const currentTask = get().tasks[taskId];
  if (!currentTask) return;

  // Cache chunk data
  if (!currentTask.chunkBuffer[modelId]) {
    currentTask.chunkBuffer[modelId] = { 
      output_text: '', 
      reasoning_text: '' 
    };
  }
  currentTask.chunkBuffer[modelId].output_text += chunkText;
  currentTask.chunkBuffer[modelId].reasoning_text += reasoningText;

  // Intelligent update scheduling
  if (!currentTask.bufferFlushTimer) {
    const scheduleFlush = () => {
      const now = Date.now();
      const timeSinceLastFlush = now - (currentTask.lastFlushTime || 0);
      
      if (timeSinceLastFlush >= 20) { // 20ms interval = ~50fps
        get().flushChunkBuffer(taskId);
      } else {
        const timerId = requestAnimationFrame(scheduleFlush);
        // Update timer reference
      }
    };
    
    requestAnimationFrame(scheduleFlush);
  }
}
```

#### 3. Batch Flush Mechanism

```typescript
flushChunkBuffer: (taskId: number) => {
  const taskState = get().tasks[taskId];
  if (!taskState || Object.keys(taskState.chunkBuffer).length === 0) return;
  
  set(state => {
    const currentTask = state.tasks[taskId];
    if (!currentTask) return state;

    // Batch apply all cached chunks
    const newOutputs = currentTask.outputs.map(output => {
      const bufferedData = currentTask.chunkBuffer[output.model_id_used];
      if (bufferedData) {
        return {
          ...output,
          output_text: (output.output_text || '') + bufferedData.output_text,
          reasoning_text: (output.reasoning_text || '') + bufferedData.reasoning_text
        };
      }
      return output;
    });

    return {
      tasks: {
        ...state.tasks,
        [taskId]: {
          ...currentTask,
          outputs: newOutputs,
          chunkBuffer: {}, // Clear buffer
          bufferFlushTimer: null,
          lastFlushTime: Date.now(),
        }
      }
    };
  });
}
```

## Optimization Upgrade: requestAnimationFrame

After initially solving the lag issue, we further optimized the buffering mechanism by transitioning from setTimeout to requestAnimationFrame.

### Implementation Comparison

```typescript
// Old approach: fixed 100ms interval
setTimeout(() => {
  get().flushChunkBuffer(taskId);
}, 100);

// New approach: requestAnimationFrame + 20ms control
const scheduleFlush = () => {
  const now = Date.now();
  const timeSinceLastFlush = now - (currentTask.lastFlushTime || 0);
  
  if (timeSinceLastFlush >= 20) {
    get().flushChunkBuffer(taskId);
  } else {
    requestAnimationFrame(scheduleFlush);
  }
};
requestAnimationFrame(scheduleFlush);
```

### Performance Comparison

| Metric | Original | setTimeout Buffer | requestAnimationFrame Buffer |
|--------|----------|------------------|------------------------------|
| **Update Frequency** | Per chunk (~20-50Hz) | ~10fps | ~50fps |
| **UI Responsiveness** | Severe lag | Slight delay | Silky smooth |
| **User Experience** | Unusable | Usable | Excellent |

## Technical Deep Dive

### Why React 19's Auto-Batching Isn't Sufficient

Despite using React 19.1.0, manual buffering was still necessary due to:

1. **Asynchronous nature of SSE events** - Each Server-Sent Event message is an independent event loop
2. **Zustand state management** - May bypass React's batching mechanism
3. **High-frequency update scenarios** - Beyond the optimization scope of auto-batching

### Advantages of requestAnimationFrame

1. **Browser synchronization** - Perfect alignment with rendering loop
2. **Performance-friendly** - Automatically pauses when tab is not visible
3. **Smooth experience** - Silky 60fps updates
4. **Resource conservation** - Avoids unnecessary background computation

## Performance Test Results

### Test Scenario
- 3 AI models generating simultaneously
- Each model produces a chunk every 30-50ms
- Continuous generation for 2 minutes

### Results

```
Original Implementation:
- UI freeze time: >80%
- Average FPS: <10

After Optimization:
- UI freeze time: 0%
- Average FPS: 50-60
```

## Implementation Guidelines

### When to Apply This Pattern

This optimization pattern is suitable for:

- High-frequency data streams (>10 updates/second)
- Real-time data visualization
- Multi-source concurrent updates
- CPU-intensive rendering scenarios

### Best Practices

1. **Buffer Management**: Keep buffer size reasonable to prevent memory bloat
2. **Timer Cleanup**: Always clean up timers on component unmount
3. **Frequency Control**: Adjust flush interval based on data characteristics
4. **Error Handling**: Implement robust error handling for stream interruptions

### Monitoring

Monitor these metrics to validate performance:

- Frame rate (target: 50-60fps)
- Main thread blocking time
- Memory usage patterns
- User interaction responsiveness

## Related Files

- `src/store/taskStore.ts` - Main implementation
- `src/components/OutputDisplay.tsx` - Consumer component
- `src/pages/ViewTaskPage.tsx` - Parent component

## Future Considerations

- Web Workers for heavy processing
- Virtual scrolling for large datasets
- Progressive rendering for complex visualizations
- Adaptive frequency based on system performance 