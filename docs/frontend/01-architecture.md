# Frontend Architecture

## 1. Overview

The frontend for the LLM Evaluation Platform is a single-page application (SPA) built using React and TypeScript. It provides a user interface for interacting with the backend, managing evaluation tasks, and viewing results. The UI is styled with Tailwind CSS and aims for a responsive and modern user experience, including dark/light theme support.

## 2. Core Technologies & Libraries

-   **React:** The core JavaScript library for building the user interface with a component-based architecture.
-   **TypeScript:** Adds static typing to JavaScript, improving code quality and maintainability.
-   **Vite:** Used as the build tool and development server, offering fast startup and hot module replacement (HMR). (Note: `package.json` shows `react-scripts`, implying Create React App (CRA) might be used, but <PERSON>ite is mentioned in `frontend/vite.config.ts` in the initial README anlysis. This guide will assume Vite/CRA-like setup for React development.)
-   **Zustand:** A small, fast, and scalable state management solution used for managing global application state (`taskStore.ts`).
-   **Axios:** A promise-based HTTP client used in `apiClient.ts` for making RESTful API calls to the backend.
-   **React Router (implicitly):** Although not explicitly detailed in the provided files, a typical SPA structure would use a library like React Router for client-side navigation between different views (e.g., new conversation, view task).
-   **Tailwind CSS:** A utility-first CSS framework for styling the application.
-   **Lucide React & Font Awesome:** Used for icons within the UI.
-   **`react-markdown` & `react-syntax-highlighter`:** Used for rendering Markdown content and highlighting code blocks in model outputs and reasoning.

## 3. Directory Structure & Key Modules

(Located under `llm-eval-platform/frontend/src/`)

-   **`main.tsx`:** The entry point of the React application. It renders the root `App` component and wraps it with context providers (e.g., `ThemeProvider`).
-   **`App.tsx`:** The main application component. It sets up the overall layout, including the header, main content area, and footer. It likely handles routing logic if React Router is used.
-   **`api/apiClient.ts`:** Centralizes all communication with the backend API. It defines TypeScript interfaces for API request/response payloads (matching backend Pydantic schemas) and provides functions for each API endpoint. It also includes logic for establishing and managing Server-Sent Event (SSE) connections for real-time updates.
-   **`assets/`:** Contains static assets like images (e.g., `logo.svg`).
-   **`components/`:** Contains reusable UI components used throughout the application. Examples:
    -   `PromptInput.tsx`: For entering prompts and selecting generator models.
    -   `OutputDisplay.tsx` & `SingleOutputDisplay.tsx`: For displaying LLM-generated outputs, including Markdown rendering and code highlighting.
    -   `EvaluateArea.tsx`: For selecting evaluator models and initiating evaluation.
    -   `ReportViewer.tsx`: For displaying evaluation reports, including rankings and reasoning.
    -   `ModelSelector.tsx`: A generic component for selecting models.
    -   `EvaluationSidebar.tsx`: A unified sidebar component for displaying evaluation history and navigation.
    -   `ThemeToggle.tsx`: Allows users to switch between light and dark themes.
    -   `LoadingSpinner.tsx`: A reusable loading indicator.
-   **`context/ThemeContext.tsx`:** Manages the application's theme (light/dark/system) using React Context, persisting the user's preference in local storage.
-   **`hooks/`:** (Directory exists, intended for custom React hooks, though no specific hooks were detailed in the provided files beyond `useTheme` from `ThemeContext`).
-   **`pages/`:** Contains top-level components that represent different views or pages of the application.
    -   `AgentEvaluationLayout.tsx` & `LLMEvaluationLayout.tsx`: Layout components orchestrating the sidebar and content area for different evaluation types.
    -   `NewConversationPage.tsx`: The view for starting a new task/prompt.
    -   `ViewTaskPage.tsx`: The view for displaying details of an existing task, its outputs, and evaluations.
-   **`store/taskStore.ts`:** Defines the Zustand store for global application state. This includes:
    -   Managing the currently selected task ID.
    -   Storing details of all tasks (prompts, outputs, evaluation reports).
    -   Tracking loading states (e.g., for models, history).
    -   Holding the list of available LLM models.
    -   Managing SSE connection state for active tasks.
    -   Handling polling logic for task status updates (generation and evaluation).
-   **`types/`:** (Directory exists, intended for global TypeScript type definitions, though most API types are co-located in `apiClient.ts`).

## 4. State Management (Zustand)

-   Global application state is managed by the Zustand store defined in `taskStore.ts`.
-   **Key pieces of state managed:**
    -   `selectedTaskId`: The ID of the task currently being viewed or interacted with.
    -   `tasks`: An object where keys are task IDs and values are `TaskState` objects containing all data related to a task (prompt, requested models, outputs, evaluation status, reports, SSE connection status, error messages).
    -   `taskHistory`: A list of summarized task items for the history sidebar.
    -   `availableModels`: List of LLMs available for generation/evaluation.
    -   Loading and error flags for various asynchronous operations.
-   **Actions:** The store defines actions to modify the state, such_as:
    -   Fetching available models and task history.
    -   Creating new tasks and loading details of existing tasks.
    -   Initiating evaluations.
    -   Managing SSE connections (initiating, closing, handling messages).
    -   Polling for status updates when SSE is not available or for final confirmation.

## 5. API Communication & Real-time Updates

-   **`apiClient.ts`:** All HTTP requests to the backend are made through methods defined in this file.
-   **Server-Sent Events (SSE):**
    -   The `sse.connect` function in `apiClient.ts` establishes an SSE connection to the backend's `/tasks/{task_id}/stream` endpoint.
    -   The `taskStore.ts` manages these SSE connections. When a task is active (generating), an SSE connection is initiated.
    -   Event handlers in the store process incoming SSE messages (`onMessage`, `onDone`, `onError`, `onOpen`, `onConnectionError`):
        -   `onMessage`: Appends new text chunks to the relevant model's output in the Zustand store.
        -   `onDone`: Marks a model as finished streaming. If all models for a task are done, it may trigger fetching final task details.
        -   `onError`: Handles errors specific to a model's stream or general connection errors, updating the task state with error messages and potentially initiating polling as a fallback.

## 6. Component Structure & Data Flow

-   **Page Components:** (`NewConversationPage.tsx`, `ViewTaskPage.tsx`) act as containers for specific views, fetching data from the Zustand store and passing it down to presentational components.
-   **Presentational Components:** (`PromptInput.tsx`, `OutputDisplay.tsx`, etc.) are responsible for rendering the UI based on the props they receive. They dispatch actions to the Zustand store via functions passed down as props (e.g., `onSubmit` in `PromptInput` calls `createNewTask` from the store).
-   **Data Flow:** Typically follows a unidirectional pattern: user interactions in components trigger actions in the Zustand store, the store updates its state (potentially after API calls), and React re-renders components subscribed to those state changes.

## 7. Styling and Theming

-   **Tailwind CSS:** Used for utility-first styling, allowing for rapid UI development.
-   **CSS Modules:** While Tailwind is primary, individual components might use CSS Modules for scoped styles if needed (e.g., `Button.module.css` was mentioned in a generic React rule, but not seen in current project files).
-   **ThemeContext:** `ThemeContext.tsx` provides theme state (`light`, `dark`) and allows toggling. The `resolvedTheme` is applied to the root HTML element, and Tailwind CSS dark mode variants (`dark:...`) are used for theme-specific styling. 