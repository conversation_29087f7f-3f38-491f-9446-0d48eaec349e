# Developer Guide

This guide provides information for developers looking to contribute to or extend the LLM Evaluation Platform.

## 1. Getting Started & Prerequisites

Refer to the main `README.md` file in the `llm-eval-platform` project root for detailed instructions on:

-   Prerequisites (Python, Node.js, Poetry, OpenRouter API Key).
-   Backend setup (environment variables, dependency installation, running the server).
-   Frontend setup (dependency installation, running the development server).

Ensure you have successfully run both the backend and frontend services locally before proceeding.

## 2. Project Structure Overview

-   **`llm-eval-platform/`**
    -   **`backend/`**: Contains the FastAPI (Python) backend application.
        -   `app/`: Core application code (API endpoints, services, CRUD operations, DB models, schemas).
        -   `alembic/`: Database migration scripts.
        -   `.env`: Local environment variables (copied from `.env.example` in the project root and placed here).
    -   **`frontend/`**: Contains the React (TypeScript) frontend application.
        -   `src/`: Core application code (components, pages, store, API client).
    -   **`docs/`**: Project documentation (this and other guides).
    -   `.env.example`: Example environment variables for the entire project (primarily for backend use).
    -   `README.md`: Main project README with setup and high-level overview.

## 3. Development Workflow

1.  **Set up Environment:** Ensure both backend and frontend environments are configured as per the main `README.md`.
2.  **Run Services:** Start the backend FastAPI server and the frontend React development server.
3.  **Make Changes:**
    -   For backend changes: Modify Python files in the `backend/app/` directory.
    -   For frontend changes: Modify TypeScript/TSX files in the `frontend/src/` directory.
4.  **Test:**
    -   The backend uses FastAPI's automatic Swagger/OpenAPI documentation (usually at `http://localhost:8000/docs`) which can be used for manual API testing.
    -   The frontend development server typically provides Hot Module Replacement (HMR) for instant updates in the browser.
    -   Implement unit and integration tests as appropriate (see Testing section below).
5.  **Linting & Formatting:** Adhere to project linting and formatting standards. For Python, this generally means PEP 8 (tools like Flake8, Black might be used). For TypeScript/React, ESLint and Prettier are common.
6.  **Commit & Push:** Follow good Git practices for commits.

## 4. Key Areas for Extension

### 4.1. Adding New LLM Models

-   **Backend:**
    -   If new models are supported by OpenRouter, they can often be used directly by specifying their OpenRouter model ID string.
    -   To make them appear in the default list of available models, update the `AVAILABLE_MODELS` list in `backend/app/api/endpoints/tasks.py` or, preferably, configure them via the `OPENROUTER_AVAILABLE_MODELS` environment variable (see Backend Configuration guide).
-   **Frontend:**
    -   The frontend dynamically fetches the list of available models from the backend's `/tasks/models` endpoint. No frontend code changes are typically needed if the backend serves the new model IDs.

### 4.2. Modifying Evaluation Rubrics/Prompts

-   The core evaluation prompt sent to judge LLMs is constructed in `backend/app/services/llm_service.py` within the `get_single_ranking` function.
-   Changes to the structure of the evaluation (e.g., asking for different metrics, different JSON output) would require modifications to this prompt and potentially to the Pydantic schemas (`schemas/task.py`) and database models (`db/models.py`) if new data fields need to be stored.
-   Frontend components like `ReportViewer.tsx` would also need updates to display any new evaluation data.

### 4.3. Adding New API Endpoints

-   **Backend:**
    1.  Define new Pydantic schemas in `schemas/task.py` (or a new schema file if substantially different) for request/response bodies.
    2.  Create new SQLAlchemy models in `db/models.py` if new database tables are needed.
    3.  Write new CRUD functions in the relevant `crud/` module if database interaction is required.
    4.  Implement the core business logic in `services/llm_service.py` or a new service file.
    5.  Define the new endpoint using APIRouter in `api/endpoints/tasks.py` (or a new endpoint file, which then needs to be included in `api/api.py`).
-   **Frontend:**
    1.  Add new interface definitions and API call functions in `api/apiClient.ts`.
    2.  Update the Zustand store (`store/taskStore.ts`) with new state variables and actions if needed.
    3.  Create or modify React components to consume and display data from the new endpoint.

### 4.4. UI/UX Enhancements

-   Changes to existing components are made in their respective files within `frontend/src/components/` or `frontend/src/pages/`.
-   Styling is primarily handled by Tailwind CSS utility classes directly in the TSX files.

## 5. Database Migrations (Backend)

-   If you change database models (`db/models.py` – e.g., add a new table or column), you will need to create a new database migration script using Alembic.
-   Refer to `llm-eval-platform/backend/alembic/README.md` for instructions on how to generate and apply migrations.
-   **Important:** Do not enable `Base.metadata.create_all()` in `app.main.py` for production or if you are using Alembic migrations, as it can conflict with Alembic's version control of the schema.

## 6. State Management (Frontend)

-   The frontend uses Zustand for global state management (`store/taskStore.ts`).
-   When adding features that require shared state or complex asynchronous logic (especially related to API calls or SSE), integrate them into this store.
-   The store is organized into state variables and actions. Actions can be asynchronous and often call functions from `apiClient.ts`.

## 7. Testing (Conceptual)

While specific testing setup details were not fully elaborated in the provided files beyond CRA defaults for the frontend, here are general guidelines:

-   **Backend (Python/FastAPI):**
    -   **Unit Tests:** Use `pytest` to test individual functions, especially in `crud/` and `services/`.
    -   **Integration Tests:** Test API endpoints using FastAPI's `TestClient` or an async equivalent. Mock external services like OpenRouter where appropriate.
-   **Frontend (React/TypeScript):**
    -   **Unit/Component Tests:** Use Jest (comes with Create React App) and React Testing Library to test individual components and hooks. Focus on user interactions and component behavior rather than implementation details.
    -   **Integration Tests:** Test interactions between multiple components or components and the Zustand store.
    -   **End-to-End (E2E) Tests:** Consider tools like Playwright or Cypress for testing full user flows through the application.

## 8. Coding Standards & Best Practices

-   **Python (Backend):**
    -   Follow PEP 8 style guidelines.
    -   Use type hints consistently.
    -   Write clear, modular, and well-documented code.
    -   Ensure proper error handling and logging.
-   **TypeScript/React (Frontend):**
    -   Follow standard React best practices (e.g., component composition, hook usage, unidirectional data flow).
    -   Use TypeScript effectively for strong typing.
    -   Maintain a clean and organized component structure.
    -   Write readable and maintainable code.

## 9. Dependencies

-   **Backend:** Managed by Poetry (`pyproject.toml` and `poetry.lock`). Use `poetry add <package>` to add new dependencies.
-   **Frontend:** Managed by npm or yarn (`package.json` and `package-lock.json`/`yarn.lock`). Use `npm install <package>` or `yarn add <package>`.

Regularly update dependencies and audit them for security vulnerabilities. 