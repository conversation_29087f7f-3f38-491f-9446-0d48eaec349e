**AI辅助软件开发工具评估项目 - 周进度报告 (Week 5 Day 1)**

**1. 项目回顾与当前阶段概述**

* 项目概览文档: [AI Development Team](https://wiki.slb.com/spaces/WLWAutomation/pages/447879566/AI+Development+Team)

* **项目启动与调整回顾**: 本项目于2025年4月启动，已平稳运行4周，当前正式进入第5周的评估执行阶段。在Week 3 (May 28)，项目目标经过关键调整，当前**核心聚焦于对“开箱即用型AI编程工具”在软件开发全生命周期中应用效能的系统性评估**。对不同底层模型的直接比较将作为项目的一个较小部分。
* **前期已完成的工作**:
    * 定义了一个关于AI工具的调查问卷，需要要求更多人来填写，来收集更多关于AI工具的体验和使用情况。
    * 实现了LLM Eval Platform，原计划用于评估LLM以及不同的Prompt的性能，但由于项目目标的调整，可以使用这个项目作为codebase，来评估AI工具的性能。
    * 为确保AI工具评估的独立性和环境一致性，已成功在本地rancher desktop中部署并配置了各工具专用的PostgreSQL数据库实例 (x8+)，部署并配置了本地Docker Registry和Github Runner，用于CI/CD Pipeline的构建和测试。

**2. 核心评估任务规划与SDLC覆盖**

为了系统性地评估AI辅助编程工具在软件开发全生命周期（SDLC）中的效能，我们将评估任务与SDLC的各个关键阶段对应起来。下表概述了主要的评估任务及其所覆盖的SDLC阶段：

| SDLC 阶段                     | 任务ID | 核心评估内容                                         |
| :---------------------------- | :----- | :--------------------------------------------------- |
| **需求分析 (Requirements Engineering)** | 1.d    | 基于高层级想法进行需求细化与分析                       |
| **设计 (Design)** | 1.a    | AI主导的API设计、数据库结构设计 (通过高层级Prompt)         |
| **开发 (Development)** | 1.a    | 全功能代码生成 (后端、前端、数据库迁移)                  |
|                               | 1.c    | 为新功能编写开发者文档和用户说明                       |
|                               | 2      | 代码重构、问题定位与修复建议                           |
| **测试 (Testing)** | 1.b    | 为已有代码生成单元测试 (及探索集成/E2E测试场景)        |
| **部署 (Deployment) / 运维 (Operations)** | 4      | CI/CD Pipeline基础配置的辅助生成与优化建议             |
| **维护 (Maintenance)** | 2      | 代码重构、问题定位                                   |
|                               | 3      | 项目整体文档的补充、审查与更新                         |
| **文档化 (Documentation)** | 1.c    | 功能模块的开发者文档和用户说明                         |
|                               | 3      | 项目级文档（架构、核心模块）的补充与审查               |

基于项目目标和对AI工具能力的考量，我设计了一系列评估任务。我认为过于简单的任务（如“Coding a new backend service”, “Coding a single page app frontend”, “Coding a simple backend-frontend app”）可能因当前AI TOOLS大多使用相同的底层模型导致输出相似而难以区分工具性能，而过于复杂的任务若非一次性Prompt完成，则迭代过程中引入的人工干预会使工具间比较失准。

因此，我首先定义了一个**核心编码挑战 (Task 1)：为现有LLM评估平台添加“Usage Statistics”功能。**
* **任务描述**: 完整实现使用统计功能，跟踪AI模型在“generations”和“evaluations”中的token使用量、成本和其他指标。技术栈为FastAPI + React + PostgreSQL + Alembic。
* **选择理由**: 这是一个我非常熟悉、复杂度适中、且能全面考察AI工具全栈开发能力的任务。通过将代码库和数据库回退至此功能实现前的状态，并在独立分支和数据库实例上进行测试，可以确保评估的公平性和准确性。

**Task 1 将分解为以下子任务，分别对应SDLC的不同阶段：**

* **子任务 1.a: 高层级Prompt驱动的全功能代码与迁移生成**
    * **SDLC阶段评估**: **设计 (Design)** 与 **开发 (Development)**
    * **描述**: 此子任务的目标是使用一个高层次的Prompt，评估AI工具一次性生成所有必要的后端代码（FastAPI模型、Pydantic Schema、服务逻辑、CRUD操作）、前端代码（TypeScript接口、React组件、页面逻辑）以及一个统一的数据库迁移脚本（Alembic）的能力。具体的实现细节、API设计和数据库表结构将由AI主导决定。
    * **评估方法**: 依据预设的四档评估标准（优秀、良好、基础、需要改进），通过人工进行端到端的功能测试，审查代码质量（正确性、可读性、可维护性），并严格验证数据库迁移脚本的`upgrade`和`downgrade`功能。
    * **当前进展 (Week 5 Day 1)**:
        * 已完成对 Github Copilot, Cursor (Claude 4 & Claude 4 MAX MODE & Gemini Pro 2.5 0506), Roo Code, Augment Code, Windsurf, Cline等6个工具（8种模型工具组合）的评估。
        * **剩余4个工具 (Codex, Claude Code) 的Task 1.a评估正在进行中，计划本周内完成。**

* **子任务 1.b: 为已有代码编写测试用例 (Writing unit tests for existing code)**
    * **SDLC阶段评估**: **测试 (Testing)**
    * **描述**: 在Task 1.a完成后，基于一个统一的基准代码版本，要求AI工具为已实现的“Usage Statistics”功能增加测试代码，主要侧重于单元测试，同时探索AI生成集成测试或描述E2E测试场景的能力。
    * **公平性**: 为保证公平，执行此任务前，所有工具分支的代码库将同步到相同的、已实现功能的版本。

* **子任务 1.c: 为已有功能编写文档**
    * **SDLC阶段评估**: **文档化 (Documentation)** – 此能力对开发和维护阶段均至关重要。
    * **描述**: 要求AI工具为Task 1.a中实现的功能模块生成必要的开发者文档（如代码注释、API文档、 ChangeLog）和简要的用户使用说明。

* **子任务 1.d: 基于高层级想法进行需求细化与分析**
    * **SDLC阶段评估**: **需求分析 (Requirements Engineering)**
    * **描述**: 当前我们对“Usage Statistics”功能只有一个高层次的想法。此子任务旨在探索能否利用LLM结合AI IDE工具，将这个高层级想法转化为更详细、结构化的需求文档（例如用户故事、验收标准、非功能性需求等）。评估不同工具生成需求文档的能力。以及进一步评估有了这份详细的需求文档后，是否能够帮助AI工具（或开发者）更好地进行后续的设计与实现。

**Task 1 (包含所有子任务 a,b,c,d) 的原计划是在Week 5完成对所有不同AI工具的测试。为确保评估质量和深度，若时间资源出现紧张，将优先保障对核心工具列表 (Copilot, Cursor, Augment Code, Roo Code, Claude Code) 的全面测试。**

**3. 后续评估任务规划 (预计Week 6开始，针对工具子集)：**

* **评估任务2: 代码重构与问题定位 (Refactor the existing codebase)**
    * **SDLC阶段评估**: **维护 (Maintenance)** 及 **开发 (Development)** 中的调试与优化。
    * **描述**: 项目中存在一个已知问题：“多个模型同时生成时，会卡住后端服务对其他请求的响应”。利用此BUG，评估AI工具在理解现有代码库（上下文感知能力）、定位问题根源以及提出修复方案方面的能力。

* **评估任务3: 项目整体文档的补充与审查**
    * **SDLC阶段评估**: 贯穿项目周期的 **文档化 (Documentation)** 及 **维护 (Maintenance)**。
    * **描述**: 评估AI工具为整个LLM评估平台项目（而不仅仅是Task 1的功能）补充高级别文档（如架构概览、核心模块说明）以及检查和更新现有项目文档（识别过时或不一致内容）的能力。

* **评估任务4: CI/CD Pipeline的构建辅助**
    * **SDLC阶段评估**: **部署 (Deployment)** 及 **运维 (Operations)** 的初步支持。
    * **描述**: 我之前已使用Cursor初步实现了一个可运行的CI/CD pipeline，满足基本需求但存在一些小问题。由于我在此领域非专家，迭代过程较为曲折且难以标准化记录作为普适的评估任务。本次评估将尝试使用单个（或少量）高层级Prompt，考察AI工具能否生成CI/CD pipeline的基础配置（如GitHub Actions的workflow文件、Dockerfile优化建议、Alembic迁移命令等）。重点评估其辅助生成配置片段的正确性、对相关工具命令的理解和解释能力。

**4. 评估标准 (Criteria) 的持续完善与新增：**

* **现有核心标准**: Task 1.a的四档定性评估（优秀、良好、基础、需要改进），结合人工E2E测试。
* **计划新增/细化的量化与非量化指标 (预计Week 6完成数据收集和整理框架)**:
    * **工具调用成功率**: 特别针对代码编辑、代码解释等交互操作，记录不同AI工具在使用不同底层模型（如适用）时的调用成功情况。
    * **Checklist类指标**: 系统记录各工具的Customization选项（如模型切换、prompt工程支持）、IDE Integration特性（如VS Code插件的快捷键、tab自动跳转）、License模式与成本、User Experience（结合个人使用和实习生问卷反馈）、是否支持本地模型等。
    * **具体衡量KPIs**: 将围绕“Proof of correctness vs time to develop” 和 “Proof of correctness vs cost to develop” 展开，其中“time to develop”会包含所有必要的人工交互和修正时间。

**5. 当前工作重点 (Week 5 Day 1)**

* **首要**: 完成对Codex和Claude Code在Task 1.a上的评估，从而对所有目标工具在核心代码生成能力上形成初步的横向对比。
* **其次**: 根据优先级工具列表，并结合Task 1.a的初步结果，具体规划和启动Task 1.b, 1.c, 1.d的评估工作。明确每个子任务对选定工具的测试深度和具体操作步骤。

**6. 时间规划概览 (原计划，将根据优先级策略灵活调整)：**

* **Week 5-6 (6.4 - 6.18)**: 集中完成Task 1 (a,b,c,d) 对概览文档中列出的工具的深度评估，并启动Tasks 2, 3, 4对选定工具子集的评估。
* **Week 7-8 (6.19 - 7.2)**: 根据首轮评估的详细结果和AI工具的最新发展（如有重大更新），进行针对性的补充测试、深化分析或对特定工具的亮点功能进行探索。
* **Week 9-10 (7.3 - 7.16)**: 数据汇总、深度分析、撰写评估报告、准备最终演示。
* **(如果公司需要，可延长实习至与其他实习生同时结束8月9日)**

**7. 当前的疑问？**
1. 项目要求中提出结合人工评估与LLM AS JUDGE，但是如果用LLM AS JUDGE，需要把大量代码上下文（甚至是整个项目代码）提供给LLM，这会导致API调用成本过高。

2. 目前的任务执行过程中，以人工评估为主。如何解决这一问题？提前写测试用例。但是测试用例的编写要与代码的实际实现相匹配，如果把测试用例提供给AI工具，让AI工具根据测试用例写代码，是一种好的思路吗？如果不把测试用例提供给AI，AI生成的API端点不一定与测试用例相匹配，如何解决这一问题？例如任务1.a，我们只有高层级的想法，AI可能会生成不同的实现。
