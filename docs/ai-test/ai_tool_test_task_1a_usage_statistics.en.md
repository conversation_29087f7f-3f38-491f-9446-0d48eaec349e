# AI Tool Test Task: Usage Statistics Feature Implementation

**Task Nature**: Complex Full-Stack Feature Development Coding Challenge
**Tech Stack**: FastAPI + React + PostgreSQL + Alembic
**Difficulty Level**: ⭐⭐⭐⭐⭐ (Advanced)
**Purpose**: To serve as a benchmark for evaluating the capabilities of AI tools in complex software development tasks, particularly focusing on code generation, system understanding, API integration, database operations, and solving full-stack problems. This task originates from a broader project aimed at exploring and evaluating the application potential of AI throughout the software development lifecycle.

---
## 📄 Project Background and Vision (Excerpt)

This project aims to research and deploy AI for developing and validating programs, exploring how to leverage AI to improve various aspects of software development. The goal is to enable AI to iterate rapidly towards workable solutions and to assess AI's effectiveness in design, construction, testing, documentation, etc. This "Usage Statistics Feature Implementation" task is one of the specific coding challenges set under this project, used to measure the performance of different AI tools in actual development scenarios.

---

## 📋 Task Description

**Functional Requirement**: Add a complete usage statistics feature to the LLM evaluation platform, tracking token usage, cost, and other metrics for AI models in **generations** and **evaluations**.

**Reference Document**: [OpenRouter Usage Accounting](https://openrouter.ai/docs/usage-accounting)

**Core Requirements**:
1. Backend integration of OpenRouter usage tracking (generations + evaluations)
2. Database storage of usage statistics (both tables required)
3. Frontend display of usage statistics (individual and aggregated)
4. Support for usage statistics in evaluations
5. Creation of a complete database migration chain (AI should attempt to create all fields and types correctly in one go)

---

## 🎯 Test Objectives as a Coding Challenge

This coding challenge aims to test the capabilities of AI tools in the following aspects:
- **Overall Planning and Understanding**: Can the AI understand the entire task requirement and plan a feasible full-stack implementation solution?
- **API Integration Capability**: Accuracy and efficiency of AI in integrating third-party APIs (OpenRouter).
- **Database Design and One-time Migration**: Can the AI correctly design the data model and complete database changes through a single Alembic migration?
- **Full-Stack Development**: AI's ability to simultaneously handle backend APIs, frontend UI, and database schema.
- **Problem Anticipation and Resolution**: How AI handles potential dependencies and technical details when generating a large amount of code at once.
- **Code Quality and Consistency**: Correctness, readability, maintainability of generated code, and consistency between generations and evaluations modules.

These test objectives are directly related to evaluation criteria such as "code generation quality," "codebase contextual awareness," and "debugging assistance" in the broader project vision.

---

## ✅ Benchmark Implementation Process (Manual + Cursor Assistance)

The following are the detailed steps taken by a human developer (Rui Tao) with Cursor assistance to complete this task. This process can serve as a benchmark reference for AI tool performance. **It is worth noting that the database migration in the benchmark implementation was done in three steps because the type of `cost_credits` returned by OpenRouter was initially uncertain, and due to the process of gradually adding features. Ideally, an AI tool should be able to correctly add all database fields in one go through more comprehensive analysis.**

### Phase 1: Backend Data Model Expansion

#### 1. **Generation Model Field Addition**
```python
# backend/app/db/models/generation.py
class Generation(Base):
    # ... existing fields ...
    
    # Usage statistics fields
    prompt_tokens = Column(Integer, nullable=True)
    completion_tokens = Column(Integer, nullable=True) 
    total_tokens = Column(Integer, nullable=True)
    reasoning_tokens = Column(Integer, nullable=True)
    cached_tokens = Column(Integer, nullable=True)
    cost_credits = Column(Float, nullable=True)  # Note: Initially Integer, later corrected to Float
    generation_id = Column(String, nullable=True)
```

#### 2. **Ranking Model Field Addition**
```python
# backend/app/db/models/ranking.py
class Ranking(Base):
    # ... existing fields ...
    
    # Usage statistics fields for evaluations
    prompt_tokens = Column(Integer, nullable=True)
    completion_tokens = Column(Integer, nullable=True)
    total_tokens = Column(Integer, nullable=True)
    reasoning_tokens = Column(Integer, nullable=True)
    cached_tokens = Column(Integer, nullable=True)
    cost_credits = Column(Float, nullable=True)
    generation_id = Column(String, nullable=True)
```

#### 3. **Pydantic Schema Update**
```python
# backend/app/schemas/generation.py
class GenerationBase(BaseModel):
    # ... existing fields ...
    prompt_tokens: Optional[int] = None
    completion_tokens: Optional[int] = None
    total_tokens: Optional[int] = None
    reasoning_tokens: Optional[int] = None
    cached_tokens: Optional[int] = None
    cost_credits: Optional[float] = None
    generation_id: Optional[str] = None

# backend/app/schemas/ranking.py 
class RankingBase(BaseModel):
    # ... existing fields ...
    prompt_tokens: Optional[int] = None
    completion_tokens: Optional[int] = None
    total_tokens: Optional[int] = None
    reasoning_tokens: Optional[int] = None
    cached_tokens: Optional[int] = None
    cost_credits: Optional[float] = None
    generation_id: Optional[str] = None
```

#### 4. **LLM Service Modification**
```python
# backend/app/services/llm_service.py
async def generate_response(self, messages: List[Dict], model: str = None):
    try:
        response = await self.client.chat.completions.create(
            model=model or self.default_model,
            messages=messages,
            extra_body={
                "usage": {"include": True}  # Enable OpenRouter usage tracking
            }
        )
        
        # Extract usage statistics (common function)
        usage_stats = self._extract_usage_stats(response)
        return response, usage_stats

def _extract_usage_stats(self, response):
    """Common usage statistics extraction function"""
    usage_stats = {}
    if hasattr(response, 'usage') and response.usage:
        usage_stats = {
            'prompt_tokens': getattr(response.usage, 'prompt_tokens', None),
            'completion_tokens': getattr(response.usage, 'completion_tokens', None),
            'total_tokens': getattr(response.usage, 'total_tokens', None),
            'reasoning_tokens': getattr(response.usage, 'reasoning_tokens', None),
            'cached_tokens': getattr(response.usage, 'cached_tokens', None),
            'cost_credits': getattr(response.usage, 'cost_credits', None),
        }
    
    # Extract generation_id
    if hasattr(response, 'id'):
        usage_stats['generation_id'] = response.id
    
    return usage_stats
```

#### 5. **Evaluation Service Update**
```python
# backend/app/services/evaluation_service.py
async def evaluate_generations(self, task_id: int, evaluation_prompt: str):
    # ... existing evaluation logic ...
    
    # Call LLM for evaluation
    evaluation_response, usage_stats = await self.llm_service.generate_response(
        messages=[{"role": "user", "content": evaluation_prompt}]
    )
    
    # Create ranking record including usage statistics
    ranking_data = {
        "task_id": task_id,
        "evaluation_result": evaluation_response.choices[0].message.content,
        **usage_stats  # Expand usage statistics data
    }
    
    ranking = await self.ranking_crud.create(ranking_data)
    return ranking
```

### Phase 2: Frontend Interface Implementation

#### 1. **TypeScript Interface Definition**
```typescript
// frontend/src/types/generation.ts
interface Generation {
  // ... existing fields ...
  prompt_tokens?: number;
  completion_tokens?: number;
  total_tokens?: number;
  reasoning_tokens?: number;
  cached_tokens?: number;
  cost_credits?: number;
  generation_id?: string;
}

// frontend/src/types/ranking.ts 
interface Ranking {
  // ... existing fields ...
  prompt_tokens?: number;
  completion_tokens?: number;
  total_tokens?: number;
  reasoning_tokens?: number;
  cached_tokens?: number;
  cost_credits?: number;
  generation_id?: string;
}
```

#### 2. **Common Usage Statistics Component**
```typescript
// frontend/src/components/UsageStatistics.tsx
interface UsageStatsProps {
  data: {
    prompt_tokens?: number;
    completion_tokens?: number;
    total_tokens?: number;
    reasoning_tokens?: number;
    cached_tokens?: number;
    cost_credits?: number;
    generation_id?: string;
  };
  title?: string;
}

const UsageStatistics = ({ data, title = "Usage Statistics" }: UsageStatsProps) => {
  return (
    <div className="usage-statistics">
      <h4>{title}</h4>
      <div className="grid grid-cols-2 gap-4">
        {data.prompt_tokens != null && (
          <div>Prompt Tokens: {data.prompt_tokens}</div>
        )}
        {data.completion_tokens != null && (
          <div>Completion Tokens: {data.completion_tokens}</div>
        )}
        {data.total_tokens != null && (
          <div>Total Tokens: {data.total_tokens}</div>
        )}
        {data.reasoning_tokens != null && (
          <div>Reasoning Tokens: {data.reasoning_tokens}</div>
        )}
        {data.cached_tokens != null && (
          <div>Cached Tokens: {data.cached_tokens}</div>
        )}
        {data.cost_credits != null && (
          <div>Cost: {data.cost_credits.toFixed(6)} credits</div>
        )}
        {data.generation_id != null && (
          <div>Generation ID: {data.generation_id}</div>
        )}
      </div>
    </div>
  );
};
```

#### 3. **Generation Display Component Update**
```typescript
// frontend/src/components/SingleOutputDisplay.tsx
const SingleOutputDisplay = ({ generation }: { generation: Generation }) => {
  return (
    <div>
      {/* ... existing content ... */}
      <UsageStatistics data={generation} title="Generation Usage" />
    </div>
  );
};
```

#### 4. **Evaluation Display Component**
```typescript
// frontend/src/components/EvaluationDisplay.tsx
const EvaluationDisplay = ({ ranking }: { ranking: Ranking }) => {
  return (
    <div>
      <h3>Evaluation Result</h3>
      <p>{ranking.evaluation_result}</p>
      <UsageStatistics data={ranking} title="Evaluation Usage" />
    </div>
  );
};
```

#### 5. **Aggregated Statistics Display**
```typescript
// frontend/src/pages/ViewTaskPage.tsx
const aggregatedUsage = useMemo(() => {
  // Aggregate usage of generations
  const generationUsage = generations.reduce((acc, gen) => ({
    totalPromptTokens: acc.totalPromptTokens + (gen.prompt_tokens || 0),
    totalCompletionTokens: acc.totalCompletionTokens + (gen.completion_tokens || 0),
    totalCost: acc.totalCost + (gen.cost_credits || 0),
    // ... other aggregations
  }), { totalPromptTokens: 0, totalCompletionTokens: 0, totalCost: 0 }); // Added initialValues

  // Aggregate usage of evaluations
  const evaluationUsage = rankings.reduce((acc, ranking) => ({
    totalPromptTokens: acc.totalPromptTokens + (ranking.prompt_tokens || 0),
    totalCompletionTokens: acc.totalCompletionTokens + (ranking.completion_tokens || 0),
    totalCost: acc.totalCost + (ranking.cost_credits || 0),
    // ... other aggregations
  }), { totalPromptTokens: 0, totalCompletionTokens: 0, totalCost: 0 }); // Added initialValues

  // Total
  return {
    generations: generationUsage,
    evaluations: evaluationUsage,
    total: {
      totalPromptTokens: generationUsage.totalPromptTokens + evaluationUsage.totalPromptTokens,
      totalCompletionTokens: generationUsage.totalCompletionTokens + evaluationUsage.totalCompletionTokens,
      totalCost: generationUsage.totalCost + evaluationUsage.totalCost,
    }
  };
}, [generations, rankings]);
```

### Phase 3: Database Migration (Benchmark Implementation - In Three Steps)

#### 1. **Create Generations Usage Migration**
```bash
# Create the first migration file
poetry run alembic revision --autogenerate -m "Add usage statistics to generations table"
```

```python
# backend/alembic/versions/a1b2c3d4e5f6_add_usage_statistics_to_generations.py
# (Filename and ID are examples, actual ID will differ)
import sqlalchemy as sa
from alembic import op

def upgrade():
    op.add_column('generations', sa.Column('prompt_tokens', sa.Integer(), nullable=True))
    op.add_column('generations', sa.Column('completion_tokens', sa.Integer(), nullable=True))
    op.add_column('generations', sa.Column('total_tokens', sa.Integer(), nullable=True))
    op.add_column('generations', sa.Column('reasoning_tokens', sa.Integer(), nullable=True))
    op.add_column('generations', sa.Column('cached_tokens', sa.Integer(), nullable=True))
    op.add_column('generations', sa.Column('cost_credits', sa.Integer(), nullable=True))  # Note: Initially Integer
    op.add_column('generations', sa.Column('generation_id', sa.String(), nullable=True))

def downgrade():
    op.drop_column('generations', 'generation_id')
    op.drop_column('generations', 'cost_credits')
    op.drop_column('generations', 'cached_tokens')
    op.drop_column('generations', 'reasoning_tokens')
    op.drop_column('generations', 'total_tokens')
    op.drop_column('generations', 'completion_tokens')
    op.drop_column('generations', 'prompt_tokens')
```

#### 2. **Correct Data Type Migration**
```bash
# Create type correction migration
poetry run alembic revision --autogenerate -m "Change cost_credits column to Float for generations"
# Migration ID example: b2c3d4e5f6a7 (actual ID will differ)
```

```python
# backend/alembic/versions/b2c3d4e5f6a7_change_cost_credits_to_float_for_generations.py
# (Filename and ID are examples, actual ID will differ)
import sqlalchemy as sa
from alembic import op

def upgrade():
    op.alter_column('generations', 'cost_credits',
                    existing_type=sa.INTEGER(),
                    type_=sa.Float(),
                    existing_nullable=True)

def downgrade():
    op.alter_column('generations', 'cost_credits',
                    existing_type=sa.Float(),
                    type_=sa.INTEGER(),
                    existing_nullable=True)
```

#### 3. **Create Rankings Usage Migration**
```bash
# Create rankings usage statistics migration
poetry run alembic revision --autogenerate -m "Add usage statistics to rankings table"
# Migration ID example: d70dbb5a6738 (actual ID will differ)
```

```python
# backend/alembic/versions/d70dbb5a6738_add_usage_statistics_to_rankings_table.py
# (Filename and ID are examples, actual ID will differ)
import sqlalchemy as sa
from alembic import op

def upgrade():
    op.add_column('rankings', sa.Column('prompt_tokens', sa.Integer(), nullable=True))
    op.add_column('rankings', sa.Column('completion_tokens', sa.Integer(), nullable=True))
    op.add_column('rankings', sa.Column('total_tokens', sa.Integer(), nullable=True))
    op.add_column('rankings', sa.Column('reasoning_tokens', sa.Integer(), nullable=True))
    op.add_column('rankings', sa.Column('cached_tokens', sa.Integer(), nullable=True))
    op.add_column('rankings', sa.Column('cost_credits', sa.Float(), nullable=True))  # Use Float type directly
    op.add_column('rankings', sa.Column('generation_id', sa.String(), nullable=True))

def downgrade():
    op.drop_column('rankings', 'generation_id')
    op.drop_column('rankings', 'cost_credits')
    op.drop_column('rankings', 'cached_tokens')
    op.drop_column('rankings', 'reasoning_tokens')
    op.drop_column('rankings', 'total_tokens')
    op.drop_column('rankings', 'completion_tokens')
    op.drop_column('rankings', 'prompt_tokens')
```

---

## 🐛 Problems Encountered and Solutions in Benchmark Implementation

### Problem 1: Migration Execution Failure
- **Symptom**: `python -m alembic upgrade head` failed
- **Reason**: Poetry environment configuration issue
- **Solution**: Use `poetry run alembic upgrade head`

### Problem 2: Field Display Anomaly
- **Symptom**: 
  - Backend log shows `cost: 0.0001335`
  - Frontend displays `"000"`
  - `cached_tokens`, `generation_id` not displayed

### Problem 3: Data Type Mismatch (Caused benchmark migration to be split)
- **Root Cause**: Initially, the `cost_credits` field in the `generations` table was defined as `Integer`, but OpenRouter actually returns a float. This necessitated an additional migration to correct the type.
- **Solution**: 
  1. Modify model definition to `Float`
  2. Create new migration file to correct the type
  3. Update frontend type definition

### Problem 4: Frontend Display Logic Error
- **Reason**: Used truthy check instead of explicit null check
- **Correction**:
```typescript
// Before
{generation.cost_credits && <div>Cost: {generation.cost_credits}</div>}

// After  
{generation.cost_credits != null && (
  <div>Cost: {generation.cost_credits.toFixed(6)} credits</div>
)}
```

### Problem 5: Duplicate Code Issue**
- **Symptom**: Usage display code for Generations and Rankings was duplicated
- **Solution**: Create a common UsageStatistics component to avoid code duplication

### Problem 6: Aggregated Statistics Omission**
- **Symptom**: Only usage for generations was tallied, evaluations were missed
- **Solution**: Calculate usage for both types separately and provide a total
---
### Problem 7: `useMemo` `reduce` missing `initialValues` (Benchmark implementation detail supplement)
- **Symptom**: In `ViewTaskPage.tsx`, if the `generations` or `rankings` array is empty, the `reduce` function wrapped in `useMemo` will cause a runtime error because `reduce` on an empty array without an initial value will throw an error.
- **Solution**: Provide an explicit initial value object for the `reduce` function, e.g., `{ totalPromptTokens: 0, totalCompletionTokens: 0, totalCost: 0 }`.
---

## 📊 Final Implementation Effect (Benchmark)

### Database Schema
```sql
-- New fields for generations table
ALTER TABLE generations ADD COLUMN prompt_tokens INTEGER;
ALTER TABLE generations ADD COLUMN completion_tokens INTEGER;
ALTER TABLE generations ADD COLUMN total_tokens INTEGER;
ALTER TABLE generations ADD COLUMN reasoning_tokens INTEGER;
ALTER TABLE generations ADD COLUMN cached_tokens INTEGER;
ALTER TABLE generations ADD COLUMN cost_credits FLOAT;
ALTER TABLE generations ADD COLUMN generation_id VARCHAR;

-- New fields for rankings table
ALTER TABLE rankings ADD COLUMN prompt_tokens INTEGER;
ALTER TABLE rankings ADD COLUMN completion_tokens INTEGER;
ALTER TABLE rankings ADD COLUMN total_tokens INTEGER;
ALTER TABLE rankings ADD COLUMN reasoning_tokens INTEGER;
ALTER TABLE rankings ADD COLUMN cached_tokens INTEGER;
ALTER TABLE rankings ADD COLUMN cost_credits FLOAT;
ALTER TABLE rankings ADD COLUMN generation_id VARCHAR;
```

### Frontend Display Effect
```
Generation Usage Statistics
├── Prompt Tokens: 150
├── Completion Tokens: 75  
├── Total Tokens: 225
├── Cost: 0.000134 credits
└── Generation ID: openrouter_gen_xyz

Evaluation Usage Statistics 
├── Prompt Tokens: 200
├── Completion Tokens: 50
├── Total Tokens: 250
├── Cost: 0.000089 credits
└── Generation ID: openrouter_eval_abc

Aggregated Usage Summary
├── Generations Total: 0.001245 credits (8 generations)
├── Evaluations Total: 0.000234 credits (3 evaluations)
└── Task Total Cost: 0.001479 credits
```

---
## 🧪 AI Implementation and Test Protocol (Single Generation Attempt)

This section provides a high-level AI prompt designed to have the AI tool generate all necessary code for the "Usage Statistics Feature Implementation" coding challenge in one go. This is followed by detailed validation test cases to comprehensively evaluate the correctness and completeness of the AI-generated code.

**🎯 Objective**: Based on the following single prompt, the AI tool generates all necessary backend and frontend code modifications, as well as a unified database migration script.

**🤖 AI Prompt (Single Generation)**:
```prompt_text
    Task: Implement Usage Statistics feature for LLM evaluation platform, tracking token usage and cost from OpenRouter API.

    Requirements:
    1. Add usage statistics fields to generations and rankings tables
    2. Modify OpenRouter API calls to fetch usage data
    3. Display individual and aggregated usage statistics on the frontend
    4. Create a single database migration to add all necessary fields

    Reference:---
    title: Usage Accounting
    headline: Usage Accounting | Track AI Model Usage with OpenRouter
    canonical-url: 'https://openrouter.ai/docs/use-cases/usage-accounting'
    'og:site_name': OpenRouter Documentation
    'og:title': Usage Accounting - Track AI Model Token Usage
    'og:description': >-
      Learn how to track AI model usage including prompt tokens, completion tokens,
      and cached tokens without additional API calls.
    'og:image':
      type: url
      value: >-
        https://openrouter.ai/dynamic-og?title=Usage%20Accounting&description=Track%20AI%20model%20token%20usage%20with%20OpenRouter
    'og:image:width': 1200
    'og:image:height': 630
    'twitter:card': summary_large_image
    'twitter:site': '@OpenRouterAI'
    noindex: false
    nofollow: false
    ---

    import { API_KEY_REF, Model } from '../../../imports/constants';

    The OpenRouter API provides built-in **Usage Accounting** that allows you to track AI model usage without making additional API calls. This feature provides detailed information about token counts, costs, and caching status directly in your API responses.

    ## Usage Information

    When enabled, the API will return detailed usage information including:

    1. Prompt and completion token counts using the model's native tokenizer
    2. Cost in credits
    3. Reasoning token counts (if applicable)
    4. Cached token counts (if available)

    This information is included in the last SSE message for streaming responses, or in the complete response for non-streaming requests.

    ## Enabling Usage Accounting

    You can enable usage accounting in your requests by including the `usage` parameter:

    ```json
    {
      "model": "your-model",
      "messages": [],
      "usage": {
        "include": true
      }
    }
    ```

    ## Response Format

    When usage accounting is enabled, the response will include a `usage` object with detailed token information:

    ```json
    {
      "object": "chat.completion.chunk",
      "usage": {
        "completion_tokens": 2,
        "completion_tokens_details": {
          "reasoning_tokens": 0
        },
        "cost": 197,
        "prompt_tokens": 194,
        "prompt_tokens_details": {
          "cached_tokens": 0
        },
        "total_tokens": 196
      }
    }
    ```

    <Note title='Performance Impact'>
      Enabling usage accounting will add a few hundred milliseconds to the last
      response as the API calculates token counts and costs. This only affects the
      final message and does not impact overall streaming performance.
    </Note>

    ## Benefits

    1. **Efficiency**: Get usage information without making separate API calls
    2. **Accuracy**: Token counts are calculated using the model's native tokenizer
    3. **Transparency**: Track costs and cached token usage in real-time
    4. **Detailed Breakdown**: Separate counts for prompt, completion, reasoning, and cached tokens

    ## Best Practices

    1. Enable usage tracking when you need to monitor token consumption or costs
    2. Account for the slight delay in the final response when usage accounting is enabled
    3. Consider implementing usage tracking in development to optimize token usage before production
    4. Use the cached token information to optimize your application's performance

    ## Alternative: Getting Usage via Generation ID

    You can also retrieve usage information asynchronously by using the generation ID returned from your API calls. This is particularly useful when you want to fetch usage statistics after the completion has finished or when you need to audit historical usage.

    To use this method:

    1. Make your chat completion request as normal
    2. Note the `id` field in the response
    3. Use that ID to fetch usage information via the `/generation` endpoint

    For more details on this approach, see the [Get a Generation](/docs/api-reference/get-a-generation) documentation.

    ## Examples

    ### Basic Usage with Token Tracking

    <Template data={{
      API_KEY_REF,
      MODEL: "anthropic/claude-3-opus"
    }}>
    <CodeGroup>

    ```python Python
    import requests
    import json

    url = "https://openrouter.ai/api/v1/chat/completions"
    headers = {
        "Authorization": f"Bearer {{API_KEY_REF}}",
        "Content-Type": "application/json"
    }
    payload = {
        "model": "{{MODEL}}",
        "messages": [
            {"role": "user", "content": "What is the capital of France?"}
        ],
        "usage": {
            "include": True
        }
    }

    response = requests.post(url, headers=headers, data=json.dumps(payload))
    print("Response:", response.json()['choices'][0]['message']['content'])
    print("Usage Stats:", response.json()['usage'])
    ```

    ```typescript TypeScript
    import OpenAI from 'openai';

    const openai = new OpenAI({
      baseURL: 'https://openrouter.ai/api/v1',
      apiKey: '{{API_KEY_REF}}',
    });

    async function getResponseWithUsage() {
      const response = await openai.chat.completions.create({
        model: '{{MODEL}}',
        messages: [
          {
            role: 'user',
            content: 'What is the capital of France?',
          },
        ],
        extra_body: {
          usage: {
            include: true,
          },
        },
      });

      console.log('Response:', response.choices[0].message.content);
      console.log('Usage Stats:', response.usage);
    }

    getResponseWithUsage();
    ```

    </CodeGroup>
    </Template>

    ### Streaming with Usage Information

    This example shows how to handle usage information in streaming mode:

    <Template data={{
      API_KEY_REF,
      MODEL: "anthropic/claude-3-opus"
    }}>
    <CodeGroup>

    ```python Python
    from openai import OpenAI

    client = OpenAI(
        base_url="https://openrouter.ai/api/v1",
        api_key="{{API_KEY_REF}}",
    )

    def chat_completion_with_usage(messages):
        response = client.chat.completions.create(
            model="{{MODEL}}",
            messages=messages,
            usage={
              "include": True
            },
            stream=True
        )
        return response

    for chunk in chat_completion_with_usage([
        {"role": "user", "content": "Write a haiku about Paris."}
    ]):
        if hasattr(chunk, 'usage'):
            if hasattr(chunk.usage, 'total_tokens'):
                print(f"
    Usage Statistics:")
                print(f"Total Tokens: {chunk.usage.total_tokens}")
                print(f"Prompt Tokens: {chunk.usage.prompt_tokens}")
                print(f"Completion Tokens: {chunk.usage.completion_tokens}")
                print(f"Cost: {chunk.usage.cost} credits")
        elif chunk.choices[0].delta.content:
            print(chunk.choices[0].delta.content, end="")
    ```

    ```typescript TypeScript
    import OpenAI from 'openai';

    const openai = new OpenAI({
      baseURL: 'https://openrouter.ai/api/v1',
      apiKey: '{{API_KEY_REF}}',
    });

    async function chatCompletionWithUsage(messages) {
      const response = await openai.chat.completions.create({
        model: '{{MODEL}}',
        messages,
        usage: {
          include: true,
        },
        stream: true,
      });

      return response;
    }

    (async () => {
      for await (const chunk of chatCompletionWithUsage([
        { role: 'user', content: 'Write a haiku about Paris.' },
      ])) {
        if (chunk.usage) {
          console.log('\nUsage Statistics:');
          console.log(`Total Tokens: ${chunk.usage.total_tokens}`);
          console.log(`Prompt Tokens: ${chunk.usage.prompt_tokens}`);
          console.log(`Completion Tokens: ${chunk.usage.completion_tokens}`);
          console.log(`Cost: ${chunk.usage.cost} credits`);
        } else if (chunk.choices[0].delta.content) {
          process.stdout.write(chunk.choices[0].delta.content);
        }
      }
    })();
    ```

    </CodeGroup>
    </Template>

    Tech stack: FastAPI + React + PostgreSQL + Alembic
```

**📝 Expected Deliverables (Single Generation)**:
- Backend data model and schema updates (generations + rankings)
- Backend service layer modifications (OpenRouter integration + usage extraction)
- Backend CRUD operation updates
- Single database migration script
- Frontend TypeScript interface updates
- Frontend UI components (display usage statistics)
- Frontend aggregated statistics functionality

## 🎯 AI Tool Test Evaluation Criteria (Based on this coding challenge)

When evaluating AI tools using this coding challenge (especially for single generation attempts):

### Excellent Performance Indicators (90-100 points)
- ✅ **One-time Complete Generation**: The AI tool can generate most (or all) of the required code in a single attempt based on a high-level prompt, passing most validation test cases with minimal manual correction.
- ✅ **High Code Quality**: Generated code is functionally correct, well-structured, adheres to project conventions, and critical types like `cost_credits` are handled correctly.
- ✅ **Problem Anticipation**: The AI considers potential issues like null values and types during code generation and handles them appropriately.
- ✅ **Accurate Contextual Understanding**: The AI accurately understands the existing codebase structure and functionality, making modifications and extensions in the correct files and locations.
- ✅ **Good Completeness**: The AI considers all relevant modules (generations and evaluations, backend and frontend), achieving complete functionality.
- ✅ **Correct Migration**: The generated database migration script (single) is correct, and both `upgrade` and `downgrade` are effective.
- ✅ **Adherence to Instructions**: The AI closely follows the requirements and constraints defined in the initial prompt.

### Good Performance Indicators (70-89 points)
- ✅ Basic functionality is implemented, but may require significant manual prompting and correction to pass all test cases.
- ✅ Code quality is acceptable, but some areas may have redundancy, non-standard practices, or improper handling of critical details (like `cost_credits` type).
- ⚠️ May omit implementation of some functional points or modules.
- ⚠️ The generated migration script might have issues or be incomplete.

### Basic Performance Indicators (50-69 points)
- ✅ Understands basic task requirements, but implementation is incomplete or has many obvious errors, requiring substantial manual refactoring or supplementation.
- ⚠️ May fail to correctly generate a unified database migration, or the migration script has many errors.
- ⚠️ Frontend-backend connection or data flow might have fundamental issues.

### Needs Improvement (<50 points)
- ❌ Fails to understand task requirements or existing system architecture.
- ❌ Generated code has severe errors or fails to complete core functionality.
- ❌ Fails to generate a usable database migration.

**Metrics to Record (For single generation attempt)**:
- **First-pass Success Rate**: What percentage of validation test cases did the AI's initially generated code pass (without any manual modification)?
- **Number and Type of Manual Interventions**: The number and specific content of guidance, corrections, and prompts required to make the AI-generated code pass all test cases.
- **Time Taken**: Total time spent by the AI to generate the complete solution and subsequent manual corrections.
- **LLM Model Choice**: If the AI tool supports it, record the specific LLM model used.
- **Cost**: If paid API calls are involved, record the associated costs.
- **Code Diff**: Differences from the benchmark implementation (to analyze the AI's implementation strategy and quality).
- **Migration Script Quality**: Whether the generated single migration script is complete, correct, and if `upgrade`/`downgrade` are effective.

---

## 🔧 Test Environment Preparation (For AI Tool Testing)

### Database Baseline Version
- **Starting Version**: `28b5937d928a` (Add system_prompt to tasks table) – This is the database state before running this coding challenge.
- **Target Version**: After the AI tool completes this task, the database should contain all necessary usage statistics fields and reach the latest state via the AI-generated **single** migration.

### Multi-instance Database Environment ✅ **NEW**
To ensure fairness and independence in AI tool testing, the project has now deployed 6 independent PostgreSQL instances:

- **augment**: `postgresql-augment-service:5432/llm_eval_augment`
- **roo**: `postgresql-roo-service:5432/llm_eval_roo`  
- **cline**: `postgresql-cline-service:5432/llm_eval_cline`
- **cursor**: `postgresql-cursor-service:5432/llm_eval_cursor`
- **copilot**: `postgresql-copilot-service:5432/llm_eval_copilot`
- **windsurf**: `postgresql-windsurf-service:5432/llm_eval_windsurf`

Each instance will be downgraded to the baseline version `28b5937d928a`, ensuring all AI tools start from the same point.

### Local Docker Registry Environment ✅ **NEW**
To accelerate CI/CD processes, the project now uses a local Docker registry:

- **Registry URL**: `localhost:5000`
- **Image Naming**:
  - Backend: `localhost:5000/llm-eval/backend:latest`
  - Frontend: `localhost:5000/llm-eval/frontend:latest`
- **Performance Improvement**: Build + push speed increased 7-10x (from ~450 seconds down to ~60 seconds)
- **Management Tool**: `scripts/manage-local-registry.sh`

### Deployment Architecture ✅ **UPDATED**
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────────────┐
│   Frontend      │    │    Backend      │    │   PostgreSQL Cluster   │
│   (nginx)       │───▶│   (FastAPI)     │───▶│   - augment DB          │
│   Port: 3000    │    │   Port: 8000    │    │   - roo DB              │
│   LoadBalancer  │    │   ClusterIP     │    │   - cline DB            │
└─────────────────┘    └─────────────────┘    │   - cursor DB           │
                                               │   - copilot DB          │
┌─────────────────┐                           │   - windsurf DB         │
│ Local Registry  │                           │   All: Port 5432        │
│ localhost:5000  │                           │   ClusterIP             │
│ (Docker)        │                           └─────────────────────────┘
└─────────────────┘
```

### Migration Chain Reference (Benchmark implementation's step-by-step migrations, AI should attempt to merge these logics in one go)
1. Migration for: Add usage statistics to generations table (Example ID `a1b2c3d4e5f6`)
2. Migration for: Change cost_credits column to Float for generations (Example ID `b2c3d4e5f6a7`)
3. Migration for: Add usage statistics to rankings table (Example ID `d70dbb5a6738`)

### AI Tool Test Configuration
Each AI tool will:
1. **Be assigned an independent database instance** - To avoid interference between tests
2. **Use the local registry** - To speed up Docker image building and deployment
3. **Start from the same baseline** - To ensure fair comparison
4. **Have its entire process recorded** - Including generated code, migration scripts, test results, etc.

---

**Total Estimated Time (AI single generation + Human verification/correction)**: 2-5 hours (Highly dependent on AI capability and quality of initially generated code)
