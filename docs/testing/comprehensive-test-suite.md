# Comprehensive Test Suite Documentation

## Overview

The LLM Evaluation Platform includes a comprehensive test suite with **386 automated tests** covering both frontend and backend functionality. This document provides detailed information about the test architecture, coverage, and execution.

## Test Suite Statistics

- **Total Tests**: 386
- **Frontend Tests**: 342
- **Backend Tests**: 44
- **Test Frameworks**: Vitest (frontend), pytest (backend)
- **Coverage Tools**: Vitest coverage (frontend), pytest-cov (backend)

## Frontend Tests (342 tests)

### Component Tests

#### Core UI Components
- **LoadingSpinner** (22 tests): Loading states, animations, accessibility
- **ThemeToggle** (8 tests): Theme switching, persistence, visual states
- **SystemStatus** (18 tests): Health monitoring, status display, error states

#### Form Components
- **ModelSelector** (21 tests): Model selection, validation, multi-select functionality
- **PromptInput** (9 tests): Input validation, character limits, placeholder behavior
- **EvaluateArea** (35 tests): Evaluation controls, model selection, custom prompts

#### Display Components
- **OutputDisplay** (26 tests): Output rendering, copy functionality, formatting
- **ReportViewer** (31 tests): Report visualization, ranking display, export features

#### Navigation Components
- **EvaluationSidebar** (43 tests): Evaluation history, filtering, deletion, navigation
- **DeleteConfirmModal** (34 tests): Confirmation dialogs, keyboard navigation, accessibility

#### Usage Statistics Components
- **UsageStatsCard** (20 tests): Statistics display, formatting, null handling
- **CombinedUsageSummary** (17 tests): Aggregated statistics, calculations, visualizations

### Infrastructure Tests

#### API Client Tests (11 tests)
- HTTP request handling
- Error response management
- Retry logic
- Request/response transformation

#### State Management Tests (26 tests)
- **Health Store** (17 tests): Health monitoring state, auto-refresh, error handling
- **Task Store** (9 tests): Task state management, usage statistics tracking

#### Integration Tests (24 tests)
- **Model Selection Integration** (12 tests): End-to-end model selection workflow
- **Usage Statistics Integration** (12 tests): Complete usage tracking workflow

## Backend Tests (44 tests)

### API Endpoint Tests
- **Health API** (3 tests): Health check endpoints, database connectivity
- **Usage Stats API** (5 tests): Statistics endpoints, data aggregation

### CRUD Operation Tests
- **Task CRUD** (18 tests): Create, read, update, delete operations for tasks
- **Generation CRUD** (13 tests): Generation record management, usage tracking
- **Usage Stats CRUD** (5 tests): Statistics persistence and retrieval

## Test Technologies

### Frontend Testing Stack
- **Vitest**: Modern test runner with Vite integration
- **React Testing Library**: Component testing with user-centric approach
- **MSW (Mock Service Worker)**: API mocking for integration tests
- **@testing-library/user-event**: User interaction simulation

### Backend Testing Stack
- **pytest**: Python testing framework
- **pytest-asyncio**: Async test support
- **SQLAlchemy Testing**: Database transaction rollback
- **httpx**: Async HTTP client for API testing

## Running Tests

### Run All Tests
```bash
./scripts/run-comprehensive-tests.py
```

### Run Frontend Tests Only
```bash
cd frontend
npm test
```

### Run Backend Tests Only
```bash
cd backend
poetry run pytest
```

### Run Specific Test Files
```bash
# Frontend
npm test -- src/test/components/LoadingSpinner.test.tsx

# Backend
poetry run pytest tests/test_api_health.py
```

### Run with Coverage
```bash
# Frontend
npm run test:coverage

# Backend
poetry run pytest --cov=app --cov-report=html
```

## Coverage Reports

### Frontend Coverage
- **Location**: `frontend/coverage/index.html`
- **Current Coverage**: ~95% for components, ~90% for API client, ~85% for stores

### Backend Coverage
- **Location**: `backend/htmlcov/index.html`
- **Current Coverage**: ~90% for API endpoints, ~85% for CRUD operations

## Test Patterns and Best Practices

### Frontend Testing Patterns

1. **Component Testing**
   - Test user interactions, not implementation details
   - Use accessibility queries (getByRole, getByLabelText)
   - Mock external dependencies (API calls, timers)
   - Test error states and edge cases

2. **Store Testing**
   - Test state mutations and computed values
   - Verify side effects (API calls)
   - Test error handling and recovery

3. **Integration Testing**
   - Test complete user workflows
   - Use MSW for realistic API mocking
   - Verify component interactions

### Backend Testing Patterns

1. **API Testing**
   - Test all HTTP methods and status codes
   - Verify request validation
   - Test error responses
   - Use dependency injection for mocking

2. **CRUD Testing**
   - Test all database operations
   - Verify data integrity
   - Test concurrent operations
   - Use transaction rollback for isolation

3. **Async Testing**
   - Use pytest-asyncio for async tests
   - Test concurrent operations
   - Verify proper cleanup

## Common Testing Issues and Solutions

### Timer Handling (Frontend)
```typescript
// Use vi.useFakeTimers() and vi.waitFor()
vi.useFakeTimers()
await vi.waitFor(() => {
  expect(element).toBeInTheDocument()
})
```

### Async Test Marking (Backend)
```python
# Always mark async test classes
@pytest.mark.asyncio
class TestMyFeature:
    async def test_something(self):
        pass
```

### Console Error Mocking (Frontend)
```typescript
// Mock console.error to prevent test failures
const consoleErrorSpy = vi.spyOn(console, 'error').mockImplementation(() => {})
```

## Continuous Integration

Tests are automatically run in CI/CD pipeline:
- On every pull request
- Before deployment to production
- Nightly for regression testing

## Future Enhancements

1. **E2E Testing**: Add Playwright for end-to-end testing
2. **Performance Testing**: Add performance benchmarks
3. **Visual Regression**: Add visual snapshot testing
4. **API Contract Testing**: Add Pact or similar
5. **Load Testing**: Add k6 or similar for load testing

## Related Documentation

- [Usage Statistics Testing Implementation](./usage-statistics-testing-implementation.md)
- [CI/CD Documentation](../cicd/README.md)
- [Scripts Documentation](../../scripts/README.md)