# Backend API Guide

This document provides an overview of the backend API endpoints, their purpose, request/response structures, and key functionalities. All endpoints are prefixed with `/api/v1`.

## 1. Core Concepts

-   **Task:** Represents a single user request, initiated by a prompt and a selection of generator models. A task progresses through various statuses (e.g., PENDING, GENERATING, COMPLETED, EVALUATING, EVALUATION_DONE, FAILED).
-   **Generation:** An individual output produced by one LLM for a given task prompt. Includes the text output, any reasoning from the model, and error messages if applicable.
-   **Evaluation:** A process of assessing the generated outputs for a task. An evaluation is performed by one or more evaluator LLMs.
-   **Ranking:** The result produced by a single evaluator LLM for an evaluation. It includes an ordered list of the generator model IDs (from best to worst) and textual reasoning for this order.

## 2. Endpoints

All endpoints related to tasks, generations, and evaluations are grouped under the `/tasks` prefix.

### 2.1. Model Endpoints

#### `GET /tasks/models`

-   **Description:** Retrieves a list of available LLM models that can be used for generation and evaluation.
-   **Response Body (`ModelListResponse`):**
    -   `models`: A list of strings, where each string is a model identifier (e.g., `openai/gpt-4.1`).

### 2.2. Task Creation & Management Endpoints

#### `POST /tasks/`

-   **Description:** Creates a new task to generate outputs from multiple LLMs based on a user prompt. This triggers a background process for generation.
-   **Request Body (`TaskCreateRequest`):
    -   `prompt` (string, required): The prompt to send to the LLMs.
    -   `models_to_generate` (list of strings, optional): A list of model identifiers to use for generation. If not provided, default models configured on the backend are used.
-   **Response Body (`TaskCreateResponse`, Status: 202 Accepted):
    -   `task_id` (integer): The ID of the newly created task.
    -   `status` (string enum): The initial status of the task (usually `PENDING` or `GENERATING`).
    -   `requested_models` (list of strings): The list of model identifiers that will be used for generation.
    -   `message` (string): A confirmation message.

#### `GET /tasks/{task_id}/status`

-   **Description:** Retrieves the full details for a specific task, including its current status, the original prompt, requested models, all associated generations, and all associated evaluations with their rankings.
-   **Path Parameter:**
    -   `task_id` (integer): The ID of the task to retrieve.
-   **Response Body (`Task` schema):
    -   `id` (integer): Task ID.
    -   `prompt` (string): The original prompt.
    -   `status` (string enum): Current status of the task.
    -   `requested_models` (list of strings): Models originally requested for generation.
    -   `created_at` (datetime): Timestamp of task creation.
    -   `updated_at` (datetime, optional): Timestamp of last update.
    -   `generations` (list of `Generation` objects): All generated outputs for this task.
        -   Each `Generation` object contains: `id`, `task_id`, `model_id_used`, `output_text` (optional), `reasoning_text` (optional), `error_message` (optional), `created_at`.
    -   `evaluations` (list of `Evaluation` objects): All evaluation sessions for this task.
        -   Each `Evaluation` object contains: `id`, `task_id`, `status`, `created_at`, `updated_at` (optional), and a list of `rankings`.
            -   Each `Ranking` object contains: `id`, `evaluation_id`, `evaluator_model_id`, `ranked_list_json` (list of model IDs), `reasoning_text` (optional), `error_message` (optional), `created_at`.

#### `GET /tasks/{task_id}/outputs` (Consider for deprecation)

-   **Description:** Retrieves the current status and generated outputs for a specific task. (Note: `/tasks/{task_id}/status` provides more comprehensive information and is generally preferred).
-   **Path Parameter:**
    -   `task_id` (integer): The ID of the task.
-   **Response Body (`TaskStatusResponse`):
    -   `task_id` (integer): Task ID.
    -   `status` (string enum): Current task status.
    -   `generations` (list of `Generation` objects).

#### `GET /tasks/history`

-   **Description:** Retrieves a paginated list of past tasks, ordered by creation date (newest first). Intended for displaying a task history log.
-   **Query Parameters:**
    -   `skip` (integer, optional, default: 0): Number of tasks to skip for pagination.
    -   `limit` (integer, optional, default: 50): Maximum number of tasks to return.
-   **Response Body (`TaskHistoryResponse`):
    -   `history` (list of `TaskHistoryItem` objects):
        -   Each `TaskHistoryItem` contains: `id` (task ID), `prompt_snippet` (string), `status` (string enum), `created_at` (datetime).

#### `DELETE /tasks/{task_id}`

-   **Description:** Deletes a task and all its associated data, including generations, evaluations, and rankings.
-   **Path Parameter:**
    -   `task_id` (integer): The ID of the task to delete.
-   **Response (Status: 204 No Content):** No body is returned on successful deletion.

### 2.3. Evaluation Endpoints

#### `POST /tasks/{task_id}/evaluate`

-   **Description:** Triggers the evaluation of a task's generated outputs using selected LLMs as evaluators. This starts a background process.
-   **Path Parameter:**
    -   `task_id` (integer): The ID of the task whose outputs are to be evaluated.
-   **Request Body (`EvaluateRequest`):
    -   `evaluator_models` (list of strings, required): A list of model identifiers to use as evaluators. At least one must be provided.
-   **Response Body (`EvaluateResponse`, Status: 202 Accepted):
    -   `evaluation_id` (integer): The ID of the newly created evaluation session.
    -   `status` (string enum): The initial status of the evaluation (usually `PENDING`).
    -   `message` (string): A confirmation message.

#### `GET /evaluations/{evaluation_id}/report`

-   **Description:** Retrieves the full evaluation report for a specific evaluation session, including rankings and reasoning from all evaluator models used in that session.
-   **Path Parameter:**
    -   `evaluation_id` (integer): The ID of the evaluation session.
-   **Response Body (`EvaluationReportResponse`):
    -   `evaluation_id` (integer): Evaluation session ID.
    -   `task_id` (integer): The ID of the parent task.
    -   `status` (string enum): The status of this evaluation session (e.g., `EVALUATING`, `EVALUATION_DONE`, `FAILED`).
    -   `rankings` (list of `Ranking` objects): Contains the detailed ranking and reasoning from each evaluator model involved in this session.

### 2.4. Real-time Streaming Endpoint

#### `GET /tasks/{task_id}/stream`

-   **Description:** Establishes a Server-Sent Event (SSE) connection to stream generation progress for a specific task. This is used by the frontend to display outputs in real-time as they are generated by the LLMs.
-   **Path Parameter:**
    -   `task_id` (integer): The ID of the task to stream.
-   **Events Streamed:** The server sends events with different types (`event: <type>`) and data (`data: <json_payload>`).
    -   `event: message`: Sent when a new chunk of text is generated by a model.
        -   `data`: JSON object like `{"model": "model_id_used", "chunk": "text_chunk", "reasoning_detail": "..."}`.
    -   `event: done`: Sent when a specific model has finished its generation stream.
        -   `data`: JSON object like `{"model": "model_id_used", "status": "DONE", "content": null}`.
    -   `event: error`: Sent if an error occurs during a model's generation stream or if the task setup times out.
        -   `data`: JSON object like `{"model": "model_id_used_or_system", "status": "ERROR", "content": "error_message"}`.
    -   `: keep-alive`: Sent periodically if no actual data events are available, to keep the connection open.

## 3. Status Enums (`TaskStatusEnum`)

The following statuses are used for tasks and evaluations:

-   `PENDING`: Task/Evaluation created but not yet processed.
-   `GENERATING`: LLM generation is in progress for the task.
-   `COMPLETED`: LLM generation for the task has finished successfully.
-   `EVALUATING`: Evaluation of generated outputs is in progress.
-   `EVALUATION_DONE`: Evaluation process has finished successfully.
-   `FAILED`: The task or evaluation encountered an unrecoverable error. 