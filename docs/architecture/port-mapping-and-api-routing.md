# Port Mapping and API Routing Architecture

## Overview

This document explains how the LLM Evaluation Platform handles port mapping and API routing in a Kubernetes (K3s) environment. The architecture uses a single entry point design where all external traffic flows through the frontend service, which then routes API requests to the backend service via nginx reverse proxy.

## Architecture Components

### 1. Frontend Service (External Entry Point)
- **Service Type**: `LoadBalancer`
- **External Port**: `3000` (accessible via `localhost:3000`)
- **Internal Port**: `3000` (nginx container port)
- **External IP**: `*************` (K3s LoadBalancer IP)
- **Pod Replicas**: 2 instances for high availability

### 2. Backend Service (Internal Only)
- **Service Type**: `ClusterIP`
- **Cluster IP**: `***********`
- **Internal Port**: `8000`
- **External Access**: None (not directly accessible from outside the cluster)
- **Pod Replicas**: 2 instances for high availability

### 3. Nginx Reverse Proxy
- **Location**: Inside frontend containers
- **Purpose**: Route API requests to backend service
- **Configuration**: `/frontend/nginx.conf`

## Port Mapping Flow

```
External Request (localhost:3000)
         ↓
K3s LoadBalancer Service (frontend-service)
         ↓
Frontend Pod (nginx:3000)
         ↓
Nginx Reverse Proxy
         ↓
Backend Service (backend-service:8000)
         ↓
Backend Pod (FastAPI:8000)
```

## API Routing Mechanism

### 1. Frontend API Client Configuration

**File**: `frontend/src/api/apiClient.ts`
```typescript
const apiClient = axios.create({
  baseURL: '/api/v1', // Relative path - relies on nginx proxy
  headers: {
    'Content-Type': 'application/json',
  },
  timeout: 30000,
});
```

### 2. Nginx Proxy Configuration

**File**: `frontend/nginx.conf`
```nginx
# Handle API requests (proxy to backend)
location /api/ {
    proxy_pass http://backend-service:8000;
    
    proxy_http_version 1.1;
    proxy_set_header Upgrade $http_upgrade;
    proxy_set_header Connection 'upgrade';
    
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
}

# Handle static assets and SPA routing
location / {
    try_files $uri $uri/ /index.html;
}
```

### 3. Request Flow Example

When a user creates a new task:

1. **Frontend JavaScript**: 
   ```javascript
   api.createTask(prompt, selectedModels, systemPrompt)
   ```

2. **Axios Request**:
   ```
   POST /api/v1/tasks
   ```

3. **Browser Resolution**:
   ```
   http://localhost:3000/api/v1/tasks
   ```

4. **K3s LoadBalancer**: Routes to frontend service

5. **Nginx Proxy**: Matches `/api/` location block

6. **Backend Proxy**:
   ```
   http://backend-service:8000/api/v1/tasks
   ```

7. **Kubernetes DNS**: Resolves `backend-service` to `***********:8000`

8. **Backend Processing**: FastAPI handles the request

9. **Response Path**: Reverse of the above flow

## Service Discovery in Kubernetes

### DNS Resolution
- **Service Name**: `backend-service`
- **Namespace**: `llm-eval`
- **Full FQDN**: `backend-service.llm-eval.svc.cluster.local`
- **Short Name**: `backend-service` (works within same namespace)

### Service Configuration

**Backend Service** (`k8s/backend.yaml`):
```yaml
apiVersion: v1
kind: Service
metadata:
  name: backend-service
  namespace: llm-eval
spec:
  selector:
    app: backend
  ports:
  - port: 8000
    targetPort: 8000
    protocol: TCP
  type: ClusterIP
```

**Frontend Service** (`k8s/frontend.yaml`):
```yaml
apiVersion: v1
kind: Service
metadata:
  name: frontend-service
  namespace: llm-eval
spec:
  selector:
    app: frontend
  ports:
  - port: 3000
    targetPort: 3000
    protocol: TCP
  type: LoadBalancer
```

## Security Considerations

### 1. Network Isolation
- Backend service is not directly accessible from external networks
- All API access must go through the frontend nginx proxy
- Provides an additional layer of security and request filtering

### 2. Nginx Security Headers
```nginx
add_header X-Frame-Options "SAMEORIGIN" always;
add_header X-XSS-Protection "1; mode=block" always;
add_header X-Content-Type-Options "nosniff" always;
add_header Referrer-Policy "no-referrer-when-downgrade" always;
add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;
```

### 3. Proxy Headers
- `X-Real-IP`: Preserves original client IP
- `X-Forwarded-For`: Maintains request chain
- `X-Forwarded-Proto`: Preserves original protocol

## Troubleshooting

### Common Issues

1. **API Requests Failing**
   - Check if backend pods are running: `kubectl get pods -n llm-eval`
   - Verify backend service exists: `kubectl get svc -n llm-eval`
   - Check nginx configuration in frontend pods

2. **Port 3000 Not Accessible**
   - Verify LoadBalancer service: `kubectl get svc frontend-service -n llm-eval`
   - Check K3s LoadBalancer configuration
   - Ensure frontend pods are running

3. **Backend Service Unreachable**
   - Verify service name resolution: `kubectl exec -it <frontend-pod> -- nslookup backend-service`
   - Check backend pod logs: `kubectl logs -n llm-eval <backend-pod>`
   - Verify service selector matches pod labels

### Debugging Commands

```bash
# Check all services and pods
kubectl get pods,svc -n llm-eval

# Check frontend nginx logs
kubectl logs -n llm-eval <frontend-pod>

# Check backend API logs
kubectl logs -n llm-eval <backend-pod>

# Test service connectivity from frontend pod
kubectl exec -it -n llm-eval <frontend-pod> -- curl http://backend-service:8000/api/v1/health

# Check service endpoints
kubectl get endpoints -n llm-eval
```

## Alternative Architectures

### Option 1: Direct Backend Exposure
```yaml
# Change backend service to LoadBalancer
spec:
  type: LoadBalancer
  ports:
  - port: 8000
    targetPort: 8000
```

**Pros**: Direct API access, simpler debugging
**Cons**: Multiple entry points, potential security concerns

### Option 2: Ingress Controller
```yaml
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: llm-eval-ingress
spec:
  rules:
  - host: localhost
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: frontend-service
            port:
              number: 3000
      - path: /api
        pathType: Prefix
        backend:
          service:
            name: backend-service
            port:
              number: 8000
```

**Pros**: More flexible routing, better for production
**Cons**: Additional complexity, requires ingress controller

## Performance Considerations

### 1. Nginx Proxy Overhead
- Minimal latency impact (typically <1ms)
- Enables caching and compression
- Provides connection pooling

### 2. LoadBalancer vs NodePort
- LoadBalancer: Better for production, automatic external IP
- NodePort: Simpler for development, manual port management

### 3. Service Mesh Alternative
For complex microservices, consider service mesh solutions like Istio or Linkerd for advanced traffic management.

## Conclusion

The current architecture provides a secure, scalable, and maintainable approach to API routing in Kubernetes. The single entry point design through nginx reverse proxy offers excellent control over traffic flow while maintaining security best practices. This pattern is widely adopted in production Kubernetes deployments and provides a solid foundation for the LLM Evaluation Platform. 