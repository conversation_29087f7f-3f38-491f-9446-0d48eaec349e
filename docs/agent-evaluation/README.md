# AI Agent Evaluation System Documentation

This directory contains all documentation related to the AI Agent Evaluation system implementation.

## Overview

The AI Agent Evaluation System is designed to evaluate AI agents' performance on coding tasks using two main evaluation types:
- **0-1 Evaluation**: Evaluating agents building solutions from scratch
- **90-100 Evaluation**: Evaluating agents making incremental improvements

## Documentation Structure

### Core Documentation

#### 1. [User Guide](./user-guide.md)
Complete guide for using the AI Agent Evaluation system:
- Getting started
- Creating evaluation tasks
- Managing submissions
- Running evaluations
- Understanding results
- Best practices

#### 2. [Implementation Plan](./implementation-plan.md)
Detailed technical plan for the AI Agent Evaluation system:
- Architecture overview
- Data models and schemas
- API endpoints
- Implementation phases

#### 3. [Implementation Summary](./implementation-summary.md)
Summary of the implemented features:
- Phase 1 completion details
- Database models
- API endpoints
- File upload functionality
- Testing approach

#### 4. [Testing Strategy](./test-strategy.md)
Comprehensive testing strategy:
- Testing principles
- Test layers (Unit, Integration, API)
- Phase-by-phase testing approach
- No over-mocking principle

### Phase Completion Reports

#### 5. [Phase 2: Context Capture](./phase-2-report.md)
Backend services for capturing evaluation contexts:
- Token estimation service
- Blob storage implementation
- Gitignore parser
- Folder and Git context capture
- Database models and migrations

#### 6. [Phase 3: Evaluation Engine](./phase-3-report.md)
Core evaluation functionality:
- Criteria generation service
- Multi-model evaluator
- Score aggregation
- Evaluation pipeline
- Real-time streaming (SSE)
- Complete API endpoints

#### 7. [Phase 4: Frontend Implementation](./phase-4-report.md)
User interface for the evaluation system:
- Agent evaluation dashboard
- Task creation interface
- Submission management
- Real-time progress tracking
- Results visualization

## Key Features Implemented

1. **Task Management**
   - Create evaluation tasks with prompts
   - Support for 0-1 and 90-100 evaluation types
   - Task status tracking (draft, ready, running, completed)

2. **File Upload System**
   - Support for multiple file uploads
   - Archive extraction (ZIP, TAR, TAR.GZ)
   - 100MB size limit
   - Progress tracking

3. **Evaluation Pipeline**
   - Automatic criteria generation
   - Multi-model evaluation support
   - Real-time progress updates via SSE
   - Comprehensive result aggregation

4. **Frontend Components**
   - Task creation interface
   - File upload with drag-and-drop
   - Submission details viewer
   - Results visualization

## API Endpoints

- `POST /api/v1/agent-evaluations/tasks` - Create evaluation task
- `POST /api/v1/agent-evaluations/tasks/{task_id}/upload-submission` - Upload files
- `POST /api/v1/agent-evaluations/generate-criteria` - Generate evaluation criteria
- `POST /api/v1/agent-evaluations/evaluate` - Run evaluation
- `GET /api/v1/agent-evaluations/submissions/{submission_id}/contexts` - Get submission details

## Database Schema

The system uses PostgreSQL with the following main tables:
- `agent_evaluation_tasks` - Evaluation task definitions
- `agent_submissions` - Agent submissions with code
- `evaluation_contexts` - Stored file contents
- `evaluation_criteria` - Generated evaluation criteria
- `agent_evaluations` - Evaluation runs
- `evaluation_results` - Evaluation scores and feedback

## Usage Guide

1. **Create a Task**: Define the evaluation task with a prompt
2. **Upload Submissions**: Upload agent-generated code files or archives
3. **Generate Criteria**: Auto-generate evaluation criteria based on the task
4. **Run Evaluation**: Execute the evaluation across multiple LLM models
5. **View Results**: See rankings and detailed feedback

## Recent Updates

- Fixed file upload functionality
- Added submission details viewer
- Improved error handling for API serialization
- Updated UI to show correct submission types
- Consolidated documentation structure