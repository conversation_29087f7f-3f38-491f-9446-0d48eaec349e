# Agent Evaluation System - Testing Strategy

## Testing Principles

1. **No Over-Mocking**: Only use mocks when necessary (e.g., external API calls)
2. **Real Database Tests**: Integration tests use real PostgreSQL
3. **API Contract Testing**: All API endpoints must have serialization tests
4. **Progressive Testing**: Test immediately after completing each phase

## Testing Layers

### 1. Unit Tests

**Goal**: Test isolated functions and class methods

```python
# Example: token_estimator.py
def test_estimate_tokens():
    estimator = TokenEstimator()
    result = estimator.estimate_text("Hello world")
    assert result > 0
```

**When to use mocks**:
- File system operations
- External API calls
- Time-related functions

### 2. Component Tests

**Goal**: Test complete service layer functionality

```python
# Example: folder_context_capture service
async def test_capture_folder_with_gitignore():
    # Use real temporary file system
    with tempfile.TemporaryDirectory() as tmpdir:
        # Create test file structure
        create_test_files(tmpdir)
        
        # Test real file capture
        capture = FolderContextCapture()
        result = await capture.capture_folder(tmpdir)
        
        # Verify results
        assert len(result['contexts']) > 0
```

### 3. API Tests

**Goal**: Test complete HTTP request/response cycle

```python
# Example: Ensure proper serialization
def test_create_task_api():
    # Use real database (test database)
    with TestClient(app) as client:
        response = client.post("/api/v1/agent-evaluations/tasks", json={
            "name": "Test Task",
            "evaluation_type": "0-1",
            "prompt": "Test prompt"
        })
        
        # Verify response
        assert response.status_code == 200
        data = response.json()
        
        # Ensure JSON is returned, not SQLAlchemy objects
        assert isinstance(data, dict)
        assert "_sa_instance_state" not in data
```

### 4. Integration Tests

**Goal**: Test complete business workflows

```python
# Example: Complete evaluation flow
async def test_complete_evaluation_flow():
    # Step 1: Create task
    task = await create_evaluation_task(...)
    
    # Step 2: Import submission
    submission = await import_folder_submission(...)
    
    # Step 3: Generate evaluation criteria
    criteria = await generate_criteria(...)
    
    # Step 4: Run evaluation (Mock OpenRouter API)
    with mock_openrouter():
        result = await run_evaluation(...)
    
    # Step 5: Verify results
    assert result.final_score > 0
```

## Phase Testing Requirements

### Phase 1: Foundation
- [x] Test database model creation
- [x] Test basic CRUD operations
- [x] Test blob storage mechanism

### Phase 2: Context Capture  
- [x] Test folder scanning and .gitignore parsing
- [x] Test Git operations (commit diff, history)
- [x] Test token estimation accuracy
- [x] Test large file blob storage

### Phase 3: Evaluation Engine
- [ ] Test criteria generation (Mock LLM)
- [ ] Test multi-model evaluation flow
- [ ] Test score aggregation algorithm
- [ ] Test streaming progress updates

### Phase 4: Frontend
- [ ] Test component rendering
- [ ] Test API calls
- [ ] Test state management (Zustand)
- [ ] Test error handling

### Phase 5: Integration
- [ ] End-to-end test complete workflow
- [ ] Performance tests (large files, multiple commits)
- [ ] Concurrency tests
- [ ] Error recovery tests

## Test Environment Setup

### 1. Test Database
```bash
# PostgreSQL for integration tests
TEST_DATABASE_URL=postgresql://test_user:test_pass@localhost:5432/test_llm_eval
```

### 2. Mock 服务
```python
# Mock OpenRouter API responses
@pytest.fixture
def mock_openrouter():
    with responses.RequestsMock() as rsps:
        rsps.add(
            responses.POST,
            "https://openrouter.ai/api/v1/chat/completions",
            json={"choices": [{"message": {"content": "mocked response"}}]}
        )
        yield rsps
```

### 3. Test Data
```python
# Standard test datasets
TEST_DATASETS = {
    "small_project": "tests/fixtures/small_project/",
    "large_project": "tests/fixtures/large_project/",
    "git_repo": "tests/fixtures/test_repo.git/"
}
```

## Continuous Integration (CI)

```yaml
# .github/workflows/test.yml
name: Tests
on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
    
    steps:
      - uses: actions/checkout@v3
      - name: Run Unit Tests
        run: pytest tests/unit -v
      - name: Run Integration Tests  
        run: pytest tests/integration -v
      - name: Run API Tests
        run: pytest tests/api -v
```

## Test Coverage Goals

- Unit Tests: 80%+ coverage
- API Tests: 100% endpoint coverage
- Integration Tests: All critical business workflows

## Common Testing Pitfalls

1. **Over-Mocking**: Tests pass but actual code fails
2. **Ignoring Serialization**: Not testing API response formats
3. **Skipping Edge Cases**: Large files, empty files, special characters
4. **Ignoring Concurrency**: Multiple users operating simultaneously
5. **Not Testing Error Paths**: Only testing success scenarios

## Test Commands

```bash
# Run all tests
pytest

# Run specific test types
pytest tests/unit
pytest tests/api  
pytest tests/integration

# Run with coverage
pytest --cov=app --cov-report=html

# Run specific test
pytest tests/api/test_agent_evaluation_api.py::test_create_task
```