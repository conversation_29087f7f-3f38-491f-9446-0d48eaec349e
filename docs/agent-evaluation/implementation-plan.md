# AI Agent Evaluation System - Implementation Plan

## Background

The current LLM evaluation platform allows users to create tasks, generate outputs using LLMs, and evaluate those outputs. The goal is to extend this platform to evaluate AI Agents' capabilities through two distinct evaluation modes:

### 0-1 Evaluation (From Scratch)
- AI Agents generate complete projects from the same prompt
- Projects are saved in separate folders (e.g., `task1-agent-a`, `task1-agent-b`)
- Users manually select folders and specify agent metadata (name, version, original prompt)
- System imports all files (excluding those in .gitignore) and evaluates the outputs
- Context includes all project files for comprehensive evaluation

### 90-100 Evaluation (Incremental Improvement)
- Start from a common codebase
- Use git worktree to create separate working directories for each agent
- Agents make improvements using the same prompt and commit their changes
- Users manually select specific commits from each agent for evaluation
- Evaluation context includes git diffs and modified files from selected commits
- Challenge: Agents may produce different numbers of commits, requiring flexible selection

### Key Requirements
- **No file size limits** - Handle projects of any size
- **Token estimation only** - Calculate approximate tokens, no cost estimation needed
- **Context storage** - Store evaluation context in database/blob storage, not just file paths
- **Gitignore support** - Respect .gitignore when scanning folders
- **Reproducibility** - Evaluations must be reproducible even if source files change

## Core Architecture

### System Components

```
AI Agent Evaluation System
├── Context Management
│   ├── Token Calculator (estimates tokens for files)
│   ├── Folder Scanner (respects .gitignore)
│   ├── Git Integration (worktree, commits, diffs)
│   └── Storage Service (PostgreSQL + blob storage)
├── Evaluation Pipeline
│   ├── Criteria Generator (LLM-based)
│   ├── Multi-Model Evaluator
│   └── Score Aggregator
└── UI Components
    ├── Folder Selector
    ├── Commit Selector
    ├── Token Display
    └── Results Comparison
```

## Database Schema

### Core Tables

```sql
-- Main evaluation task table
CREATE TABLE agent_evaluation_tasks (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    title VARCHAR(255) NOT NULL,
    description TEXT,
    evaluation_type VARCHAR(20) NOT NULL CHECK (evaluation_type IN ('0-1', '90-100')),
    original_prompt TEXT NOT NULL,
    base_repository_url TEXT,  -- For 90-100 evaluations
    status VARCHAR(50) DEFAULT 'draft',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Agent submissions for evaluation
CREATE TABLE agent_submissions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    task_id UUID NOT NULL REFERENCES agent_evaluation_tasks(id) ON DELETE CASCADE,
    agent_name VARCHAR(255) NOT NULL,
    agent_version VARCHAR(50),
    submission_type VARCHAR(20) NOT NULL CHECK (submission_type IN ('folder', 'commits')),
    original_path TEXT,  -- Reference only, not used for evaluation
    total_estimated_tokens INTEGER,
    metadata JSONB,  -- Additional agent-specific metadata
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Selected commits for 90-100 evaluations
CREATE TABLE selected_commits (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    submission_id UUID NOT NULL REFERENCES agent_submissions(id) ON DELETE CASCADE,
    commit_hash VARCHAR(40) NOT NULL,
    commit_message TEXT,
    commit_timestamp TIMESTAMP WITH TIME ZONE,
    files_changed INTEGER,
    insertions INTEGER,
    deletions INTEGER,
    author VARCHAR(255),
    metadata JSONB
);

-- Captured context for evaluation
CREATE TABLE evaluation_context (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    submission_id UUID NOT NULL REFERENCES agent_submissions(id) ON DELETE CASCADE,
    context_type VARCHAR(20) NOT NULL CHECK (context_type IN ('file', 'diff', 'metadata')),
    file_path TEXT NOT NULL,  -- Relative path within the context
    content TEXT,  -- Null if stored in blob storage
    content_hash VARCHAR(64) NOT NULL,  -- SHA-256 for deduplication
    file_metadata JSONB,  -- File type, size, encoding, etc.
    estimated_tokens INTEGER NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_submission_context (submission_id, context_type),
    INDEX idx_content_hash (content_hash)
);

-- Large content blob storage references
CREATE TABLE evaluation_context_blobs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    context_id UUID NOT NULL REFERENCES evaluation_context(id) ON DELETE CASCADE,
    storage_type VARCHAR(20) NOT NULL DEFAULT 'local',
    storage_key VARCHAR(500) NOT NULL,
    content_type VARCHAR(100),
    compressed_size BIGINT,
    uncompressed_size BIGINT,
    compression_type VARCHAR(20),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Evaluation criteria
CREATE TABLE evaluation_criteria (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    task_id UUID NOT NULL REFERENCES agent_evaluation_tasks(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    criteria_prompt TEXT NOT NULL,
    generated_by_model VARCHAR(100),
    criteria_schema JSONB NOT NULL,  -- Structured criteria definition
    weight DECIMAL(3,2) DEFAULT 1.0,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Individual evaluations
CREATE TABLE agent_evaluations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    submission_id UUID NOT NULL REFERENCES agent_submissions(id) ON DELETE CASCADE,
    criteria_id UUID NOT NULL REFERENCES evaluation_criteria(id),
    model_id VARCHAR(100) NOT NULL,
    overall_score DECIMAL(5,2),
    detailed_scores JSONB,  -- Breakdown by criteria components
    rationale TEXT,
    confidence_level DECIMAL(3,2),
    token_usage JSONB,  -- Input/output tokens
    evaluation_metadata JSONB,
    started_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_submission_evaluations (submission_id),
    INDEX idx_model_evaluations (model_id)
);

-- Aggregated results
CREATE TABLE evaluation_results (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    task_id UUID NOT NULL REFERENCES agent_evaluation_tasks(id) ON DELETE CASCADE,
    submission_id UUID NOT NULL REFERENCES agent_submissions(id) ON DELETE CASCADE,
    aggregation_method VARCHAR(50) NOT NULL,
    final_score DECIMAL(5,2),
    score_breakdown JSONB,
    ranking INTEGER,
    summary TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

## Token Estimation

### Approach
- Use tiktoken with cl100k_base encoding for accurate estimation
- For small files (<10KB): exact token count
- For large files: sampling-based estimation with file-type multipliers
- Account for formatting overhead in context assembly

### File Type Multipliers
Different file types have different token densities:
- Code files (.py, .js, .go): ~0.28-0.32 tokens/character
- Markup (.md, .txt): ~0.25 tokens/character
- Data files (.json, .yaml): ~0.33-0.35 tokens/character
- Verbose formats (.xml, .html): ~0.38-0.40 tokens/character

## Blob Storage Strategy

Since the system uses PostgreSQL, blob storage will be implemented using:

### Local Filesystem Storage
- Store large files (>1MB) on local filesystem
- Path structure: `/var/lib/llm-eval/contexts/{hash_prefix}/{hash}`
- Track references in `evaluation_context_blobs` table
- Use zlib compression for text files
- Implement cleanup for orphaned blobs

### Storage Thresholds
- <100KB: Store directly in `evaluation_context.content`
- 100KB-10MB: Store compressed in filesystem
- >10MB: Store compressed with chunking if needed

## Key Services

### Context Capture
**0-1 Evaluation:**
- Scan folders recursively
- Parse and respect .gitignore patterns
- Include default excludes (node_modules, __pycache__, .env, etc.)
- Capture all file contents with metadata
- Calculate token estimates per file

**90-100 Evaluation:**
- Use GitPython for repository operations
- Capture commit metadata (message, author, timestamp)
- Generate diffs between commits
- Extract changed file contents at specific commits
- Support for multiple commit selection per agent

### Evaluation Pipeline
1. **Criteria Generation**: LLM generates quantifiable evaluation criteria based on task prompt
2. **Multi-Model Evaluation**: Run evaluations with multiple models for robustness
3. **Score Aggregation**: Weighted scoring based on criteria importance
4. **Result Storage**: Store detailed breakdowns and summaries

## API Endpoints

```
POST   /api/v1/agent-evaluations/tasks                 # Create evaluation task
GET    /api/v1/agent-evaluations/tasks                 # List tasks
POST   /api/v1/agent-evaluations/tasks/{id}/import-folder    # Import folder (0-1)
POST   /api/v1/agent-evaluations/tasks/{id}/select-commits   # Select commits (90-100)
GET    /api/v1/agent-evaluations/estimate-tokens       # Token estimation
POST   /api/v1/agent-evaluations/generate-criteria     # Generate evaluation criteria
POST   /api/v1/agent-evaluations/evaluate              # Run evaluation
GET    /api/v1/agent-evaluations/results/{task_id}     # Get results
```

## UI Components

### Key Frontend Elements
1. **Folder Selector**: Multi-folder selection with agent metadata input
2. **Commit Selector**: Browse and select commits from git history
3. **Token Estimator**: Real-time display of estimated tokens
4. **Criteria Editor**: Create/edit evaluation criteria with AI assistance
5. **Results Comparison**: Side-by-side agent performance comparison

## Implementation Phases

### Phase 1: Foundation (Weeks 1-2) ✓ COMPLETED
- Database schema and migrations ✓
- Token estimation system ✓
- Blob storage implementation ✓

### Phase 2: Context Capture (Weeks 3-4) ✓ COMPLETED
- Folder scanning with .gitignore support ✓
- Git integration for commit capture ✓
- Context storage optimization ✓

### Phase 3: Evaluation Engine (Weeks 5-6) ✓ COMPLETED
- Criteria generation ✓
- Multi-model evaluation ✓
- Score aggregation ✓
- Evaluation pipeline ✓
- Streaming progress ✓
- API endpoints ✓

### Phase 4: Frontend (Weeks 7-8) ✓ COMPLETED
- Selection interfaces ✓
- Results visualization ✓
- Real-time updates ✓
- Task management UI ✓
- Progress tracking ✓
- Navigation system ✓

### Phase 5: Testing & Deployment (Weeks 9-10) ✓ COMPLETED
- Integration testing ✓
- Performance optimization ✓
- Documentation ✓
- Kubernetes deployment ✓
- User guide ✓
- API tests ✓

## Technical Decisions

### Why Store Context
- **Immutability**: Evaluations remain reproducible
- **Reliability**: No dependency on external files
- **Portability**: Can share/archive evaluations
- **Audit Trail**: Complete evaluation history

### Key Considerations
- Use content hashing for deduplication
- Implement streaming for large contexts
- Add progress tracking for long operations
- Support evaluation cancellation
- Include comprehensive logging