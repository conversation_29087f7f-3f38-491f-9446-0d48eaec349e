# AI Agent Evaluation Platform - User Guide

## Table of Contents

1. [Introduction](#introduction)
2. [Getting Started](#getting-started)
3. [Creating Evaluation Tasks](#creating-evaluation-tasks)
4. [Managing Agent Submissions](#managing-agent-submissions)
5. [Running Evaluations](#running-evaluations)
6. [Understanding Results](#understanding-results)
7. [Best Practices](#best-practices)
8. [Troubleshooting](#troubleshooting)

## Introduction

The AI Agent Evaluation Platform allows you to systematically evaluate and compare different AI agents' code generation capabilities. The platform supports two evaluation modes:

- **0-1 Evaluation (From Scratch)**: Evaluate complete projects generated by AI agents from the same prompt
- **90-100 Evaluation (Incremental)**: Evaluate improvements made by AI agents to an existing codebase

## Getting Started

### Accessing the Platform

1. Navigate to the platform URL in your web browser
2. You'll see two main sections in the navigation:
   - **LLM Evaluation**: For evaluating individual LLM responses
   - **Agent Evaluation**: For evaluating AI agent code generation (this guide)

### Platform Overview

The Agent Evaluation workflow consists of:
1. Creating an evaluation task with a specific prompt
2. Adding agent submissions (their generated code)
3. Generating evaluation criteria automatically
4. Running the evaluation across multiple models
5. Viewing and comparing results

## Creating Evaluation Tasks

### Step 1: Navigate to Agent Evaluation

Click on "Agent Evaluation" in the main navigation bar.

### Step 2: Create New Evaluation

1. Click the "New Evaluation" button
2. Fill in the required fields:
   - **Title**: A descriptive name for your evaluation
   - **Description**: Brief explanation of what you're evaluating
   - **Evaluation Type**: Choose between:
     - **0-1 (From Scratch)**: For complete project generation
     - **90-100 (Incremental)**: For code improvements
   - **Original Prompt**: The exact prompt given to all AI agents

### Example Prompts

#### 0-1 Evaluation Example:
```
Create a todo application with the following features:
- Add, edit, and delete tasks
- Mark tasks as complete
- Filter tasks by status (all, active, completed)
- Persist data to local storage
- Responsive design with modern UI
- Include error handling and input validation
```

#### 90-100 Evaluation Example:
```
Improve this codebase by:
- Adding comprehensive error handling
- Improving performance bottlenecks
- Adding unit tests with >80% coverage
- Enhancing documentation with JSDoc/docstrings
- Implementing proper logging
- Refactoring for better maintainability
```

### Step 3: Additional Configuration

For **90-100 evaluations**, you'll also need to provide:
- **Base Repository URL**: The Git repository that agents will improve

## Managing Agent Submissions

### Adding Submissions

After creating a task, you need to add agent outputs for evaluation.

#### For 0-1 Evaluations (Folder-based):

1. Click "Add Submission" on the task detail page
2. Fill in:
   - **Agent Name**: e.g., "GPT-4", "Claude 3", "Gemini Pro"
   - **Agent Version**: e.g., "turbo-0125", "3.5-sonnet"
   - **Folder Path**: Full path to the agent's generated project
3. Optional: Add metadata (JSON format) for agent parameters:
   ```json
   {
     "temperature": 0.7,
     "max_tokens": 4000,
     "system_prompt": "You are an expert programmer..."
   }
   ```

#### For 90-100 Evaluations (Git-based):

1. Click "Add Submission" on the task detail page
2. Fill in:
   - **Agent Name** and **Version**
   - **Repository Path**: Local path to the git repository
   - **Commit Hashes**: One or more commit hashes (one per line)

### Token Estimation

Before submitting, click "Estimate Tokens" to see how many tokens the evaluation will consume. This helps you:
- Ensure submissions are within model context limits
- Estimate evaluation costs
- Identify overly large submissions

### Best Practices for Submissions

1. **Consistent Naming**: Use consistent agent names across evaluations
2. **Version Tracking**: Always specify the exact model version
3. **Clean Outputs**: Ensure agent outputs don't contain:
   - Personal information
   - API keys or secrets
   - Large binary files
4. **Multiple Submissions**: Add 2-5 different agents for meaningful comparison

## Running Evaluations

### Step 1: Generate Evaluation Criteria

Once you have submissions:
1. Click "Generate Criteria" button
2. The system will automatically generate 5 evaluation criteria based on your task
3. Review the generated criteria in the "Criteria" tab

### Understanding Criteria

Each criterion includes:
- **Name**: What aspect is being evaluated
- **Weight**: Relative importance (0-1)
- **Components**: Specific aspects with scoring rubrics
- **Evaluation Method**: How the criterion is assessed

Example criteria for a todo app:
- **Functional Completeness** (30%): All features implemented correctly
- **Code Quality** (25%): Clean, maintainable code following best practices
- **Error Handling** (20%): Robust error handling and edge cases
- **UI/UX Design** (15%): User interface quality and responsiveness
- **Documentation** (10%): Code comments and user documentation

### Step 2: Run Evaluation

1. Click the "Run Evaluation" button
2. A progress modal will appear showing:
   - Current step (1-6)
   - Progress percentage
   - Activity log
3. The evaluation typically takes 2-5 minutes depending on:
   - Number of submissions
   - Code complexity
   - Number of evaluation models

### Evaluation Steps

1. **Loading Task**: Retrieving task and submission data
2. **Generating Criteria**: Creating evaluation rubrics (if not done)
3. **Preparing Contexts**: Loading and organizing code for evaluation
4. **Running Evaluations**: Multiple models evaluate each submission
5. **Aggregating Results**: Combining scores across models
6. **Generating Rankings**: Final ranking and summary

## Understanding Results

### Results Overview

After evaluation completes, the "Results" tab shows:

#### Rankings View
- **Rank badges**: Visual indicators (gold, silver, bronze)
- **Agent details**: Name and version
- **Final scores**: Weighted average across all criteria
- **Score breakdown**: Expandable details for each criterion

#### Comparison View
- **Table format**: Side-by-side score comparison
- **Criteria columns**: Score for each evaluation criterion
- **Color coding**: Visual indicators of performance levels

### Score Interpretation

Scores are on a 0-100 scale:
- **90-100**: Excellent implementation
- **70-89**: Good implementation with minor issues
- **50-69**: Acceptable but needs improvement
- **Below 50**: Significant issues or missing features

### Summary Statistics

- **Winner Margin**: Score difference between 1st and 2nd place
- **Average Score**: Mean score across all submissions
- **Score Range**: Minimum to maximum scores

### Detailed Analysis

Click on any submission to see:
- **Component scores**: Breakdown within each criterion
- **Model agreement**: Variance between evaluating models
- **Specific feedback**: What worked well and what didn't

## Best Practices

### For Reliable Evaluations

1. **Clear Prompts**: Write specific, unambiguous prompts
2. **Multiple Agents**: Compare at least 3 different agents
3. **Consistent Context**: Ensure all agents receive identical prompts
4. **Review Criteria**: Check that generated criteria match your intent

### For 0-1 Evaluations

1. **Project Structure**: Ensure agents generate complete, runnable projects
2. **Dependencies**: Include package files (package.json, requirements.txt)
3. **Documentation**: Encourage README files in your prompt

### For 90-100 Evaluations

1. **Clean Baseline**: Start from a working codebase
2. **Atomic Commits**: Each commit should represent one improvement
3. **Clear Instructions**: Specify exactly what should be improved

### Avoiding Common Pitfalls

1. **Token Limits**: Check estimation before running evaluation
2. **File Types**: Exclude binary files and build artifacts
3. **Git History**: For 90-100, ensure commits are on correct branch
4. **Prompt Engineering**: Test prompts with one agent before full evaluation

## Troubleshooting

### Common Issues

#### "Import Failed" Error
- **Cause**: Invalid folder path or permission issues
- **Solution**: Verify path exists and has read permissions

#### "Token Limit Exceeded"
- **Cause**: Submission too large for evaluation models
- **Solution**: 
  - Remove unnecessary files
  - Add more specific .gitignore
  - Split into smaller evaluations

#### "Evaluation Stuck"
- **Cause**: Network issues or API limits
- **Solution**: 
  - Check your internet connection
  - Wait a few minutes and retry
  - Contact support if persists

#### "No Results Generated"
- **Cause**: Evaluation failed or incomplete
- **Solution**: 
  - Check task status
  - Review error messages
  - Ensure all submissions imported successfully

### Getting Help

1. **Check Logs**: Review the activity log during evaluation
2. **Task Status**: Verify task status (should be "completed")
3. **Error Messages**: Note any specific error messages
4. **Support**: Contact platform administrators with:
   - Task ID
   - Error messages
   - Steps to reproduce

## Advanced Features

### Custom Metadata

Add agent-specific parameters for better tracking:
```json
{
  "temperature": 0.7,
  "top_p": 0.95,
  "frequency_penalty": 0.5,
  "presence_penalty": 0.5,
  "system_prompt": "Custom instructions...",
  "tool_use": ["web_search", "code_execution"],
  "attempt_number": 1
}
```

### Evaluation Models

By default, evaluations use:
- Claude 3.5 Sonnet
- GPT-4
- Gemini Pro 1.5

### Export Options

Results can be used for:
- Performance tracking over time
- Model selection decisions
- Prompt optimization
- Research and analysis

## Tips for Success

1. **Start Small**: Test with 2-3 agents before larger evaluations
2. **Iterate**: Use results to improve prompts and try again
3. **Document**: Keep notes on what prompts work best
4. **Compare Versions**: Track the same agent across versions
5. **Share Results**: Collaborate with team members on findings

Remember: The goal is to objectively measure and improve AI agent performance for code generation tasks. Consistent methodology and clear evaluation criteria are key to meaningful results.