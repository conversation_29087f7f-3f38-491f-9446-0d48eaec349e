import React from 'react';
import { FolderIcon, PlusIcon } from '@heroicons/react/24/outline';

interface EmptyStateProps {
  title: string;
  description: string;
  actionText?: string;
  onAction?: () => void;
  showAction?: boolean;
  icon?: React.ReactNode;
  className?: string;
}

const EmptyState: React.FC<EmptyStateProps> = ({
  title,
  description,
  actionText = "Create First Evaluation",
  onAction,
  showAction = false,
  icon,
  className = "text-center py-12 card-glass"
}) => {
  return (
    <div className={className}>
      <div className="text-light-secondary dark:text-dark-secondary mb-4 opacity-60">
        {icon || <FolderIcon className="w-16 h-16 mx-auto" />}
      </div>
      <h3 className="text-xl font-semibold text-light-primary dark:text-dark-primary mb-2">
        {title}
      </h3>
      <p className="text-light-secondary dark:text-dark-secondary mb-4">
        {description}
      </p>
      {showAction && onAction && (
        <button
          onClick={onAction}
          className="inline-flex items-center gap-2 px-4 py-2 bg-light-accent hover:bg-light-accent-hover dark:bg-dark-accent dark:hover:bg-dark-accent-hover text-white rounded-xl transition-colors duration-200"
        >
          <PlusIcon className="w-5 h-5" />
          {actionText}
        </button>
      )}
    </div>
  );
};

export default EmptyState; 