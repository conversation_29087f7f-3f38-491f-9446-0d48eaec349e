import React from 'react';

interface ErrorDisplayProps {
  error: string;
  title?: string;
  className?: string;
}

const ErrorDisplay: React.FC<ErrorDisplayProps> = ({ 
  error, 
  title = "Error",
  className = "mb-4"
}) => {
  return (
    <div className={`${className} p-4 glass glass-border rounded-xl bg-red-500/10 dark:bg-red-400/10 border-l-4 border-red-500`}>
      <div className="flex items-start space-x-2">
        <svg className="w-4 h-4 text-red-500 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
          <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
        </svg>
        <div>
          <p className="font-semibold text-xs">{title}</p>
          <p className="text-xs mt-0.5">{error}</p>
        </div>
      </div>
    </div>
  );
};

export default ErrorDisplay; 