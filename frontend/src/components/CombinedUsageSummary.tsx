import React from 'react';

interface UsageData {
  prompt_tokens: number;
  completion_tokens: number;
  total_tokens: number;
  reasoning_tokens?: number | null;
  cached_tokens?: number | null;
  cost_credits: number;
  count: number;
  totalGenerations?: number;
  totalEvaluations?: number;
}

interface CombinedUsageSummaryProps {
  generationUsage?: UsageData;
  evaluationUsage?: UsageData;
  evaluationBatchId?: number;
  generationUsageTitle?: string; // Allow custom title for generation usage
}

const CombinedUsageSummary: React.FC<CombinedUsageSummaryProps> = ({
  generationUsage,
  evaluationUsage,
  generationUsageTitle = "Generation Usage"
}) => {
  // Don't render if no usage data is available
  if (!generationUsage && !evaluationUsage) {
    return null;
  }

  const hasGenerationData = generationUsage && (generationUsage.total_tokens > 0 || generationUsage.cost_credits > 0);
  const hasEvaluationData = evaluationUsage && (evaluationUsage.total_tokens > 0 || evaluationUsage.cost_credits > 0);

  if (!hasGenerationData && !hasEvaluationData) {
    return null;
  }

  return (
    <div className="bg-gradient-to-br from-light-component to-light-component-subtle dark:from-dark-component dark:to-dark-component-subtle shadow-md rounded-md border border-light-border/50 dark:border-dark-border/50">
      <div className="px-3 py-3">
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center space-x-2">
            <div className="p-1.5 bg-blue-500/10 dark:bg-blue-400/10 rounded-md">
              <svg className="w-4 h-4 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
            </div>
            <h2 className="text-lg font-bold text-light-primary dark:text-dark-primary">
              Usage Dashboard
            </h2>
          </div>
          
          {hasGenerationData && hasEvaluationData && (
            <div className="flex items-center space-x-3">
              <div className="text-right">
                <div className="text-xs text-light-secondary dark:text-dark-secondary">Total Tokens</div>
                <div className="text-sm font-bold text-blue-600 dark:text-blue-400 font-mono">
                  {(generationUsage!.total_tokens + evaluationUsage!.total_tokens).toLocaleString()}
                </div>
              </div>
              <div className="text-right">
                <div className="text-xs text-light-secondary dark:text-dark-secondary">Total Cost</div>
                <div className="text-sm font-bold text-light-accent dark:text-dark-accent font-mono">
                  ${(generationUsage!.cost_credits + evaluationUsage!.cost_credits).toFixed(6)}
                </div>
              </div>
              {((generationUsage!.reasoning_tokens || 0) + (evaluationUsage!.reasoning_tokens || 0)) > 0 && (
                <div className="text-right">
                  <div className="text-xs text-light-secondary dark:text-dark-secondary">Reasoning</div>
                  <div className="text-sm font-bold text-blue-600 dark:text-blue-400 font-mono">
                    {((generationUsage!.reasoning_tokens || 0) + (evaluationUsage!.reasoning_tokens || 0)).toLocaleString()}
                  </div>
                </div>
              )}
              {((generationUsage!.cached_tokens || 0) + (evaluationUsage!.cached_tokens || 0)) > 0 && (
                <div className="text-right">
                  <div className="text-xs text-light-secondary dark:text-dark-secondary">Cached</div>
                  <div className="text-sm font-bold text-green-600 dark:text-green-400 font-mono">
                    {((generationUsage!.cached_tokens || 0) + (evaluationUsage!.cached_tokens || 0)).toLocaleString()}
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-3">
          {hasGenerationData && (
            <div className="bg-gradient-to-br from-emerald-50 to-emerald-100 dark:from-emerald-900/20 dark:to-emerald-800/20 rounded-lg border border-emerald-200/50 dark:border-emerald-800/50 p-3">
              <div className="flex items-center space-x-2 mb-2">
                <div className="p-1.5 bg-emerald-500/10 dark:bg-emerald-400/10 rounded-md">
                  <svg className="w-4 h-4 text-emerald-600 dark:text-emerald-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                  </svg>
                </div>
                <h3 className="text-sm font-semibold text-light-primary dark:text-dark-primary">
                  {generationUsageTitle}
                </h3>
                <span className="text-xs px-2 py-0.5 rounded-full text-light-secondary dark:text-dark-secondary bg-light-component-subtle dark:bg-dark-component-subtle border border-light-border/60 dark:border-dark-border/60">
                  {generationUsage!.count} {generationUsage!.count === 1 ? 'model' : 'models'}
                </span>
                {generationUsage!.totalGenerations && generationUsage!.count < generationUsage!.totalGenerations && (
                  <span className="text-xs px-2 py-0.5 rounded-full text-amber-700 dark:text-amber-300 bg-amber-100 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800/60">
                    {generationUsage!.totalGenerations - generationUsage!.count} without data
                  </span>
                )}
              </div>
              
              <div className="grid grid-cols-2 gap-2">
                <div className="bg-light-component dark:bg-dark-component rounded-md p-2 border border-light-border/30 dark:border-dark-border/30">
                  <div className="text-xs text-light-secondary dark:text-dark-secondary mb-0.5">Prompt</div>
                  <div className="text-sm font-bold text-light-primary dark:text-dark-primary font-mono">
                    {generationUsage!.prompt_tokens.toLocaleString()}
                  </div>
                </div>
                
                <div className="bg-light-component dark:bg-dark-component rounded-md p-2 border border-light-border/30 dark:border-dark-border/30">
                  <div className="text-xs text-light-secondary dark:text-dark-secondary mb-0.5">Completion</div>
                  <div className="text-sm font-bold text-light-primary dark:text-dark-primary font-mono">
                    {generationUsage!.completion_tokens.toLocaleString()}
                  </div>
                </div>
                
                <div className="bg-light-component dark:bg-dark-component rounded-md p-2 border border-light-border/30 dark:border-dark-border/30">
                  <div className="text-xs text-light-secondary dark:text-dark-secondary mb-0.5">Total</div>
                  <div className="text-sm font-bold text-emerald-600 dark:text-emerald-400 font-mono">
                    {generationUsage!.total_tokens.toLocaleString()}
                  </div>
                </div>
                
                <div className="bg-light-component dark:bg-dark-component rounded-md p-2 border border-light-border/30 dark:border-dark-border/30">
                  <div className="text-xs text-light-secondary dark:text-dark-secondary mb-0.5">Cost</div>
                  <div className="text-sm font-bold text-light-accent dark:text-dark-accent font-mono">
                    ${generationUsage!.cost_credits.toFixed(6)}
                  </div>
                </div>
              </div>
            </div>
          )}
          
          {hasEvaluationData && (
            <div className="bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20 rounded-lg border border-purple-200/50 dark:border-purple-800/50 p-3">
              <div className="flex items-center space-x-2 mb-2">
                <div className="p-1.5 bg-purple-500/10 dark:bg-purple-400/10 rounded-md">
                  <svg className="w-4 h-4 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
                  </svg>
                </div>
                <h3 className="text-sm font-semibold text-light-primary dark:text-dark-primary">
                  Evaluation Usage
                </h3>
                <span className="text-xs px-2 py-0.5 rounded-full text-light-secondary dark:text-dark-secondary bg-light-component-subtle dark:bg-dark-component-subtle border border-light-border/60 dark:border-dark-border/60">
                  {evaluationUsage!.count} {evaluationUsage!.count === 1 ? 'evaluator' : 'evaluators'}
                </span>
                {evaluationUsage!.totalEvaluations && evaluationUsage!.count < evaluationUsage!.totalEvaluations && (
                  <span className="text-xs px-2 py-0.5 rounded-full text-amber-700 dark:text-amber-300 bg-amber-100 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800/60">
                    {evaluationUsage!.totalEvaluations - evaluationUsage!.count} without data
                  </span>
                )}
              </div>
              
              <div className="grid grid-cols-2 gap-2">
                <div className="bg-light-component dark:bg-dark-component rounded-md p-2 border border-light-border/30 dark:border-dark-border/30">
                  <div className="text-xs text-light-secondary dark:text-dark-secondary mb-0.5">Prompt</div>
                  <div className="text-sm font-bold text-light-primary dark:text-dark-primary font-mono">
                    {evaluationUsage!.prompt_tokens.toLocaleString()}
                  </div>
                </div>
                
                <div className="bg-light-component dark:bg-dark-component rounded-md p-2 border border-light-border/30 dark:border-dark-border/30">
                  <div className="text-xs text-light-secondary dark:text-dark-secondary mb-0.5">Completion</div>
                  <div className="text-sm font-bold text-light-primary dark:text-dark-primary font-mono">
                    {evaluationUsage!.completion_tokens.toLocaleString()}
                  </div>
                </div>
                
                <div className="bg-light-component dark:bg-dark-component rounded-md p-2 border border-light-border/30 dark:border-dark-border/30">
                  <div className="text-xs text-light-secondary dark:text-dark-secondary mb-0.5">Total</div>
                  <div className="text-sm font-bold text-purple-600 dark:text-purple-400 font-mono">
                    {evaluationUsage!.total_tokens.toLocaleString()}
                  </div>
                </div>
                
                <div className="bg-light-component dark:bg-dark-component rounded-md p-2 border border-light-border/30 dark:border-dark-border/30">
                  <div className="text-xs text-light-secondary dark:text-dark-secondary mb-0.5">Cost</div>
                  <div className="text-sm font-bold text-light-accent dark:text-dark-accent font-mono">
                    ${evaluationUsage!.cost_credits.toFixed(6)}
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>

      </div>
    </div>
  );
};

export default CombinedUsageSummary; 