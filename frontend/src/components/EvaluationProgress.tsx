import React, { useEffect, useState, useRef, useCallback } from 'react';
import useAgentEvaluationStore, { type EvaluationProgress as StoreEvaluationProgress } from '../store/agentEvaluationStore';
import { XMarkIcon, CheckCircleIcon, ExclamationCircleIcon } from '@heroicons/react/24/outline';
import type { ProgressData, CompletionData, ErrorData } from '../types/progress';

interface EvaluationProgressProps {
  taskId: string;
  onComplete: () => void;
  onClose: () => void;
}

interface ProgressStep {
  number: number;
  name: string;
  status: 'pending' | 'active' | 'completed' | 'error';
  message?: string;
  duration?: number;
}

const EvaluationProgress: React.FC<EvaluationProgressProps> = ({
  taskId,
  onComplete,
  onClose
}) => {
  const { setEvaluationProgress } = useAgentEvaluationStore();
  const [steps, setSteps] = useState<ProgressStep[]>([
    { number: 1, name: 'Loading Task', status: 'pending' },
    { number: 2, name: 'Loading Criteria', status: 'pending' },
    { number: 3, name: 'Preparing Contexts', status: 'pending' },
    { number: 4, name: 'Running Evaluations', status: 'pending' },
    { number: 5, name: 'Aggregating Results', status: 'pending' },
    { number: 6, name: 'Generating Rankings', status: 'pending' }
  ]);
  const [overallProgress, setOverallProgress] = useState(0);
  const [error, setError] = useState<string | null>(null);
  const [logs, setLogs] = useState<string[]>([]);
  const eventSourceRef = useRef<EventSource | null>(null);
  const logsEndRef = useRef<HTMLDivElement | null>(null);

  // Stable reference to addLog function
  const addLog = useCallback((message: string) => {
    setLogs(prev => [...prev, `[${new Date().toLocaleTimeString()}] ${message}`]);
  }, []);

  // Stable reference to onComplete callback
  const onCompleteRef = useRef(onComplete);
  onCompleteRef.current = onComplete;

  // Store callback refs to avoid useEffect re-execution
  const handleProgressUpdateRef = useRef<(data: ProgressData) => void>(() => {});
  const handleCompletionRef = useRef<(data: CompletionData) => void>(() => {});
  const handleErrorRef = useRef<(data: ErrorData) => void>(() => {});
  const addLogRef = useRef<(message: string) => void>(() => {});

  const handleProgressUpdate = useCallback((data: ProgressData) => {
    const progressData: StoreEvaluationProgress = {
      current_step: data.current_step,
      total_steps: data.total_steps,
      progress: data.progress,
      message: data.message,
      details: data.details,
      step_duration: data.step_duration
    };
    setEvaluationProgress(progressData);
    
    const currentStep = data.current_step || 0;
    const totalSteps = data.total_steps || 6;
    const progress = data.progress || (currentStep / totalSteps);
    
    setOverallProgress(Math.round(progress * 100));
    
    // Update steps
    setSteps(prev => prev.map((step, index) => {
      if (index < currentStep - 1) {
        return { ...step, status: 'completed' };
      } else if (index === currentStep - 1) {
        return { 
          ...step, 
          status: 'active', 
          message: data.message,
          duration: data.step_duration
        };
      }
      return step;
    }));

    // Add to logs
    if (data.message) {
      addLog(data.message);
    }
    
    // Handle details
    if (data.details) {
      Object.entries(data.details).forEach(([key, value]) => {
        addLog(`  ${key}: ${JSON.stringify(value)}`);
      });
    }
  }, [setEvaluationProgress, addLog]);

  const handleCompletion = useCallback((data: CompletionData) => {
    setOverallProgress(100);
    setSteps(prev => prev.map(step => ({ ...step, status: 'completed' })));
    addLog(`Evaluation completed in ${(data as any).total_duration?.toFixed(1)}s`);
    
    // Close EventSource
    if (eventSourceRef.current) {
      eventSourceRef.current.close();
    }
    
    // Trigger completion callback after a short delay
    setTimeout(() => {
      onCompleteRef.current();
    }, 1500);
  }, [addLog]);

  const handleError = useCallback((data: ErrorData) => {
    setError(data.error || 'An error occurred during evaluation');
    
    // Mark current step as error
    const errorStep = (data as any).error_at_step || 0;
    setSteps(prev => prev.map((step, index) => {
      if (index === errorStep - 1) {
        return { ...step, status: 'error', message: data.error };
      }
      return step;
    }));
    
    addLog(`ERROR: ${data.error}`);
    
    // Close EventSource
    if (eventSourceRef.current) {
      eventSourceRef.current.close();
    }
  }, [addLog]);

  // Update callback refs whenever callbacks change
  useEffect(() => {
    handleProgressUpdateRef.current = handleProgressUpdate;
    handleCompletionRef.current = handleCompletion;
    handleErrorRef.current = handleError;
    addLogRef.current = addLog;
  }, [handleProgressUpdate, handleCompletion, handleError, addLog]);

  useEffect(() => {
    // Create EventSource connection
    const eventSource = new EventSource(`/api/v1/agent-evaluations/stream/${taskId}`);
    eventSourceRef.current = eventSource;

    eventSource.addEventListener('connected', (event) => {
      const data = JSON.parse(event.data);
      addLogRef.current?.(`Connected to evaluation stream at ${new Date(data.timestamp).toLocaleTimeString()}`);
    });

    eventSource.addEventListener('progress', (event) => {
      const data = JSON.parse(event.data);
      handleProgressUpdateRef.current?.(data);
    });

    eventSource.addEventListener('completed', (event) => {
      const data = JSON.parse(event.data);
      handleCompletionRef.current?.(data);
    });

    eventSource.addEventListener('error', (event) => {
      const data = JSON.parse((event as MessageEvent).data);
      handleErrorRef.current?.(data);
    });

    eventSource.addEventListener('keepalive', () => {
      // Ignore keepalive messages
    });

    eventSource.onerror = (error) => {
      console.error('EventSource error:', error);
      setError('Connection to evaluation stream lost');
      eventSource.close();
    };

    return () => {
      if (eventSourceRef.current) {
        eventSourceRef.current.close();
      }
    };
  }, [taskId]); // Only depend on taskId, not the handler functions

  useEffect(() => {
    // Auto-scroll logs
    logsEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [logs]);

  // Handle ESC key to close modal
  useEffect(() => {
    const handleEscKey = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose();
      }
    };

    document.addEventListener('keydown', handleEscKey);
    return () => document.removeEventListener('keydown', handleEscKey);
  }, [onClose]);

  const getStepIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircleIcon className="w-6 h-6 text-green-500" />;
      case 'error':
        return <ExclamationCircleIcon className="w-6 h-6 text-red-500" />;
      case 'active':
        return (
          <div className="w-6 h-6 border-2 border-blue-500 border-t-transparent rounded-full animate-spin" />
        );
      default:
        return <div className="w-6 h-6 border-2 border-gray-300 dark:border-gray-600 rounded-full" />;
    }
  };

  return (
    <div 
      className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50"
      onClick={(e) => {
        // Close when clicking on backdrop
        if (e.target === e.currentTarget) {
          onClose();
        }
      }}
    >
      <div className="bg-white dark:bg-gray-800 rounded-lg max-w-3xl w-full max-h-[90vh] overflow-hidden flex flex-col">
        {/* Header */}
        <div className="p-6 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <h3 className="text-xl font-semibold text-gray-900 dark:text-white">
              Evaluation Progress
            </h3>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-200 transition-colors"
              title="Close"
            >
              <XMarkIcon className="w-6 h-6" />
            </button>
          </div>
          
          {/* Overall Progress Bar */}
          <div className="mt-4">
            <div className="flex justify-between text-sm text-gray-600 dark:text-gray-400 mb-1">
              <span>Overall Progress</span>
              <span>{overallProgress}%</span>
            </div>
            <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
              <div
                className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${overallProgress}%` }}
              />
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto p-6">
          {/* Error Display */}
          {error && (
            <div className="mb-6 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
              <p className="text-red-800 dark:text-red-200">{error}</p>
            </div>
          )}

          {/* Steps */}
          <div className="space-y-4 mb-6">
            {steps.map((step) => (
              <div key={step.number} className="flex items-start gap-4">
                <div className="flex-shrink-0 mt-0.5">
                  {getStepIcon(step.status)}
                </div>
                <div className="flex-1">
                  <h4 className={`font-medium ${
                    step.status === 'active' ? 'text-blue-600 dark:text-blue-400' :
                    step.status === 'completed' ? 'text-green-600 dark:text-green-400' :
                    step.status === 'error' ? 'text-red-600 dark:text-red-400' :
                    'text-gray-500 dark:text-gray-400'
                  }`}>
                    Step {step.number}: {step.name}
                  </h4>
                  {step.message && (
                    <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                      {step.message}
                    </p>
                  )}
                  {step.duration && (
                    <p className="text-xs text-gray-500 dark:text-gray-500 mt-1">
                      Duration: {step.duration.toFixed(1)}s
                    </p>
                  )}
                </div>
              </div>
            ))}
          </div>

          {/* Logs */}
          <div className="border-t border-gray-200 dark:border-gray-700 pt-4">
            <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Activity Log
            </h4>
            <div className="bg-gray-50 dark:bg-gray-900 rounded-lg p-3 h-48 overflow-y-auto">
              <pre className="text-xs text-gray-600 dark:text-gray-400 font-mono whitespace-pre-wrap">
                {logs.join('\n')}
              </pre>
              <div ref={logsEndRef} />
            </div>
          </div>
        </div>

        {/* Footer */}
        {overallProgress === 100 && (
          <div className="p-6 border-t border-gray-200 dark:border-gray-700">
            <button
              onClick={onComplete}
              className="w-full px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
            >
              View Results
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default EvaluationProgress;