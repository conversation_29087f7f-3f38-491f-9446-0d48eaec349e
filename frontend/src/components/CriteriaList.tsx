import React, { useState, useEffect } from 'react';
import { EvaluationCriteria } from '../store/agentEvaluationStore';
import { 
  BeakerIcon, 
  ChevronDownIcon, 
  ChevronRightIcon,
  ScaleIcon,
  DocumentTextIcon,
  PencilIcon,
  PlusIcon,
  TrashIcon,
  AdjustmentsHorizontalIcon,
  ClockIcon
} from '@heroicons/react/24/outline';
import CriteriaEditModal from './CriteriaEditModal';
import CriteriaHistory from './CriteriaHistory';
import { apiClient } from '../api/apiClient';

interface CriteriaListProps {
  criteria: EvaluationCriteria[];
  canGenerate: boolean;
  onGenerate: () => void;
  isLoading: boolean;
  taskId?: string;
  onCriteriaUpdate?: () => void;
}

const CriteriaList: React.FC<CriteriaListProps> = ({
  criteria,
  canGenerate: _canGenerate,
  onGenerate: _onGenerate,
  isLoading: _isLoading,
  taskId,
  onCriteriaUpdate
}) => {
  const [expandedCriteria, setExpandedCriteria] = useState<Set<string>>(new Set());
  const [editingCriteria, setEditingCriteria] = useState<EvaluationCriteria | null>(null);
  const [isCreating, setIsCreating] = useState(false);
  const [editingWeights, setEditingWeights] = useState<boolean>(false);
  const [criteriaWeights, setCriteriaWeights] = useState<Record<string, number>>({});
  const [historyModal, setHistoryModal] = useState<{ isOpen: boolean; criteriaId: string | null }>({ 
    isOpen: false, 
    criteriaId: null 
  });

  // Initialize criteria weights when component mounts or criteria changes
  useEffect(() => {
    const weights: Record<string, number> = {};
    criteria.forEach(criterion => {
      weights[criterion.id] = criterion.weight;
    });
    setCriteriaWeights(weights);
  }, [criteria]);

  const toggleExpanded = (criterionId: string) => {
    setExpandedCriteria(prev => {
      const newSet = new Set(prev);
      if (newSet.has(criterionId)) {
        newSet.delete(criterionId);
      } else {
        newSet.add(criterionId);
      }
      return newSet;
    });
  };

  const getWeightColor = (weight: number) => {
    if (weight >= 0.3) return 'text-red-600 dark:text-red-400';
    if (weight >= 0.2) return 'text-orange-600 dark:text-orange-400';
    return 'text-blue-600 dark:text-blue-400';
  };

  const handleCreateCriteria = async () => {
    if (!taskId) return;
    
    setIsCreating(true);
    try {
      const newCriteria = {
        name: 'New Criteria',
        description: 'Description of the evaluation criteria',
        weight: 0.5,
        criteria_type: 'quality',
        evaluation_prompt: '',
        source: 'USER_CREATED',
        meta_data: {
          components: []
        }
      };

      const response = await apiClient.post(
        `/agent-evaluations/tasks/${taskId}/criteria`,
        newCriteria
      );

      if (onCriteriaUpdate) {
        onCriteriaUpdate();
      }
      
      // Open edit modal immediately
      setEditingCriteria(response.data);
    } catch (error) {
      console.error('Failed to create criteria:', error);
      alert('Failed to create criteria. Please try again.');
    } finally {
      setIsCreating(false);
    }
  };

  const handleDeleteCriteria = async (criteriaId: string) => {
    if (!confirm('Are you sure you want to delete this criteria?')) return;

    try {
      await apiClient.delete(`/agent-evaluations/criteria/${criteriaId}`);
      
      if (onCriteriaUpdate) {
        onCriteriaUpdate();
      }
    } catch (error) {
      console.error('Failed to delete criteria:', error);
      const err = error as { response?: { data?: { detail?: string } } };
      alert(err.response?.data?.detail || 'Failed to delete criteria.');
    }
  };

  const handleSaveCriteria = (_updatedCriteria: EvaluationCriteria) => {
    setEditingCriteria(null);
    if (onCriteriaUpdate) {
      onCriteriaUpdate();
    }
  };

  // Round to nearest 0.05
  const roundToNearestFive = (value: number): number => {
    return Math.round(value * 20) / 20;
  };

  // Normalize criteria weights
  const handleNormalizeCriteriaWeights = () => {
    const currentTotal = Object.values(criteriaWeights).reduce((sum, weight) => sum + weight, 0);
    
    if (currentTotal === 0 || criteria.length === 0) {
      // Distribute equally if all weights are 0
      const equalWeight = roundToNearestFive(1 / criteria.length);
      const newWeights: Record<string, number> = {};
      criteria.forEach(criterion => {
        newWeights[criterion.id] = equalWeight;
      });
      setCriteriaWeights(newWeights);
      return;
    }

    // Normalize proportionally
    let newWeights: Record<string, number> = {};
    criteria.forEach(criterion => {
      const currentWeight = criteriaWeights[criterion.id] || 0;
      newWeights[criterion.id] = roundToNearestFive(currentWeight / currentTotal);
    });

    // Ensure no weight is 0 (minimum 0.05)
    Object.keys(newWeights).forEach(id => {
      if (newWeights[id] < 0.05) {
        newWeights[id] = 0.05;
      }
    });

    // Calculate the actual sum after rounding
    const actualSum = Object.values(newWeights).reduce((sum, weight) => sum + weight, 0);
    const difference = 1 - actualSum;

    // Adjust the largest weight to compensate for rounding
    if (Math.abs(difference) > 0.001) {
      const maxId = Object.keys(newWeights).reduce((maxId, id) => 
        newWeights[id] > newWeights[maxId] ? id : maxId
      );
      
      const adjustedWeight = newWeights[maxId] + difference;
      if (adjustedWeight >= 0.05) {
        newWeights[maxId] = roundToNearestFive(adjustedWeight);
      }
    }

    setCriteriaWeights(newWeights);
  };

  // Save criteria weights
  const handleSaveCriteriaWeights = async () => {
    try {
      // Update each criterion with new weight
      const updatePromises = criteria.map(criterion => {
        const newWeight = criteriaWeights[criterion.id];
        if (newWeight !== criterion.weight) {
          return apiClient.put(`/agent-evaluations/criteria/${criterion.id}`, {
            ...criterion,
            weight: newWeight
          });
        }
        return null;
      }).filter(promise => promise !== null);

      await Promise.all(updatePromises);
      
      setEditingWeights(false);
      if (onCriteriaUpdate) {
        onCriteriaUpdate();
      }
    } catch (error) {
      console.error('Failed to update criteria weights:', error);
      alert('Failed to update criteria weights. Please try again.');
    }
  };

  // Calculate total weight for display
  const totalWeight = Object.values(criteriaWeights).reduce((sum, weight) => sum + weight, 0);

  if (criteria.length === 0) {
    return (
      <div className="text-center py-12 bg-gray-50 dark:bg-gray-800 rounded-lg">
        <BeakerIcon className="w-16 h-16 mx-auto text-gray-400 dark:text-gray-600 mb-4" />
        <p className="text-gray-500 dark:text-gray-400 mb-4">
          No evaluation criteria generated yet.
        </p>
        <p className="text-sm text-gray-500 dark:text-gray-400">
          Use the "Generate Criteria" button above to create evaluation criteria.
        </p>
      </div>
    );
  }

  return (
    <div>
      {/* Add Criteria Button */}
      {taskId && (
        <div className="mb-4 flex justify-end">
          <button
            onClick={handleCreateCriteria}
            disabled={isCreating}
            className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            <PlusIcon className="w-5 h-5" />
            Add Criteria
          </button>
        </div>
      )}

      {/* Criteria List */}
      <div className="space-y-4">
        {criteria.map((criterion) => {
          const components = criterion.meta_data?.components || [];
          const isExpanded = expandedCriteria.has(criterion.id);

          return (
            <div
              key={criterion.id}
              className="bg-light-component dark:bg-dark-component rounded-lg shadow-sm border border-light-border dark:border-dark-border"
            >
              {/* Criterion Header */}
              <div
                onClick={() => toggleExpanded(criterion.id)}
                className="p-6 cursor-pointer hover:bg-gray-50 dark:hover:bg-dark-component-subtle transition-colors"
              >
                <div className="flex items-start justify-between">
                  <div className="flex items-start gap-4 flex-1">
                    <div className="text-gray-400 dark:text-gray-500 mt-0.5">
                      {isExpanded ? (
                        <ChevronDownIcon className="w-5 h-5" />
                      ) : (
                        <ChevronRightIcon className="w-5 h-5" />
                      )}
                    </div>
                    
                    <div className="flex-1">
                      <h4 className="text-lg font-semibold text-light-primary dark:text-dark-primary">
                        {criterion.name}
                      </h4>
                      <p className="text-sm text-light-secondary dark:text-dark-secondary mt-1">
                        {criterion.description}
                      </p>
                      
                      <div className="flex items-center gap-4 mt-3">
                        <span className="inline-flex items-center gap-1.5 text-sm text-light-secondary dark:text-dark-secondary">
                          <ScaleIcon className="w-4 h-4" />
                          Weight: 
                          {editingWeights ? (
                            <div className="inline-flex items-center gap-2 ml-1">
                              <input
                                type="range"
                                value={criteriaWeights[criterion.id] || criterion.weight}
                                onChange={(e) => {
                                  const value = roundToNearestFive(parseFloat(e.target.value));
                                  setCriteriaWeights(prev => ({
                                    ...prev,
                                    [criterion.id]: value
                                  }));
                                }}
                                min="0"
                                max="1"
                                step="0.05"
                                className="w-24 h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700"
                                onClick={(e) => e.stopPropagation()}
                              />
                              <input
                                type="number"
                                value={criteriaWeights[criterion.id] || criterion.weight}
                                onChange={(e) => {
                                  const value = parseFloat(e.target.value);
                                  if (!isNaN(value)) {
                                    setCriteriaWeights(prev => ({
                                      ...prev,
                                      [criterion.id]: value
                                    }));
                                  }
                                }}
                                min="0"
                                max="1"
                                step="0.05"
                                className="w-16 px-2 py-1 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                                onClick={(e) => e.stopPropagation()}
                              />
                              <span className={`font-medium ${getWeightColor(criteriaWeights[criterion.id] || criterion.weight)}`}>
                                ({((criteriaWeights[criterion.id] || criterion.weight) * 100).toFixed(0)}%)
                              </span>
                            </div>
                          ) : (
                            <span className={`font-medium ${getWeightColor(criterion.weight)}`}>
                              {(criterion.weight * 100).toFixed(0)}%
                            </span>
                          )}
                        </span>
                        <span className="inline-flex items-center gap-1.5 text-sm text-light-secondary dark:text-dark-secondary">
                          <DocumentTextIcon className="w-4 h-4" />
                          {components.length} components
                        </span>
                        {criterion.source && (
                          <span className={`px-2 py-0.5 text-xs rounded-full ${
                            criterion.source === 'GENERATED' ? 'bg-blue-100 text-blue-800' :
                            criterion.source === 'GENERATED_EDITED' ? 'bg-yellow-100 text-yellow-800' :
                            'bg-green-100 text-green-800'
                          }`}>
                            {criterion.source.replace('_', ' ')}
                          </span>
                        )}
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-2">
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        setEditingCriteria(criterion);
                      }}
                      className="p-2 text-gray-500 hover:text-blue-600 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded-lg transition-colors"
                      title="Edit criteria"
                    >
                      <PencilIcon className="w-5 h-5" />
                    </button>
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        setHistoryModal({ isOpen: true, criteriaId: criterion.id });
                      }}
                      className="p-2 text-gray-500 hover:text-purple-600 hover:bg-purple-50 dark:hover:bg-purple-900/20 rounded-lg transition-colors"
                      title="View history"
                    >
                      <ClockIcon className="w-5 h-5" />
                    </button>
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        handleDeleteCriteria(criterion.id);
                      }}
                      className="p-2 text-gray-500 hover:text-red-600 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-lg transition-colors"
                      title="Delete criteria"
                    >
                      <TrashIcon className="w-5 h-5" />
                    </button>
                  </div>
                </div>
              </div>

              {/* Expanded Content */}
              {isExpanded && (
                <div className="border-t border-light-border dark:border-dark-border p-6 bg-light-component-subtle dark:bg-dark-component-subtle">
                  {/* Components */}
                  <div className="mb-6">
                    <h5 className="text-sm font-medium text-light-primary dark:text-dark-primary mb-4">
                      Evaluation Components
                    </h5>
                    
                    <div className="space-y-3">
                      {components.map((component: Record<string, unknown>, index: number) => {
                        const name = String(component.name || '');
                        const description = String(component.description || '');
                        const weight = Number(component.weight || 0);
                        const evaluationMethod = String(component.evaluation_method || '');
                        const scoringRubric = component.scoring_rubric as Record<string, unknown> || {};
                        
                        return (
                          <div
                            key={index}
                            className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700"
                          >
                            <div className="flex items-start justify-between mb-2">
                              <h6 className="font-medium text-gray-900 dark:text-white">
                                {name}
                              </h6>
                              <span className="text-sm text-gray-500 dark:text-gray-400">
                                Weight: {(weight * 100).toFixed(0)}%
                              </span>
                            </div>
                            
                            <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
                              {description}
                            </p>
                            
                            <div className="text-sm">
                              <p className="text-gray-500 dark:text-gray-400 mb-1">
                                <span className="font-medium">Evaluation Method:</span> {evaluationMethod}
                              </p>
                            
                              
                              {/* Scoring Rubric */}
                              <div className="mt-2">
                                <p className="text-gray-500 dark:text-gray-400 mb-1 font-medium">
                                  Scoring Rubric:
                                </p>
                                <div className="space-y-1 ml-4">
                                  {Object.entries(scoringRubric).map(([range, description]) => (
                                    <div key={range} className="text-xs">
                                      <span className="font-mono text-gray-600 dark:text-gray-400">{range}:</span>
                                      <span className="text-gray-700 dark:text-gray-300 ml-2">{String(description)}</span>
                                    </div>
                                  ))}
                                </div>
                              </div>
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  </div>

                  {/* Evaluation Prompt */}
                  <div>
                    <h5 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Evaluation Prompt Template
                    </h5>
                    <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                      <pre className="text-xs text-gray-600 dark:text-gray-400 font-mono whitespace-pre-wrap">
                        {criterion.evaluation_prompt}
                      </pre>
                    </div>
                  </div>

                  {/* Metadata */}
                  {criterion.meta_data?.generated_by && (
                    <div className="mt-4 text-xs text-gray-500 dark:text-gray-400">
                      Generated by: {criterion.meta_data.generated_by}
                    </div>
                  )}
                </div>
              )}
            </div>
          );
        })}
      </div>

      {/* Summary and Weight Controls */}
      <div className="mt-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm font-medium text-blue-700 dark:text-blue-300">
              Total Criteria: {criteria.length}
            </p>
            <p className="text-xs text-blue-600 dark:text-blue-400 mt-1">
              Total weight: {editingWeights ? totalWeight.toFixed(3) : criteria.reduce((sum, c) => sum + c.weight, 0).toFixed(2)}
              {editingWeights && Math.abs(totalWeight - 1) > 0.001 && (
                <span className="ml-2 text-orange-600 dark:text-orange-400">
                  (Should be 1.000)
                </span>
              )}
            </p>
          </div>
          {criteria.length > 0 && (
            <div className="flex items-center gap-2">
              {editingWeights ? (
                <>
                  <button
                    onClick={handleNormalizeCriteriaWeights}
                    className="px-3 py-1 text-sm bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    Normalize Weights
                  </button>
                  <button
                    onClick={() => {
                      // Reset weights to original values
                      const weights: Record<string, number> = {};
                      criteria.forEach(criterion => {
                        weights[criterion.id] = criterion.weight;
                      });
                      setCriteriaWeights(weights);
                      setEditingWeights(false);
                    }}
                    className="px-3 py-1 text-sm bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors"
                  >
                    Cancel
                  </button>
                  <button
                    onClick={handleSaveCriteriaWeights}
                    className="px-3 py-1 text-sm bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                  >
                    Save Weights
                  </button>
                </>
              ) : (
                <button
                  onClick={() => setEditingWeights(true)}
                  className="px-3 py-1 text-sm bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center gap-1"
                >
                  <AdjustmentsHorizontalIcon className="w-4 h-4" />
                  Edit Weights
                </button>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Edit Modal */}
      {editingCriteria && (
        <CriteriaEditModal
          criteria={editingCriteria}
          onSave={handleSaveCriteria}
          onClose={() => setEditingCriteria(null)}
        />
      )}

      {/* History Modal */}
      {historyModal.isOpen && historyModal.criteriaId && (
        <CriteriaHistory
          criteriaId={historyModal.criteriaId}
          isOpen={historyModal.isOpen}
          onClose={() => setHistoryModal({ isOpen: false, criteriaId: null })}
        />
      )}
    </div>
  );
};

export default CriteriaList;