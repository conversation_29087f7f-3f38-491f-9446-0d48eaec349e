import React from 'react';
import { Activity } from 'lucide-react';

interface UsageStatsCardProps {
  title: string;
  modelName: string;
  promptTokens?: number | null;
  completionTokens?: number | null;
  totalTokens?: number | null;
  reasoningTokens?: number | null;
  cachedTokens?: number | null;
  costCredits?: number | null;
  generationId?: string | null;
  iconType?: 'generation' | 'evaluation';
  showUnavailableMessage?: boolean;
  unavailableMessage?: string;
  unavailableSubMessage?: string;
}

const UsageStatsCard: React.FC<UsageStatsCardProps> = ({
  title: _title,
  modelName,
  promptTokens,
  completionTokens,
  totalTokens,
  reasoningTokens,
  cachedTokens,
  costCredits,
  generationId: _generationId,
  iconType = 'generation',
  showUnavailableMessage = false,
  unavailableMessage = "Usage statistics are not available",
  unavailableSubMessage: _unavailableSubMessage = "This feature was added after this was created"
}) => {
  const hasUsageData = (
    promptTokens !== null && promptTokens !== undefined ||
    completionTokens !== null && completionTokens !== undefined ||
    totalTokens !== null && totalTokens !== undefined ||
    reasoningTokens !== null && reasoningTokens !== undefined ||
    cachedTokens !== null && cachedTokens !== undefined ||
    costCredits !== null && costCredits !== undefined
  );

  const getIcon = () => {
    return <Activity className="w-4 h-4 text-light-secondary dark:text-dark-secondary" />;
  };



  if (showUnavailableMessage || !hasUsageData) {
    return (
      <div className="mt-2 pt-2 border-t border-light-border/50 dark:border-dark-border/50">
        <div className="px-1 py-1">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-1.5">
              <div className="p-0.5">
                <Activity className="w-4 h-4 text-light-secondary dark:text-dark-secondary" />
              </div>
              <span className="text-sm px-1 py-0.5 rounded text-light-secondary dark:text-dark-secondary bg-light-component dark:bg-dark-component border border-light-border/60 dark:border-dark-border/60">
                {modelName}
              </span>
            </div>
            
            <div className="text-xs text-light-secondary dark:text-dark-secondary">
              {unavailableMessage}
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="mt-2 pt-2 border-t border-light-border/50 dark:border-dark-border/50">
      <div className="px-1 py-1">
        <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-1.5">
              <div className="p-0.5">
                {getIcon()}
              </div>
              <span className="text-sm px-1 py-0.5 rounded text-light-secondary dark:text-dark-secondary bg-light-component dark:bg-dark-component border border-light-border/60 dark:border-dark-border/60">
                {modelName}
              </span>
            </div>
          
          <div className="flex items-center space-x-3 text-xs">
            {promptTokens !== null && promptTokens !== undefined && (
              <div className="text-center">
                <div className="text-light-secondary dark:text-dark-secondary">Prompt</div>
                <div className="font-bold text-light-primary dark:text-dark-primary font-mono">
                  {promptTokens.toLocaleString()}
                </div>
              </div>
            )}
            
            {completionTokens !== null && completionTokens !== undefined && (
              <div className="text-center">
                <div className="text-light-secondary dark:text-dark-secondary">Completion</div>
                <div className="font-bold text-light-primary dark:text-dark-primary font-mono">
                  {completionTokens.toLocaleString()}
                </div>
              </div>
            )}
            
            {totalTokens !== null && totalTokens !== undefined && (
              <div className="text-center">
                <div className="text-light-secondary dark:text-dark-secondary">Total</div>
                <div className={`font-bold font-mono ${
                  iconType === 'evaluation' 
                    ? 'text-green-600 dark:text-green-400' 
                    : 'text-blue-600 dark:text-blue-400'
                }`}>
                  {totalTokens.toLocaleString()}
                </div>
              </div>
            )}
            
            {reasoningTokens !== null && reasoningTokens !== undefined && reasoningTokens > 0 && (
              <div className="text-center">
                <div className="text-light-secondary dark:text-dark-secondary">Reasoning</div>
                <div className="font-bold text-light-primary dark:text-dark-primary font-mono">
                  {reasoningTokens.toLocaleString()}
                </div>
              </div>
            )}
            
            {cachedTokens !== null && cachedTokens !== undefined && cachedTokens > 0 && (
              <div className="text-center">
                <div className="text-light-secondary dark:text-dark-secondary">Cached</div>
                <div className="font-bold text-green-600 dark:text-green-400 font-mono">
                  {cachedTokens.toLocaleString()}
                </div>
              </div>
            )}
            
            {costCredits !== null && costCredits !== undefined && (
              <div className="text-center">
                <div className="text-light-secondary dark:text-dark-secondary">Cost</div>
                <div className="font-bold text-light-accent dark:text-dark-accent font-mono">
                  {costCredits > 0 
                    ? `$${costCredits.toFixed(6)}`
                    : '$0.000000'
                  }
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default UsageStatsCard; 