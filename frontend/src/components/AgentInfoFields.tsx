import React from 'react';

export interface AgentInfoFieldsProps {
  agentName: string;
  agentVersion: string;
  onChange: (field: 'agentName' | 'agentVersion', value: string) => void;
  errors?: Record<string, string>;
  disabled?: boolean;
  className?: string;
}

const AgentInfoFields: React.FC<AgentInfoFieldsProps> = ({
  agentName,
  agentVersion,
  onChange,
  errors = {},
  disabled = false,
  className = ''
}) => {
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    onChange(name as 'agentName' | 'agentVersion', value);
  };

  return (
    <div className={`grid grid-cols-2 gap-4 ${className}`}>
      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
          Agent Name <span className="text-red-500">*</span>
        </label>
        <input
          type="text"
          name="agentName"
          value={agentName}
          onChange={handleChange}
          placeholder="e.g., GPT-4, Claude 3"
          disabled={disabled}
          className={`w-full px-3 py-2 border rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white transition-colors ${
            errors.agentName 
              ? 'border-red-500 focus:border-red-500 focus:ring-red-500' 
              : 'border-gray-300 dark:border-gray-600 focus:border-blue-500 focus:ring-blue-500'
          } ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
        />
        {errors.agentName && (
          <p className="mt-1 text-sm text-red-600">{errors.agentName}</p>
        )}
      </div>
      
      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
          Agent Version <span className="text-red-500">*</span>
        </label>
        <input
          type="text"
          name="agentVersion"
          value={agentVersion}
          onChange={handleChange}
          placeholder="e.g., turbo-0125, 3.5-sonnet"
          disabled={disabled}
          className={`w-full px-3 py-2 border rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white transition-colors ${
            errors.agentVersion 
              ? 'border-red-500 focus:border-red-500 focus:ring-red-500' 
              : 'border-gray-300 dark:border-gray-600 focus:border-blue-500 focus:ring-blue-500'
          } ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
        />
        {errors.agentVersion && (
          <p className="mt-1 text-sm text-red-600">{errors.agentVersion}</p>
        )}
      </div>
    </div>
  );
};

export default AgentInfoFields; 