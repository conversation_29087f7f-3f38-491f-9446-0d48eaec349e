import React, { useState, useEffect } from 'react';
import { Clock, User, GitBranch, X } from 'lucide-react';
import { apiClient } from '../api/apiClient';
import LoadingSpinner from './LoadingSpinner';

interface CriteriaHistoryProps {
  criteriaId: string;
  isOpen: boolean;
  onClose: () => void;
}

interface HistoryEntry {
  id: string;
  criteria_id: string;
  modified_at: string;
  modified_by_user_id: string;
  source: string;
  changes: {
    field: string;
    old_value: any;
    new_value: any;
  }[];
  version_number: number;
  is_current: boolean;
}

const CriteriaHistory: React.FC<CriteriaHistoryProps> = ({ criteriaId, isOpen, onClose }) => {
  const [history, setHistory] = useState<HistoryEntry[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    if (isOpen) {
      fetchHistory();
    }
  }, [isOpen, criteriaId]);

  const fetchHistory = async () => {
    setIsLoading(true);
    try {
      const response = await apiClient.get(`/agent-evaluations/criteria/${criteriaId}/history`);
      setHistory(response.data);
    } catch (error) {
      console.error('Failed to fetch criteria history:', error);
      setHistory([]);
    } finally {
      setIsLoading(false);
    }
  };

  const getSourceBadgeColor = (source: string) => {
    switch (source) {
      case 'GENERATED':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
      case 'GENERATED_EDITED':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
      case 'USER_CREATED':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200';
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(date);
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-3xl max-h-[80vh] overflow-hidden">
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center gap-3">
            <Clock className="w-6 h-6 text-blue-600 dark:text-blue-400" />
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
              Criteria Modification History
            </h2>
          </div>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        <div className="p-6 overflow-y-auto" style={{ maxHeight: 'calc(80vh - 120px)' }}>
          {isLoading ? (
            <div className="flex justify-center py-8">
              <LoadingSpinner message="Loading history..." />
            </div>
          ) : history.length === 0 ? (
            <div className="text-center py-8 text-gray-500 dark:text-gray-400">
              No modification history available.
            </div>
          ) : (
            <div className="space-y-4">
              {history.map((entry, index) => (
                <div
                  key={entry.id}
                  className={`border rounded-lg p-4 ${
                    entry.is_current
                      ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20 dark:border-blue-600'
                      : 'border-gray-200 dark:border-gray-700'
                  }`}
                >
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex items-center gap-3">
                      <GitBranch className="w-5 h-5 text-gray-400" />
                      <div>
                        <div className="flex items-center gap-2">
                          <span className="font-medium text-gray-900 dark:text-white">
                            Version {entry.version_number}
                          </span>
                          {entry.is_current && (
                            <span className="text-xs px-2 py-1 bg-blue-600 text-white rounded">
                              Current
                            </span>
                          )}
                          <span className={`text-xs px-2 py-1 rounded ${getSourceBadgeColor(entry.source)}`}>
                            {entry.source.replace('_', ' ')}
                          </span>
                        </div>
                        <div className="flex items-center gap-4 mt-1 text-sm text-gray-600 dark:text-gray-400">
                          <span className="flex items-center gap-1">
                            <Clock className="w-4 h-4" />
                            {formatDate(entry.modified_at)}
                          </span>
                          <span className="flex items-center gap-1">
                            <User className="w-4 h-4" />
                            User ID: {entry.modified_by_user_id.slice(0, 8)}...
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>

                  {entry.changes && entry.changes.length > 0 && (
                    <div className="mt-3 space-y-2">
                      <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300">Changes:</h4>
                      <div className="space-y-1">
                        {entry.changes.map((change, changeIndex) => (
                          <div
                            key={changeIndex}
                            className="text-sm bg-gray-100 dark:bg-gray-700 rounded p-2"
                          >
                            <span className="font-medium text-gray-700 dark:text-gray-300">
                              {change.field}:
                            </span>{' '}
                            <span className="text-red-600 dark:text-red-400 line-through">
                              {JSON.stringify(change.old_value)}
                            </span>{' '}
                            →{' '}
                            <span className="text-green-600 dark:text-green-400">
                              {JSON.stringify(change.new_value)}
                            </span>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default CriteriaHistory;