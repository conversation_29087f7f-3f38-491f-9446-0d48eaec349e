import React from 'react';
import { EvaluationReportResponse, Ranking } from '../api/apiClient';
import UsageStatsCard from './UsageStatsCard';

interface ReportViewerProps {
  report: EvaluationReportResponse | null;
  getDisplayId: (generationId: number) => string;
  evaluationUsedBlindIds: boolean;
  blindIdToModelNameMap: Record<string, string>;
  revealGlobalReasoning: boolean;
  onToggleRevealGlobalReasoning: () => void;
  evaluationPrompt?: string;
}

const ReportViewer: React.FC<ReportViewerProps> = ({ 
  report, 
  getDisplayId, 
  evaluationUsedBlindIds, 
  blindIdToModelNameMap, 
  revealGlobalReasoning,
  onToggleRevealGlobalReasoning,
  evaluationPrompt
}) => {
  if (!report || report.rankings.length === 0) {
    return (
      <div className="text-center py-12 bg-light-background/50 dark:bg-dark-background/50 rounded-xl border-2 border-dashed border-light-border dark:border-dark-border">
        <svg className="w-12 h-12 mx-auto text-light-secondary dark:text-dark-secondary mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        </svg>
        <p className="text-light-secondary dark:text-dark-secondary font-medium">No evaluation results available yet</p>
      </div>
    );
  }

  const formatReasoning = (reasoningText: string | null): React.ReactNode => {
    if (!reasoningText) return null;

    if (evaluationUsedBlindIds && revealGlobalReasoning) {
      let parts: (string | React.ReactNode)[] = [reasoningText];

      for (const blindId in blindIdToModelNameMap) {
        const modelName = blindIdToModelNameMap[blindId];
        const newParts: (string | React.ReactNode)[] = [];
        parts.forEach(part => {
          if (typeof part === 'string') {
            const splitByBlindId = part.split(blindId);
            for (let i = 0; i < splitByBlindId.length; i++) {
              newParts.push(splitByBlindId[i]);
              if (i < splitByBlindId.length - 1) {
                newParts.push(
                  <code key={`${blindId}-${i}`} className="bg-gradient-to-r from-light-accent/10 to-blue-500/10 dark:from-dark-accent/10 dark:to-blue-400/10 text-light-accent dark:text-dark-accent px-2 py-1 rounded-md text-sm mx-1 border border-light-accent/20 dark:border-dark-accent/20 font-semibold">
                    {modelName}
                  </code>
                );
              }
            }
          } else {
            newParts.push(part);
          }
        });
        parts = newParts;
      }
      return <pre className="whitespace-pre-wrap text-light-primary dark:text-dark-primary leading-relaxed">{parts.map((part, i) => <React.Fragment key={i}>{part}</React.Fragment>)}</pre>;
    }
    return <pre className="whitespace-pre-wrap text-light-primary dark:text-dark-primary leading-relaxed">{reasoningText}</pre>;
  };

  return (
    <div className="space-y-8">
      {/* Custom Evaluation Prompt Section - only show if custom prompt was used */}
      {evaluationPrompt && (
        <div className="mb-6">
          <div className="flex items-center mb-3">
            <div className="p-1.5 bg-purple-500/10 dark:bg-purple-400/10 rounded-md mr-2">
              <svg className="w-4 h-4 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            </div>
            <h5 className="font-semibold text-sm text-light-primary dark:text-dark-primary">Custom Evaluation Prompt Used</h5>
          </div>
          <div className="text-sm text-light-primary dark:text-dark-primary bg-light-background/30 dark:bg-dark-background/30 p-3 rounded-md">
            <pre className="whitespace-pre-wrap leading-relaxed">{evaluationPrompt}</pre>
          </div>
        </div>
      )}
      
      {report.rankings.map((ranking: Ranking, _rankIndex: number) => {
        return (
          <div key={ranking.id}>
            {ranking.error_message ? (
              <div className="flex items-start space-x-3 p-4 text-red-800 dark:text-red-200 bg-gradient-to-r from-red-50 to-red-100 dark:from-red-900/20 dark:to-red-800/20 rounded-lg border border-red-200 dark:border-red-700/50">
                <svg className="w-5 h-5 text-red-500 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
                <div>
                  <p className="font-semibold text-sm">Error during evaluation:</p>
                  <pre className="whitespace-pre-wrap text-sm mt-2 leading-relaxed">{ranking.error_message}</pre>
                </div>
              </div>
            ) : (
              <>
                {/* Rankings Section */}
                <div className="mb-6">
                  <div className="flex items-center mb-3">
                    <div className="p-1.5 bg-yellow-500/10 dark:bg-yellow-400/10 rounded-md mr-2">
                      <svg className="w-4 h-4 text-yellow-600 dark:text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 00-2-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v6a2 2 0 00-2 2H9z" />
                      </svg>
                    </div>
                    <h5 className="font-semibold text-sm text-light-primary dark:text-dark-primary">Rankings (Best to Worst)</h5>
                  </div>
                  <div className="space-y-2">
                    {ranking.ranked_list_json.map((gen_id: number, index: number) => {
                      const getRankBadge = (rank: number) => {
                        if (rank === 0) return 'bg-yellow-500 text-white'; // Gold
                        if (rank === 1) return 'bg-gray-400 text-white'; // Silver  
                        if (rank === 2) return 'bg-orange-500 text-white'; // Bronze
                        return 'bg-light-component-subtle dark:bg-dark-component-subtle text-light-secondary dark:text-dark-secondary border border-light-border dark:border-dark-border';
                      };
                      
                      return (
                        <div key={index} className="flex items-center py-2 px-3 rounded-md bg-gradient-to-br from-light-component to-light-background dark:from-dark-component dark:to-dark-background hover:bg-light-component-subtle dark:hover:bg-dark-component-subtle transition-colors duration-200 border border-light-border/60 dark:border-dark-border/60 shadow-sm">
                          <div className={`flex items-center justify-center w-5 h-5 rounded-full text-xs font-bold mr-3 ${getRankBadge(index)}`}>
                            {index + 1}
                          </div>
                          <span className="text-sm font-medium text-light-primary dark:text-dark-primary">
                            {getDisplayId(gen_id)}
                          </span>
                        </div>
                      );
                    })}
                  </div>
                </div>
                
                {/* Reasoning Section */}
                <div>
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center">
                      <div className="p-1.5 bg-blue-500/10 dark:bg-blue-400/10 rounded-md mr-2">
                        <svg className="w-4 h-4 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                        </svg>
                      </div>
                      <h5 className="font-semibold text-sm text-light-primary dark:text-dark-primary">Reasoning</h5>
                    </div>
                    
                    {/* Reveal model names checkbox - only show if evaluation used blind IDs */}
                    {evaluationUsedBlindIds && (
                      <div className="flex items-center space-x-2">
                        <input 
                          type="checkbox" 
                          id={`revealModelNames-${ranking.id}`}
                          checked={revealGlobalReasoning} 
                          onChange={onToggleRevealGlobalReasoning}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-blue-300 dark:border-blue-600 rounded transition-all duration-200"
                        />
                        <label htmlFor={`revealModelNames-${ranking.id}`} className="text-xs font-medium text-light-secondary dark:text-dark-secondary select-none cursor-pointer flex items-center">
                          <svg className="w-3.5 h-3.5 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                          </svg>
                          Reveal actual model names
                        </label>
                      </div>
                    )}
                  </div>
                  {formatReasoning(ranking.reasoning_text)}
                </div>
                
                {/* Individual Ranking Usage Statistics */}
                {ranking.generation_id ? (
                  <UsageStatsCard
                    title="Evaluation Usage Statistics"
                    modelName={ranking.evaluator_model_id.split('/').pop() || ''}
                    promptTokens={ranking.prompt_tokens}
                    completionTokens={ranking.completion_tokens}
                    totalTokens={ranking.total_tokens}
                    reasoningTokens={ranking.reasoning_tokens}
                    cachedTokens={ranking.cached_tokens}
                    costCredits={ranking.cost_credits}
                    generationId={ranking.generation_id}
                    iconType="evaluation"
                  />
                ) : (
                  <UsageStatsCard
                    title="Evaluation Usage Statistics"
                    modelName={ranking.evaluator_model_id.split('/').pop() || ''}
                    showUnavailableMessage={true}
                    unavailableMessage="Usage statistics are not available for this evaluation"
                    unavailableSubMessage="This feature was added after this evaluation was performed"
                    iconType="evaluation"
                  />
                )}
              </>
            )}
          </div>
        );
      })}
    </div>
  );
};

export default ReportViewer; 