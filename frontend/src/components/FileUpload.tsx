import React, { useRef } from 'react';
import { CloudArrowUpIcon, DocumentIcon, ArchiveBoxIcon, XMarkIcon } from '@heroicons/react/24/outline';
import { useFileUpload, UseFileUploadOptions } from '../hooks/useFileUpload';

export interface FileUploadProps extends UseFileUploadOptions {
  label?: string;
  description?: string;
  required?: boolean;
  disabled?: boolean;
  className?: string;
  onFilesChange?: (files: File[]) => void;
  error?: string;
}

const FileUpload: React.FC<FileUploadProps> = ({
  label = 'Files',
  description = 'Drag and drop files here, or click to browse',
  required = false,
  disabled = false,
  className = '',
  onFilesChange,
  error: externalError,
  ...uploadOptions
}) => {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const {
    files,
    isDragging,
    error: internalError,
    handleFileChange,
    handleDrop,
    handleDragOver,
    handleDragEnter,
    handleDragLeave,
    removeFile,
    formatFileSize,
    getTotalSize
  } = useFileUpload(uploadOptions);

  const error = externalError || internalError;

  // Notify parent component when files change
  React.useEffect(() => {
    if (onFilesChange) {
      onFilesChange(files);
    }
  }, [files, onFilesChange]);

  const getFileIcon = (file: File) => {
    const ext = file.name.split('.').pop()?.toLowerCase();
    if (['zip', 'tar', 'gz', 'tgz', 'bz2', 'tbz2', 'rar', '7z'].includes(ext || '')) {
      return <ArchiveBoxIcon className="w-5 h-5 text-purple-500" />;
    }
    return <DocumentIcon className="w-5 h-5 text-gray-500" />;
  };

  const getSupportedFormats = () => {
    if (uploadOptions.acceptedTypes && uploadOptions.acceptedTypes.length > 0) {
      return `Supported: ${uploadOptions.acceptedTypes.join(', ').toUpperCase()}`;
    }
    return 'Supports: ZIP, TAR, TAR.GZ, or multiple individual files';
  };

  return (
    <div className={className}>
      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
        {label} {required && <span className="text-red-500">*</span>}
        {uploadOptions.maxSize && (
          <span className="text-xs text-gray-500 ml-2">
            (Max {formatFileSize(uploadOptions.maxSize)} total)
          </span>
        )}
      </label>
      
      {/* Drop Zone */}
      <div
        onDrop={handleDrop}
        onDragOver={handleDragOver}
        onDragEnter={handleDragEnter}
        onDragLeave={handleDragLeave}
        className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
          disabled 
            ? 'opacity-50 cursor-not-allowed border-gray-200 dark:border-gray-700' 
            : isDragging
              ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
              : error 
                ? 'border-red-500 bg-red-50 dark:bg-red-900/20' 
                : 'border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500'
        }`}
      >
        <CloudArrowUpIcon className={`w-12 h-12 mx-auto mb-4 ${
          isDragging ? 'text-blue-500' : 'text-gray-400'
        }`} />
        <p className="text-gray-600 dark:text-gray-400 mb-2">
          {description}
        </p>
        <p className="text-xs text-gray-500 mb-4">
          {getSupportedFormats()}
        </p>
        <input
          ref={fileInputRef}
          type="file"
          multiple={uploadOptions.multiple !== false}
          onChange={handleFileChange}
          disabled={disabled}
          className="hidden"
          accept={uploadOptions.acceptedTypes?.map(type => `.${type}`).join(',')}
        />
        <button
          type="button"
          onClick={() => fileInputRef.current?.click()}
          disabled={disabled}
          className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-400 transition-colors"
        >
          Choose Files
        </button>
      </div>
      
      {error && (
        <p className="mt-1 text-sm text-red-600">{error}</p>
      )}
      
      {/* File List */}
      {files.length > 0 && (
        <div className="mt-4 space-y-2">
          {files.map((file, index) => (
            <div
              key={`${file.name}-${index}`}
              className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-900 rounded-lg"
            >
              <div className="flex items-center gap-3">
                {getFileIcon(file)}
                <div>
                  <p className="text-sm font-medium text-gray-900 dark:text-white">
                    {file.name}
                  </p>
                  <p className="text-xs text-gray-500">
                    {formatFileSize(file.size)}
                  </p>
                </div>
              </div>
              {!disabled && (
                <button
                  type="button"
                  onClick={() => removeFile(index)}
                  className="text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300 transition-colors"
                  aria-label={`Remove ${file.name}`}
                >
                  <XMarkIcon className="w-5 h-5" />
                </button>
              )}
            </div>
          ))}
          <p className="text-sm text-gray-600 dark:text-gray-400">
            Total: {formatFileSize(getTotalSize())}
            {uploadOptions.maxFiles && ` (${files.length}/${uploadOptions.maxFiles} files)`}
          </p>
        </div>
      )}
    </div>
  );
};

export default FileUpload; 