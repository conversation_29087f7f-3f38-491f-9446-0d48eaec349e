import React, { useEffect, useState } from 'react';
import { Outlet, useParams, useLocation } from 'react-router-dom';
import { PanelLeftOpen, PanelLeftClose, SquarePen, PanelLeft, FolderIcon } from 'lucide-react';
import type { EvaluationSidebarProps } from '../types/evaluation';

interface EvaluationLayoutProps {
  // Sidebar props
  sidebarComponent: React.ComponentType<EvaluationSidebarProps>;
  sidebarProps: EvaluationSidebarProps;
  
  // Navigation handlers
  onNewEvaluation: () => void;
  onAllEvaluations?: () => void;
  
  // Breadcrumb configuration
  breadcrumbs: {
    home: string;
    all: string;
    new: string;
    details: string;
  };
  
  // Route patterns
  routes: {
    home: string;
    all?: string;
    new: string;
  };
}

const EvaluationLayout: React.FC<EvaluationLayoutProps> = ({
  sidebarComponent: SidebarComponent,
  sidebarProps,
  onNewEvaluation,
  onAllEvaluations,
  breadcrumbs,
  routes
}) => {
  const location = useLocation();
  const { taskId } = useParams<{ taskId: string }>();

  const [isDesktopSidebarCollapsed, setIsDesktopSidebarCollapsed] = useState(false);
  const [isMobileSidebarOpen, setIsMobileSidebarOpen] = useState(false);

  const handleNewEvaluation = () => {
    onNewEvaluation();
    setIsMobileSidebarOpen(false);
  };

  const handleAllEvaluations = () => {
    if (onAllEvaluations) {
      onAllEvaluations();
      setIsMobileSidebarOpen(false);
    }
  };

  // Close mobile sidebar when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (isMobileSidebarOpen &&
          (event.target as Element)?.closest('.mobile-sidebar-content') === null &&
          (event.target as Element)?.closest('.mobile-menu-button') === null) {
        setIsMobileSidebarOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [isMobileSidebarOpen]);

  // Get current breadcrumb
  const getCurrentBreadcrumb = () => {
    if (location.pathname === routes.home) {
      return breadcrumbs.home;
    }
    if (routes.all && location.pathname === routes.all) {
      return breadcrumbs.all;
    }
    if (location.pathname === routes.new) {
      return breadcrumbs.new;
    }
    if (taskId) {
      return breadcrumbs.details;
    }
    return '';
  };

  // Enhanced sidebar props with current task ID
  const enhancedSidebarProps = {
    ...sidebarProps,
    currentItemId: taskId || null,
    onToggleDesktopCollapse: () => setIsDesktopSidebarCollapsed(true)
  };

  return (
    <div className="flex w-full h-full">
      {/* Desktop Sidebar */}
      {!isDesktopSidebarCollapsed && (
        <SidebarComponent
          {...enhancedSidebarProps}
          className="hidden md:flex w-64 flex-col glass glass-border transition-all duration-300 ease-in-out"
        />
      )}

      {/* Mobile Sidebar Overlay */}
      {isMobileSidebarOpen && (
        <>
          {/* Backdrop */}
          <div className="fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden" onClick={() => setIsMobileSidebarOpen(false)} />
          
          {/* Mobile Sidebar */}
          <div className="mobile-sidebar-content fixed inset-y-0 left-0 w-64 glass glass-border z-50 md:hidden h-screen overflow-hidden">
            <SidebarComponent {...enhancedSidebarProps} />
          </div>
        </>
      )}

      {/* Main Content Area */}
      <div className="flex-1 flex flex-col min-w-0">
        {/* Sidebar Toggle Bar */}
        <div className="glass glass-border border-b px-4 py-2">
          <div className="flex items-center gap-3">
            {/* Mobile Menu Button */}
            <button
              onClick={() => setIsMobileSidebarOpen(!isMobileSidebarOpen)}
              className="mobile-menu-button md:hidden p-2 rounded-lg text-light-secondary dark:text-dark-secondary hover:text-light-primary dark:hover:text-dark-primary hover:bg-light-component-subtle dark:hover:bg-dark-component-subtle transition-colors"
              aria-label={isMobileSidebarOpen ? "Close sidebar" : "Open sidebar"}
            >
              {isMobileSidebarOpen ? <PanelLeftClose size={16} /> : <PanelLeft size={16} />}
            </button>

            {/* Desktop - Open Sidebar Button (shown when collapsed) */}
            {isDesktopSidebarCollapsed && (
              <button
                onClick={() => setIsDesktopSidebarCollapsed(false)}
                className="hidden md:block p-2 rounded-lg text-light-secondary dark:text-dark-secondary hover:text-light-primary dark:hover:text-dark-primary hover:bg-light-component-subtle dark:hover:bg-dark-component-subtle transition-colors"
                title="Open sidebar"
                aria-label="Open sidebar"
              >
                <PanelLeftOpen size={16} />
              </button>
            )}
            
            {/* Desktop - New Evaluation Icon Button (shown when sidebar collapsed) */}
            {isDesktopSidebarCollapsed && (
               <button
                onClick={handleNewEvaluation}
                title="New Evaluation"
                className="hidden md:block p-2 rounded-lg text-light-secondary dark:text-dark-secondary hover:text-light-primary dark:hover:text-dark-primary hover:bg-light-component-subtle dark:hover:bg-dark-component-subtle transition-colors"
              >
                <SquarePen size={16} />
              </button>
            )}

            {/* Desktop - All Evaluations Icon Button (shown when sidebar collapsed) */}
            {isDesktopSidebarCollapsed && onAllEvaluations && (
               <button
                onClick={handleAllEvaluations}
                title="All Evaluations"
                className="hidden md:block p-2 rounded-lg text-light-secondary dark:text-dark-secondary hover:text-light-primary dark:hover:text-dark-primary hover:bg-light-component-subtle dark:hover:bg-dark-component-subtle transition-colors"
              >
                <FolderIcon size={16} />
              </button>
            )}

            {/* Breadcrumb */}
            <div className="flex-1">
              <h2 className="text-sm font-medium text-light-secondary dark:text-dark-secondary">
                {getCurrentBreadcrumb()}
              </h2>
            </div>
          </div>
        </div>
        
        {/* Main Content */}
        <main className="flex-1 overflow-y-auto p-4">
          <Outlet />
        </main>
      </div>
    </div>
  );
};

export default EvaluationLayout; 