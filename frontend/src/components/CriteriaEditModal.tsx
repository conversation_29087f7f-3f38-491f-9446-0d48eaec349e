import React, { useState, useEffect } from 'react';
import { X, Plus, Trash2, GripVertical, Save, Copy, Library, AlertCircle } from 'lucide-react';
import { apiClient } from '../api/apiClient';
import { EvaluationCriteria } from '../store/agentEvaluationStore';
import ComponentSelector from './ComponentSelector';
import { getUserId } from '../utils/userId';

interface Component {
  name: string;
  description: string;
  weight: number;
  evaluation_method: string;
  scoring_rubric: {
    '0-30': string;
    '31-60': string;
    '61-80': string;
    '81-100': string;
  };
}

interface CriteriaEditModalProps {
  criteria: EvaluationCriteria;
  onSave: (updatedCriteria: EvaluationCriteria) => void;
  onClose: () => void;
}

const CriteriaEditModal: React.FC<CriteriaEditModalProps> = ({
  criteria,
  onSave,
  onClose,
}) => {
  const [editedCriteria, setEditedCriteria] = useState<EvaluationCriteria>(criteria);
  
  // Convert flexible scoring_rubric to fixed structure
  const convertComponents = (components: Array<Record<string, unknown>>): Component[] => {
    return components.map(comp => {
      const rubric = comp.scoring_rubric as Record<string, string> | undefined;
      return {
        name: comp.name as string || '',
        description: comp.description as string || '',
        weight: comp.weight as number || 0,
        evaluation_method: comp.evaluation_method as string || '',
        scoring_rubric: {
          '0-30': rubric?.['0-30'] || 'Poor performance',
          '31-60': rubric?.['31-60'] || 'Below average performance', 
          '61-80': rubric?.['61-80'] || 'Good performance',
          '81-100': rubric?.['81-100'] || 'Excellent performance'
        }
      };
    });
  };

  const [components, setComponents] = useState<Component[]>(
    convertComponents(criteria.meta_data?.components || [])
  );
  const [isSaving, setIsSaving] = useState(false);
  const [activeTab, setActiveTab] = useState<'details' | 'components' | 'preview'>('details');
  const [showComponentSelector, setShowComponentSelector] = useState(false);
  const [hasEvaluationResults, setHasEvaluationResults] = useState(false);
  const [isCheckingResults, setIsCheckingResults] = useState(true);

  // Calculate total weight for display
  const totalWeight = components.reduce((sum, comp) => sum + comp.weight, 0);

  // Check if criteria has evaluation results
  useEffect(() => {
    const checkEvaluationResults = async () => {
      setIsCheckingResults(true);
      try {
        // Check if this criteria has been used in evaluations
        const response = await apiClient.get(`/agent-evaluations/criteria/${criteria.id}/usage`);
        setHasEvaluationResults(response.data.has_results || false);
      } catch (error) {
        console.error('Failed to check evaluation results:', error);
        // Assume no results on error
        setHasEvaluationResults(false);
      } finally {
        setIsCheckingResults(false);
      }
    };

    checkEvaluationResults();
  }, [criteria.id]);

  // Round to nearest 0.05
  const roundToNearestFive = (value: number): number => {
    return Math.round(value * 20) / 20;
  };

  // Manual normalization function
  const handleNormalizeWeights = () => {
    if (components.length === 0) return;
    
    // Calculate total current weight
    const currentTotal = components.reduce((sum, comp) => sum + comp.weight, 0);
    
    if (currentTotal === 0) {
      // If all weights are 0, distribute equally
      const equalWeight = roundToNearestFive(1 / components.length);
      const normalizedComponents = components.map(comp => ({
        ...comp,
        weight: equalWeight
      }));
      setComponents(normalizedComponents);
      return;
    }
    
    // Normalize proportionally and round to nearest 0.05
    let normalizedComponents = components.map(comp => ({
      ...comp,
      weight: roundToNearestFive(comp.weight / currentTotal)
    }));
    
    // Ensure no weight is 0 (set minimum to 0.05)
    normalizedComponents = normalizedComponents.map(comp => ({
      ...comp,
      weight: Math.max(0.05, comp.weight)
    }));
    
    // Calculate the actual sum after rounding and minimum adjustment
    const actualSum = normalizedComponents.reduce((sum, comp) => sum + comp.weight, 0);
    const difference = 1 - actualSum;
    
    // If there's a difference, adjust the component with the largest weight
    if (Math.abs(difference) > 0.001) {
      const maxIndex = normalizedComponents.reduce((maxIdx, comp, idx, arr) => 
        comp.weight > arr[maxIdx].weight ? idx : maxIdx, 0
      );
      
      const newWeight = normalizedComponents[maxIndex].weight + difference;
      // Ensure the adjusted weight doesn't go below 0.05
      if (newWeight >= 0.05) {
        normalizedComponents[maxIndex] = {
          ...normalizedComponents[maxIndex],
          weight: roundToNearestFive(newWeight)
        };
      }
    }
    
    setComponents(normalizedComponents);
  };

  const handleSave = async () => {
    // Validation 1: Check required fields
    if (!editedCriteria.name || editedCriteria.name.trim() === '') {
      alert('Criteria name is required.');
      setActiveTab('details');
      return;
    }

    if (!editedCriteria.description || editedCriteria.description.trim() === '') {
      alert('Criteria description is required.');
      setActiveTab('details');
      return;
    }

    if (editedCriteria.weight <= 0 || editedCriteria.weight > 1) {
      alert('Criteria weight must be between 0 and 1.');
      setActiveTab('details');
      return;
    }

    // Validation 2: Check if components exist
    if (components.length === 0) {
      alert('At least one component is required. Please add components before saving.');
      setActiveTab('components');
      return;
    }

    // Validation 3: Check component required fields
    for (let i = 0; i < components.length; i++) {
      const comp = components[i];
      if (!comp.name || comp.name.trim() === '') {
        alert(`Component ${i + 1}: Name is required.`);
        setActiveTab('components');
        return;
      }
      if (!comp.description || comp.description.trim() === '') {
        alert(`Component "${comp.name}": Description is required.`);
        setActiveTab('components');
        return;
      }
      if (!comp.evaluation_method || comp.evaluation_method.trim() === '') {
        alert(`Component "${comp.name}": Evaluation method is required.`);
        setActiveTab('components');
        return;
      }
      if (comp.weight <= 0) {
        alert(`Component "${comp.name}": Weight must be greater than 0.`);
        setActiveTab('components');
        return;
      }
    }

    // Check if weights need normalization
    const currentTotal = components.reduce((sum, comp) => sum + comp.weight, 0);
    let finalComponents = components;
    
    if (Math.abs(currentTotal - 1) > 0.001) {
      const shouldNormalize = confirm(
        `Component weights sum to ${currentTotal.toFixed(3)} instead of 1.000. ` +
        'Do you want to automatically normalize the weights?'
      );
      
      if (shouldNormalize) {
        // Apply the same normalization logic as manual normalize
        let normalizedComponents = components.map(comp => ({
          ...comp,
          weight: roundToNearestFive(comp.weight / currentTotal)
        }));
        
        // Ensure no weight is 0 (set minimum to 0.05)
        normalizedComponents = normalizedComponents.map(comp => ({
          ...comp,
          weight: Math.max(0.05, comp.weight)
        }));
        
        // Calculate the actual sum and adjust the largest component
        const actualSum = normalizedComponents.reduce((sum, comp) => sum + comp.weight, 0);
        const difference = 1 - actualSum;
        
        if (Math.abs(difference) > 0.001) {
          const maxIndex = normalizedComponents.reduce((maxIdx, comp, idx, arr) => 
            comp.weight > arr[maxIdx].weight ? idx : maxIdx, 0
          );
          
          const newWeight = normalizedComponents[maxIndex].weight + difference;
          if (newWeight >= 0.05) {
            normalizedComponents[maxIndex] = {
              ...normalizedComponents[maxIndex],
              weight: roundToNearestFive(newWeight)
            };
          }
        }
        
        finalComponents = normalizedComponents;
        setComponents(finalComponents);
      }
    }

    // Check if we need to handle version control
    let saveAsNewVersion = false;
    if (hasEvaluationResults && !isCheckingResults) {
      const result = confirm(
        'This criteria has been used in evaluations. Would you like to:\n\n' +
        '• OK - Save as a new version (recommended)\n' +
        '• Cancel - Overwrite the existing criteria\n\n' +
        'Note: Saving as a new version will preserve the evaluation history.'
      );
      saveAsNewVersion = result;
    }

    setIsSaving(true);
    try {
      const updatedData = {
        name: editedCriteria.name,
        description: editedCriteria.description,
        weight: editedCriteria.weight,
        evaluation_prompt: editedCriteria.evaluation_prompt,
        meta_data: {
          ...editedCriteria.meta_data,
          components: finalComponents
        },
        modified_by_user_id: getUserId(),
        source: criteria.source === 'GENERATED' ? 'GENERATED_EDITED' : criteria.source
      };

      let response;
      if (saveAsNewVersion) {
        // Create a new version
        response = await apiClient.post(
          `/agent-evaluations/tasks/${criteria.task_id}/criteria`,
          {
            ...updatedData,
            original_criteria_id: criteria.id,
            source: 'USER_CREATED'
          }
        );
      } else {
        // Update existing criteria
        response = await apiClient.put(
          `/agent-evaluations/criteria/${criteria.id}`,
          updatedData
        );
      }

      onSave(response.data);
      onClose();
    } catch (error) {
      console.error('Failed to save criteria:', error);
      alert('Failed to save criteria. Please try again.');
    } finally {
      setIsSaving(false);
    }
  };

  const handleAddComponent = () => {
    const newComponent: Component = {
      name: 'New Component',
      description: 'Description',
      weight: 0.5,
      evaluation_method: 'Review and score',
      scoring_rubric: {
        '0-30': 'Poor performance',
        '31-60': 'Below average performance',
        '61-80': 'Good performance',
        '81-100': 'Excellent performance'
      }
    };
    setComponents([...components, newComponent]);
  };

  const handleDeleteComponent = (index: number) => {
    setComponents(components.filter((_, i) => i !== index));
  };

  const handleComponentChange = (index: number, field: keyof Component, value: string | number | Record<string, string>) => {
    const updated = [...components];
    updated[index] = { ...updated[index], [field]: value };
    setComponents(updated);
  };

  const handleRubricChange = (index: number, range: string, value: string) => {
    const updated = [...components];
    updated[index] = {
      ...updated[index],
      scoring_rubric: {
        ...updated[index].scoring_rubric,
        [range]: value
      }
    };
    setComponents(updated);
  };

  const handleSaveToLibrary = async (componentIndex: number) => {
    try {
      await apiClient.post(
        `/agent-evaluations/library/components/from-criteria/${criteria.id}`,
        { component_index: componentIndex }
      );
      alert('Component saved to library successfully!');
    } catch (error) {
      console.error('Failed to save component to library:', error);
      alert('Failed to save component to library.');
    }
  };

  const handleImportComponents = (importedComponents: Array<{
    id: string;
    name: string;
    description: string;
    evaluation_method: string;
    scoring_rubric: Record<string, string>;
  }>) => {
    // Convert imported components to the format we need
    const newComponents: Component[] = importedComponents.map(comp => ({
      name: comp.name,
      description: comp.description,
      weight: 0.2, // Default weight
      evaluation_method: comp.evaluation_method,
      scoring_rubric: {
        '0-30': comp.scoring_rubric['0-30'] || 'Poor performance',
        '31-60': comp.scoring_rubric['31-60'] || 'Below average performance',
        '61-80': comp.scoring_rubric['61-80'] || 'Good performance',
        '81-100': comp.scoring_rubric['81-100'] || 'Excellent performance'
      }
    }));

    setComponents([...components, ...newComponents]);
    setShowComponentSelector(false);
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-hidden">
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center space-x-4">
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
              Edit Evaluation Criteria
            </h2>
            {criteria.source && (
              <span className={`px-2 py-1 text-xs rounded-full ${
                criteria.source === 'GENERATED' ? 'bg-blue-100 text-blue-800' :
                criteria.source === 'GENERATED_EDITED' ? 'bg-yellow-100 text-yellow-800' :
                'bg-green-100 text-green-800'
              }`}>
                {criteria.source.replace('_', ' ')}
              </span>
            )}
            {hasEvaluationResults && !isCheckingResults && (
              <span className="px-2 py-1 text-xs rounded-full bg-purple-100 text-purple-800 flex items-center gap-1">
                <AlertCircle className="w-3 h-3" />
                Has Results
              </span>
            )}
          </div>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        <div className="flex border-b border-gray-200 dark:border-gray-700">
          <button
            onClick={() => setActiveTab('details')}
            className={`px-6 py-3 font-medium ${
              activeTab === 'details'
                ? 'text-blue-600 border-b-2 border-blue-600'
                : 'text-gray-500 hover:text-gray-700'
            }`}
          >
            Details
          </button>
          <button
            onClick={() => setActiveTab('components')}
            className={`px-6 py-3 font-medium ${
              activeTab === 'components'
                ? 'text-blue-600 border-b-2 border-blue-600'
                : 'text-gray-500 hover:text-gray-700'
            }`}
          >
            Components ({components.length})
          </button>
          <button
            onClick={() => setActiveTab('preview')}
            className={`px-6 py-3 font-medium ${
              activeTab === 'preview'
                ? 'text-blue-600 border-b-2 border-blue-600'
                : 'text-gray-500 hover:text-gray-700'
            }`}
          >
            Preview
          </button>
        </div>

        <div className="p-6 overflow-y-auto" style={{ maxHeight: 'calc(90vh - 200px)' }}>
          {activeTab === 'details' && (
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Name
                </label>
                <input
                  type="text"
                  value={editedCriteria.name}
                  onChange={(e) => setEditedCriteria({ ...editedCriteria, name: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Description
                </label>
                <textarea
                  value={editedCriteria.description}
                  onChange={(e) => setEditedCriteria({ ...editedCriteria, description: e.target.value })}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Weight (0-1)
                </label>
                <input
                  type="number"
                  value={editedCriteria.weight}
                  onChange={(e) => setEditedCriteria({ ...editedCriteria, weight: parseFloat(e.target.value) })}
                  min="0"
                  max="1"
                  step="0.1"
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Evaluation Prompt Template
                </label>
                <textarea
                  value={editedCriteria.evaluation_prompt || ''}
                  onChange={(e) => setEditedCriteria({ ...editedCriteria, evaluation_prompt: e.target.value })}
                  rows={4}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Template for evaluation prompts..."
                />
              </div>
            </div>
          )}

          {activeTab === 'components' && (
            <div className="space-y-4">
              {/* Weight Summary and Controls */}
              {components.length > 0 && (
                <div className={`rounded-lg p-4 ${
                  Math.abs(totalWeight - 1) > 0.001 
                    ? 'bg-orange-50 dark:bg-orange-900/20 border border-orange-200 dark:border-orange-800' 
                    : 'bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800'
                }`}>
                  <div className="flex items-center justify-between">
                    <div>
                      <span className={`text-sm font-medium ${
                        Math.abs(totalWeight - 1) > 0.001
                          ? 'text-orange-700 dark:text-orange-300'
                          : 'text-green-700 dark:text-green-300'
                      }`}>
                        Total Weight: {totalWeight.toFixed(3)}
                        {Math.abs(totalWeight - 1) > 0.001 ? ' ⚠️' : ' ✓'}
                      </span>
                      {Math.abs(totalWeight - 1) > 0.001 && (
                        <span className="ml-2 text-xs text-orange-600 dark:text-orange-400">
                          (Must be 1.000 to save)
                        </span>
                      )}
                    </div>
                    <button
                      onClick={handleNormalizeWeights}
                      className="px-3 py-1 text-sm bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                    >
                      Normalize Weights
                    </button>
                  </div>
                </div>
              )}

              {/* Validation Warning */}
              {components.length === 0 && (
                <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
                  <p className="text-sm text-red-700 dark:text-red-300">
                    ⚠️ At least one component is required to save criteria.
                  </p>
                </div>
              )}

              {components.map((component, index) => (
                <div key={index} className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex items-center">
                      <GripVertical className="w-5 h-5 text-gray-400 mr-2 cursor-move" />
                      <input
                        type="text"
                        value={component.name}
                        onChange={(e) => handleComponentChange(index, 'name', e.target.value)}
                        className="text-lg font-medium bg-transparent border-b border-gray-300 dark:border-gray-600 text-gray-900 dark:text-white focus:border-blue-500 focus:outline-none"
                      />
                    </div>
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={() => handleSaveToLibrary(index)}
                        className="text-gray-500 hover:text-blue-600"
                        title="Save to library"
                      >
                        <Copy className="w-5 h-5" />
                      </button>
                      <button
                        onClick={() => handleDeleteComponent(index)}
                        className="text-gray-500 hover:text-red-600"
                      >
                        <Trash2 className="w-5 h-5" />
                      </button>
                    </div>
                  </div>

                  <div className="space-y-3">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Description
                      </label>
                      <textarea
                        value={component.description}
                        onChange={(e) => handleComponentChange(index, 'description', e.target.value)}
                        rows={2}
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                      />
                    </div>

                    <div className="space-y-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                          Weight: {component.weight.toFixed(2)}
                        </label>
                        <div className="flex items-center space-x-2">
                          <input
                            type="range"
                            value={component.weight}
                            onChange={(e) => {
                              const value = Math.round(parseFloat(e.target.value) * 20) / 20; // Round to nearest 0.05
                              handleComponentChange(index, 'weight', value);
                            }}
                            min="0"
                            max="1"
                            step="0.05"
                            className="flex-1 h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700"
                          />
                          <input
                            type="number"
                            value={component.weight}
                            onChange={(e) => handleComponentChange(index, 'weight', parseFloat(e.target.value))}
                            min="0"
                            max="1"
                            step="0.05"
                            className="w-20 px-2 py-1 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                          />
                        </div>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                          Evaluation Method
                        </label>
                        <input
                          type="text"
                          value={component.evaluation_method}
                          onChange={(e) => handleComponentChange(index, 'evaluation_method', e.target.value)}
                          className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                        />
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Scoring Rubric
                      </label>
                      <div className="space-y-2">
                        {Object.entries(component.scoring_rubric).map(([range, description]) => (
                          <div key={range} className="flex items-center space-x-2">
                            <span className="text-sm font-medium w-20">{range}:</span>
                            <input
                              type="text"
                              value={description}
                              onChange={(e) => handleRubricChange(index, range, e.target.value)}
                              className="flex-1 px-3 py-1 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                            />
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              ))}

              <div className="flex gap-3">
                <button
                  onClick={handleAddComponent}
                  className="flex-1 py-3 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg text-gray-500 dark:text-gray-400 hover:border-gray-400 hover:text-gray-600 flex items-center justify-center"
                >
                  <Plus className="w-5 h-5 mr-2" />
                  Add Component
                </button>
                <button
                  onClick={() => setShowComponentSelector(true)}
                  className="flex-1 py-3 border-2 border-dashed border-blue-300 dark:border-blue-600 rounded-lg text-blue-500 dark:text-blue-400 hover:border-blue-400 hover:text-blue-600 flex items-center justify-center"
                >
                  <Library className="w-5 h-5 mr-2" />
                  Import from Library
                </button>
              </div>
            </div>
          )}

          {activeTab === 'preview' && (
            <div className="space-y-4">
              <div className="bg-gray-50 dark:bg-gray-900 rounded-lg p-6">
                <h3 className="text-lg font-semibold mb-2">{editedCriteria.name}</h3>
                <p className="text-gray-600 dark:text-gray-400 mb-4">{editedCriteria.description}</p>
                <p className="text-sm text-gray-500">Weight: {editedCriteria.weight}</p>

                {components.length > 0 && (
                  <div className="mt-6 space-y-4">
                    <h4 className="font-medium text-gray-700 dark:text-gray-300">Components:</h4>
                    {components.map((component, index) => (
                      <div key={index} className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                        <div className="flex justify-between items-start mb-2">
                          <h5 className="font-medium">{component.name}</h5>
                          <span className="text-sm text-gray-500">Weight: {component.weight}</span>
                        </div>
                        <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">{component.description}</p>
                        <p className="text-xs text-gray-500">Method: {component.evaluation_method}</p>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          )}
        </div>

        <div className="flex justify-end space-x-3 p-6 border-t border-gray-200 dark:border-gray-700">
          <button
            onClick={onClose}
            className="px-4 py-2 text-gray-700 dark:text-gray-300 bg-gray-200 dark:bg-gray-700 rounded-md hover:bg-gray-300 dark:hover:bg-gray-600"
          >
            Cancel
          </button>
          <button
            onClick={handleSave}
            disabled={isSaving}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
          >
            {isSaving ? (
              <>
                <span className="animate-spin mr-2">⌛</span>
                Saving...
              </>
            ) : (
              <>
                <Save className="w-4 h-4 mr-2" />
                Save Changes
              </>
            )}
          </button>
        </div>
      </div>

      {/* Component Selector Modal */}
      {showComponentSelector && (
        <ComponentSelector
          onSelect={handleImportComponents}
          onClose={() => setShowComponentSelector(false)}
          existingComponentIds={[]} // We don't have IDs for current components
        />
      )}
    </div>
  );
};

export default CriteriaEditModal;