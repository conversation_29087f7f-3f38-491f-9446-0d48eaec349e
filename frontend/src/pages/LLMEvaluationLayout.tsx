import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import useTaskStore from '../store/taskStore';
import ErrorBoundary from '../components/ErrorBoundary';
import EvaluationLayout from '../components/EvaluationLayout';
import EvaluationSidebar from '../components/EvaluationSidebar';
import type { EvaluationItem } from '../types/evaluation';

const LLMEvaluationLayout: React.FC = () => {
  const navigate = useNavigate();
  const {
    setSelectedTaskId,
    taskHistory,
    isLoadingHistory,
    loadTaskHistory,
    loadAvailableModels,
    errorLoadingHistory,
    deleteTask,
  } = useTaskStore();

  useEffect(() => {
    loadTaskHistory();
    loadAvailableModels();
  }, [loadTaskHistory, loadAvailableModels]);

  // Navigation handlers
  const handleSelectHistoryTask = (taskId: string | number) => {
    setSelectedTaskId(taskId as number);
    navigate(`/llm-evaluation/${taskId}`);
  };

  const handleDeleteTask = async (taskId: string | number) => {
    await deleteTask(taskId as number);
  };

  const handleNewConversation = () => {
    setSelectedTaskId(null);
    navigate('/llm-evaluation/new');
  };

  const handleAllEvaluations = () => {
    setSelectedTaskId(null);
    navigate('/llm-evaluation');
  };

  // Sidebar configuration
  const sidebarConfig = {
    searchPlaceholder: 'Search evaluations...',
    newButtonTitle: 'New Conversation',
    allButtonTitle: 'All Evaluations',
    emptyMessage: 'No evaluations yet',
    getItemTitle: (item: EvaluationItem) => item.prompt_snippet || '',
    getItemSubtitle: (item: EvaluationItem) => '', // LLM evaluations don't have subtitles
  };

  // Sidebar props
  const sidebarProps = {
    items: taskHistory,
    isLoading: isLoadingHistory,
    error: errorLoadingHistory,
    onSelectItem: handleSelectHistoryTask,
    onDeleteItem: handleDeleteTask,
    currentItemId: null, // Will be set by EvaluationLayout based on URL params
    onNewEvaluation: handleNewConversation,
    onAllEvaluations: handleAllEvaluations,
    config: sidebarConfig
  };

  return (
    <ErrorBoundary>
      <EvaluationLayout
        sidebarComponent={EvaluationSidebar}
        sidebarProps={sidebarProps}
        onNewEvaluation={handleNewConversation}
        onAllEvaluations={handleAllEvaluations}
        breadcrumbs={{
          home: 'All LLM Evaluations',
          all: 'All LLM Evaluations',
          new: 'New LLM Evaluation',
          details: 'LLM Evaluation Details'
        }}
        routes={{
          home: '/llm-evaluation',
          all: '/llm-evaluation/all',
          new: '/llm-evaluation/new'
        }}
      />
    </ErrorBoundary>
  );
};

export default LLMEvaluationLayout; 