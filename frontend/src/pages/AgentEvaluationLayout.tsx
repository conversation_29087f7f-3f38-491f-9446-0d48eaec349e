import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import useAgentEvaluationStore from '../store/agentEvaluationStore';
import ErrorBoundary from '../components/ErrorBoundary';
import EvaluationLayout from '../components/EvaluationLayout';
import EvaluationSidebar from '../components/EvaluationSidebar';
import { FolderIcon } from 'lucide-react';
import { CodeBracketIcon } from '@heroicons/react/24/outline';
import type { EvaluationItem } from '../types/evaluation';

const AgentEvaluationLayout: React.FC = () => {
  const navigate = useNavigate();
  const {
    tasks,
    isLoadingTasks,
    fetchTasks,
    deleteTask,
  } = useAgentEvaluationStore();

  // Load tasks on mount
  useEffect(() => {
    fetchTasks();
  }, []);

  // Navigation handlers
  const handleSelectTask = (taskId: string | number) => {
    navigate(`/agent-evaluation/${taskId}`);
  };

  const handleDeleteTask = async (taskId: string | number) => {
    try {
      await deleteTask(taskId as string);
    } catch (error) {
      // Error is handled in the store
    }
  };

  const handleNewEvaluation = () => {
    navigate('/agent-evaluation/create');
  };

  const handleAllEvaluations = () => {
    navigate('/agent-evaluation/all');
  };

  // Sidebar configuration
  const sidebarConfig = {
    searchPlaceholder: 'Search evaluations...',
    newButtonTitle: 'New Evaluation',
    allButtonTitle: 'All Evaluations',
    emptyMessage: 'No evaluations yet',
    getItemTitle: (item: EvaluationItem) => item.name || '',
    getItemSubtitle: (item: EvaluationItem) => item.description || '',
    showTypeIcon: true,
    getTypeIcon: (item: EvaluationItem) => {
      return item.evaluation_type === '0-1' ? 
        <FolderIcon className="w-4 h-4" /> : 
        <CodeBracketIcon className="w-4 h-4" />;
    }
  };

  // Sidebar props
  const sidebarProps = {
    items: tasks,
    isLoading: isLoadingTasks,
    error: null,
    onSelectItem: handleSelectTask,
    onDeleteItem: handleDeleteTask,
    currentItemId: null, // Will be set by EvaluationLayout based on URL params
    onNewEvaluation: handleNewEvaluation,
    onAllEvaluations: handleAllEvaluations,
    config: sidebarConfig
  };

  return (
    <ErrorBoundary>
      <EvaluationLayout
        sidebarComponent={EvaluationSidebar}
        sidebarProps={sidebarProps}
        onNewEvaluation={handleNewEvaluation}
        onAllEvaluations={handleAllEvaluations}
        breadcrumbs={{
          home: 'All Agent Evaluations',
          all: 'All Agent Evaluations',
          new: 'New Agent Evaluation',
          details: 'Agent Evaluation Details'
        }}
        routes={{
          home: '/agent-evaluation',
          all: '/agent-evaluation/all',
          new: '/agent-evaluation/create'
        }}
      />
    </ErrorBoundary>
  );
};

export default AgentEvaluationLayout;