// Common status color utility functions

// Agent Evaluation status colors
export const getAgentEvaluationStatusColor = (status: string): string => {
  switch (status) {
    case 'draft': 
      return 'bg-gray-500/20 text-gray-700 dark:bg-gray-400/20 dark:text-gray-300 backdrop-blur-sm glass-border';
    case 'ready': 
      return 'bg-blue-500/20 text-blue-700 dark:bg-blue-400/20 dark:text-blue-300 backdrop-blur-sm glass-border';
    case 'running': 
      return 'bg-yellow-500/20 text-yellow-700 dark:bg-yellow-400/20 dark:text-yellow-300 backdrop-blur-sm glass-border';
    case 'completed': 
      return 'bg-green-500/20 text-green-700 dark:bg-green-400/20 dark:text-green-300 backdrop-blur-sm glass-border';
    case 'failed': 
      return 'bg-red-500/20 text-red-700 dark:bg-red-400/20 dark:text-red-300 backdrop-blur-sm glass-border';
    default: 
      return 'bg-gray-500/20 text-gray-700 dark:bg-gray-400/20 dark:text-gray-300 backdrop-blur-sm glass-border';
  }
};

// LLM Evaluation status colors
export const getLLMEvaluationStatusColor = (status: string): string => {
  switch (status) {
    case 'COMPLETED':
      return 'bg-green-500/20 text-green-700 dark:bg-green-400/20 dark:text-green-300 backdrop-blur-sm glass-border';
    case 'EVALUATION_DONE':
      return 'bg-purple-500/20 text-purple-700 dark:bg-purple-400/20 dark:text-purple-300 backdrop-blur-sm glass-border';
    case 'GENERATING':
    case 'EVALUATING':
      return 'bg-yellow-500/20 text-yellow-700 dark:bg-yellow-400/20 dark:text-yellow-300 backdrop-blur-sm glass-border';
    case 'FAILED':
      return 'bg-red-500/20 text-red-700 dark:bg-red-400/20 dark:text-red-300 backdrop-blur-sm glass-border';
    case 'PENDING':
    default:
      return 'bg-gray-500/20 text-gray-700 dark:bg-gray-400/20 dark:text-gray-300 backdrop-blur-sm glass-border';
  }
};

// Sidebar status colors (for history records)
export const getSidebarStatusColor = (status: string): string => {
  switch (status) {
    case 'completed':
    case 'COMPLETED':
      return 'bg-green-100 text-green-700 dark:bg-green-700/20 dark:text-green-300';
    case 'EVALUATION_DONE':
      return 'bg-purple-100 text-purple-700 dark:bg-purple-700/20 dark:text-purple-300';
    case 'running':
    case 'GENERATING':
    case 'EVALUATING':
      return 'bg-yellow-100 text-yellow-700 dark:bg-yellow-700/20 dark:text-yellow-300';
    case 'ready':
      return 'bg-blue-100 text-blue-700 dark:bg-blue-700/20 dark:text-blue-300';
    case 'failed':
    case 'FAILED':
      return 'bg-red-100 text-red-700 dark:bg-red-700/20 dark:text-red-300';
    case 'draft':
    case 'PENDING':
    default:
      return 'bg-gray-100 text-gray-700 dark:bg-neutral-700/30 dark:text-neutral-300';
  }
};

// Format status display text
export const formatStatusText = (status: string): string => {
  return status.replace(/_/g, ' ').toUpperCase();
}; 