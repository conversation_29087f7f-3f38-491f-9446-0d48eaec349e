/* Import Tailwind directives */
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Linear-inspired base styles */
:root {
  --linear-background-light: #F9FAFB;
  --linear-background-dark: #171717;
  --linear-card-light: #FFFFFF;
  --linear-card-dark: #1F1F1F;
  --linear-accent: #5E6AD2;
  --linear-accent-hover: #4954BD;
  --linear-border-light: #EAECEF;
  --linear-border-dark: #2D2D2D;
  --linear-text-primary-light: #11111B;
  --linear-text-primary-dark: #FFFFFF;
  --linear-text-secondary-light: #687076;
  --linear-text-secondary-dark: #A0A0A0;
}

/* Base styles */
body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  @apply bg-light-background dark:bg-dark-background text-light-primary dark:text-dark-primary;
  position: relative;
}

/* Background mesh gradient */
body::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, 
    rgba(94, 114, 228, 0.03) 0%, 
    rgba(124, 143, 243, 0.05) 25%, 
    rgba(94, 114, 228, 0.03) 50%, 
    rgba(124, 143, 243, 0.05) 75%, 
    rgba(94, 114, 228, 0.03) 100%);
  z-index: -1;
  pointer-events: none;
}

.dark body::before {
  background: linear-gradient(135deg, 
    rgba(124, 143, 243, 0.02) 0%, 
    rgba(94, 114, 228, 0.03) 25%, 
    rgba(124, 143, 243, 0.02) 50%, 
    rgba(94, 114, 228, 0.03) 75%, 
    rgba(124, 143, 243, 0.02) 100%);
}

/* Apply smooth transitions for theme switching */
.transition-colors {
  transition-property: background-color, border-color, color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

/* Custom scrollbar styles */
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: rgba(156, 163, 175, 0.5) transparent;
}

.custom-scrollbar::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: rgba(156, 163, 175, 0.5);
  border-radius: 4px;
  border: 2px solid transparent;
  background-clip: content-box;
  transition: all 0.2s ease;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgba(156, 163, 175, 0.8);
  background-clip: content-box;
}

.dark .custom-scrollbar::-webkit-scrollbar-thumb {
  background: rgba(75, 85, 99, 0.6);
  background-clip: content-box;
}

.dark .custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgba(75, 85, 99, 0.9);
  background-clip: content-box;
}

/* Global scrollbar styles for main page scrollbar */
html {
  scrollbar-width: thin;
  scrollbar-color: rgba(156, 163, 175, 0.5) transparent;
}

html::-webkit-scrollbar {
  width: 12px;
}

html::-webkit-scrollbar-track {
  background: transparent;
}

html::-webkit-scrollbar-thumb {
  background: rgba(156, 163, 175, 0.5);
  border-radius: 6px;
  border: 2px solid transparent;
  background-clip: content-box;
  transition: all 0.2s ease;
}

html::-webkit-scrollbar-thumb:hover {
  background: rgba(156, 163, 175, 0.8);
  background-clip: content-box;
}

/* Dark mode global scrollbar */
.dark html {
  scrollbar-color: rgba(156, 163, 175, 0.7) transparent;
}

.dark html::-webkit-scrollbar-thumb {
  background: rgba(156, 163, 175, 0.7);
  background-clip: content-box;
}

.dark html::-webkit-scrollbar-thumb:hover {
  background: rgba(156, 163, 175, 0.9);
  background-clip: content-box;
}

/* Enhanced focus styles */
.focus-ring {
  @apply focus:outline-none focus:ring-2 focus:ring-light-accent dark:focus:ring-dark-accent focus:ring-offset-2 focus:ring-offset-light-component dark:focus:ring-offset-dark-component;
}

/* Removed button hover animations for cleaner UI */

/* Glassmorphism utilities */
@layer utilities {
  .glass {
    @apply bg-light-glass dark:bg-dark-glass backdrop-blur-md;
  }
  
  .glass-subtle {
    @apply bg-light-glass-subtle dark:bg-dark-glass-subtle backdrop-blur-sm;
  }
  
  .glass-border {
    @apply border-light-glass-border dark:border-dark-glass-border;
  }
  
}

/* Custom component styles */
@layer components {
  /* Simplified buttons without shadows or animations */
  .btn-primary {
    @apply px-4 py-2 bg-light-accent hover:bg-light-accent-hover dark:bg-dark-accent dark:hover:bg-dark-accent-hover 
           text-white rounded-xl transition-colors duration-200 
           focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-light-accent dark:focus:ring-dark-accent
           disabled:opacity-50 disabled:cursor-not-allowed;
  }

  .btn-secondary {
    @apply px-4 py-2 glass glass-border
           text-light-primary dark:text-dark-primary 
           rounded-xl transition-colors duration-200
           focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-light-accent dark:focus:ring-dark-accent
           disabled:opacity-50 disabled:cursor-not-allowed;
  }

  /* Unified glass card style */
  .card-glass {
    @apply glass glass-border rounded-xl p-6;
  }

  /* Linear-inspired sidebar */
  .linear-sidebar {
    @apply bg-gray-50 dark:bg-neutral-900 border-r border-gray-200 dark:border-neutral-700;
  }
  
  /* Linear-inspired form elements */
  .linear-input {
    @apply block w-full rounded-md border-gray-300 dark:border-neutral-600 shadow-sm 
           bg-white dark:bg-neutral-800 text-gray-900 dark:text-white placeholder-gray-400
           dark:placeholder-neutral-500 focus:border-indigo-500 focus:ring focus:ring-indigo-500 
           focus:ring-opacity-50 transition duration-150
           disabled:opacity-60 disabled:cursor-not-allowed;
  }

  .linear-checkbox {
    @apply h-4 w-4 rounded border-gray-300 dark:border-neutral-600 
           text-indigo-600 focus:ring-indigo-500 transition duration-150;
}

  /* Selection styles */
  .linear-selected {
    @apply bg-indigo-50 dark:bg-indigo-900/30 border-l-2 border-indigo-500;
  }
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .linear-sidebar {
    @apply fixed inset-y-0 left-0 z-40 w-64 transform -translate-x-full lg:translate-x-0 transition duration-300;
  }
  
  .linear-sidebar.open {
    @apply translate-x-0;
  }

  .sidebar-backdrop {
    @apply fixed inset-0 bg-black bg-opacity-50 z-30 transition-opacity duration-300;
  }
}

/* Custom utilities */
@layer utilities {
  .border-3 {
    border-width: 3px;
  }
  
  .animate-spin-slow {
    animation: spin 2s linear infinite;
  }
  
  .backdrop-blur-xs {
    backdrop-filter: blur(2px);
  }
  
  /* Modal animation utilities */
  .animate-in {
    animation-fill-mode: both;
  }
  
  .fade-in {
    animation-name: fadeIn;
  }
  
  .zoom-in-95 {
    animation-name: zoomIn95;
  }
  
  .duration-200 {
    animation-duration: 200ms;
  }
  
  .line-clamp-3 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
  }
}

/* Keyframe animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes zoomIn95 {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}
