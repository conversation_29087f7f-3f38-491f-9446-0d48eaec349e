// Shared types for evaluation components

export interface EvaluationItem {
  id: string | number;
  name?: string;
  prompt_snippet?: string;
  description?: string;
  status: string;
  created_at: string;
  evaluation_type?: string;
}

export interface EvaluationSidebarConfig {
  searchPlaceholder: string;
  newButtonTitle: string;
  allButtonTitle: string;
  emptyMessage: string;
  getItemTitle: (item: EvaluationItem) => string;
  getItemSubtitle?: (item: EvaluationItem) => string;
  showTypeIcon?: boolean;
  getTypeIcon?: (item: EvaluationItem) => React.ReactNode;
}

export interface EvaluationSidebarProps {
  items: EvaluationItem[];
  isLoading: boolean;
  error?: string | null;
  onSelectItem: (itemId: string | number) => void;
  onDeleteItem?: (itemId: string | number) => void;
  currentItemId: string | number | null;
  onNewEvaluation: () => void;
  onAllEvaluations?: () => void;
  onToggleDesktopCollapse?: () => void;
  className?: string;
  config: EvaluationSidebarConfig;
}