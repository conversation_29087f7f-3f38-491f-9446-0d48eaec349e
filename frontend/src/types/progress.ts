// Types for evaluation progress tracking

export interface ProgressData {
  current_step: number;
  total_steps: number;
  progress: number;
  message: string;
  details?: Record<string, unknown>;
  step_details?: Record<string, StepDetail>;
  errors?: Array<{
    error: string;
    step: number;
    timestamp: string;
  }>;
  step_duration?: number;
}

export interface StepDetail {
  status: string;
  details: Record<string, unknown>;
  timestamp: string;
}

export interface CompletionData {
  message: string;
  result?: unknown;
}

export interface ErrorData {
  error: string;
  details?: unknown;
}