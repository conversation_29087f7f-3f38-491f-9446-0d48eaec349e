import { useEffect } from 'react';

interface EvaluationDataConfig<T> {
  // Data fetching function
  fetchData: () => Promise<void>;
  
  // State selectors
  selectItems: () => T[];
  selectIsLoading: () => boolean;
  selectError: () => string | null;
  
  // Optional initialization function
  initialize?: () => Promise<void>;
}

interface EvaluationDataReturn<T> {
  items: T[];
  isLoading: boolean;
  error: string | null;
  refresh: () => Promise<void>;
}

/**
 * Generic evaluation data management hook
 * Handles data fetching, loading state, and error handling
 */
export function useEvaluationData<T>(config: EvaluationDataConfig<T>): EvaluationDataReturn<T> {
  const items = config.selectItems();
  const isLoading = config.selectIsLoading();
  const error = config.selectError();

  // Initialize data
  useEffect(() => {
    const initializeData = async () => {
      if (config.initialize) {
        await config.initialize();
      }
      await config.fetchData();
    };

    initializeData();
  }, []);

  const refresh = async () => {
    await config.fetchData();
  };

  return {
    items,
    isLoading,
    error,
    refresh
  };
}

export default useEvaluationData; 