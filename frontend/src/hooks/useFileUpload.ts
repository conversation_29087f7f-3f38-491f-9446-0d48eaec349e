import { useState, useCallback } from 'react';

export interface UseFileUploadOptions {
  maxSize?: number; // in bytes
  maxFiles?: number;
  acceptedTypes?: string[];
  multiple?: boolean;
}

export interface UseFileUploadReturn {
  files: File[];
  isDragging: boolean;
  error: string | null;
  setFiles: (files: File[]) => void;
  addFiles: (newFiles: File[]) => void;
  removeFile: (index: number) => void;
  clearFiles: () => void;
  clearError: () => void;
  handleFileChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  handleDrop: (e: React.DragEvent) => void;
  handleDragOver: (e: React.DragEvent) => void;
  handleDragEnter: (e: React.DragEvent) => void;
  handleDragLeave: (e: React.DragEvent) => void;
  getTotalSize: () => number;
  formatFileSize: (bytes: number) => string;
  validateFiles: (filesToValidate: File[]) => string | null;
}

export const useFileUpload = (options: UseFileUploadOptions = {}): UseFileUploadReturn => {
  const {
    maxSize = 100 * 1024 * 1024, // 100MB default
    maxFiles = 10,
    acceptedTypes = [],
    multiple = true
  } = options;

  const [files, setFiles] = useState<File[]>([]);
  const [isDragging, setIsDragging] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const formatFileSize = useCallback((bytes: number): string => {
    if (bytes < 1024) return bytes + ' B';
    if (bytes < 1024 * 1024) return (bytes / 1024).toFixed(1) + ' KB';
    return (bytes / (1024 * 1024)).toFixed(1) + ' MB';
  }, []);

  const getTotalSize = useCallback((): number => {
    return files.reduce((sum, file) => sum + file.size, 0);
  }, [files]);

  const validateFiles = useCallback((filesToValidate: File[]): string | null => {
    // Check file count
    if (!multiple && filesToValidate.length > 1) {
      return 'Only one file is allowed';
    }

    if (filesToValidate.length > maxFiles) {
      return `Maximum ${maxFiles} files allowed`;
    }

    // Check total size
    const totalSize = filesToValidate.reduce((sum, file) => sum + file.size, 0);
    if (totalSize > maxSize) {
      return `Total file size exceeds ${formatFileSize(maxSize)} limit`;
    }

    // Check file types
    if (acceptedTypes.length > 0) {
      const invalidFiles = filesToValidate.filter(file => {
        const extension = file.name.split('.').pop()?.toLowerCase();
        return !acceptedTypes.includes(extension || '');
      });

      if (invalidFiles.length > 0) {
        return `Invalid file type(s). Accepted types: ${acceptedTypes.join(', ')}`;
      }
    }

    return null;
  }, [maxSize, maxFiles, acceptedTypes, multiple, formatFileSize]);

  const addFiles = useCallback((newFiles: File[]) => {
    const allFiles = multiple ? [...files, ...newFiles] : newFiles;
    const validationError = validateFiles(allFiles);
    
    if (validationError) {
      setError(validationError);
      return;
    }

    setFiles(allFiles);
    setError(null);
  }, [files, multiple, validateFiles]);

  const removeFile = useCallback((index: number) => {
    setFiles(prev => prev.filter((_, i) => i !== index));
    setError(null);
  }, []);

  const clearFiles = useCallback(() => {
    setFiles([]);
    setError(null);
  }, []);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  const handleFileChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFiles = Array.from(e.target.files || []);
    addFiles(selectedFiles);
    // Reset input value to allow selecting the same file again
    e.target.value = '';
  }, [addFiles]);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
    
    const droppedFiles = Array.from(e.dataTransfer.files);
    addFiles(droppedFiles);
  }, [addFiles]);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
  }, []);

  const handleDragEnter = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    // Only set dragging to false if we're leaving the drop zone entirely
    if (!e.currentTarget.contains(e.relatedTarget as Node)) {
      setIsDragging(false);
    }
  }, []);

  return {
    files,
    isDragging,
    error,
    setFiles,
    addFiles,
    removeFile,
    clearFiles,
    clearError,
    handleFileChange,
    handleDrop,
    handleDragOver,
    handleDragEnter,
    handleDragLeave,
    getTotalSize,
    formatFileSize,
    validateFiles
  };
}; 