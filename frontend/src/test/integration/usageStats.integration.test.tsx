import { describe, it, expect, beforeEach, afterEach, afterAll, vi } from 'vitest'
import { render, screen, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { setupServer } from 'msw/node'
import { http, HttpResponse } from 'msw'
import { ThemeProvider } from '../../context/ThemeContext'
import useTaskStore from '../../store/taskStore'
import ViewTaskPage from '../../pages/ViewTaskPage'
import { mockGenerationWithUsageStats, mockRankingWithUsageStats } from '../mocks/usageStatsMocks'

// Mock server setup
const server = setupServer(
  // Mock task status endpoint
  http.get('/api/v1/tasks/:taskId/status', ({ params }) => {
    const { taskId } = params
    return HttpResponse.json({
      id: parseInt(taskId as string),
      prompt: 'Test prompt for usage statistics',
      system_prompt: 'You are a test assistant',
      status: 'COMPLETED',
      requested_models: ['openai/gpt-4', 'anthropic/claude-3-sonnet'],
      created_at: '2024-01-01T10:00:00Z',
      updated_at: '2024-01-01T10:05:00Z',
      generations: [
        mockGenerationWithUsageStats,
        {
          ...mockGenerationWithUsageStats,
          id: 2,
          model_id_used: 'anthropic/claude-3-sonnet',
          blind_id: 'blind-456',
          prompt_tokens: 200,
          completion_tokens: 300,
          total_tokens: 500,
          cost_credits: 0.005000,
          generation_id: 'gen-789456'
        }
      ],
      evaluations: [
        {
          id: 1,
          task_id: parseInt(taskId as string),
          status: 'EVALUATION_DONE',
          evaluation_used_blind_ids: true,
          evaluation_prompt: undefined,
          created_at: '2024-01-01T12:00:00Z',
          updated_at: '2024-01-01T12:05:00Z',
          rankings: [
            mockRankingWithUsageStats,
            {
              ...mockRankingWithUsageStats,
              id: 2,
              evaluator_model_id: 'anthropic/claude-3-opus',
              prompt_tokens: 300,
              completion_tokens: 150,
              total_tokens: 450,
              cost_credits: 0.004500,
              generation_id: 'eval-789456'
            }
          ]
        }
      ]
    })
  }),
  
  // Mock task details endpoint
  http.get('/api/v1/tasks/:taskId', ({ params }) => {
    const { taskId } = params
    return HttpResponse.json({
      id: parseInt(taskId as string),
      prompt: 'Test prompt for usage statistics',
      system_prompt: 'You are a test assistant',
      status: 'COMPLETED',
      outputs: [
        mockGenerationWithUsageStats,
        {
          ...mockGenerationWithUsageStats,
          id: 2,
          model_id_used: 'anthropic/claude-3-sonnet',
          blind_id: 'blind-456',
          prompt_tokens: 200,
          completion_tokens: 300,
          total_tokens: 500,
          cost_credits: 0.005000,
          generation_id: 'gen-789456'
        }
      ],
      evaluations: [
        {
          id: 1,
          task_id: parseInt(taskId as string),
          status: 'EVALUATION_DONE',
          evaluation_used_blind_ids: true,
          evaluation_prompt: null,
          created_at: '2024-01-01T12:00:00Z',
          rankings: [
            mockRankingWithUsageStats,
            {
              ...mockRankingWithUsageStats,
              id: 2,
              evaluator_model_id: 'anthropic/claude-3-opus',
              prompt_tokens: 300,
              completion_tokens: 150,
              total_tokens: 450,
              cost_credits: 0.004500,
              generation_id: 'eval-789456'
            }
          ]
        }
      ],
      created_at: '2024-01-01T10:00:00Z'
    })
  }),

  // Mock available models endpoint
  http.get('/api/v1/models', () => {
    return HttpResponse.json([
      'openai/gpt-4',
      'anthropic/claude-3-sonnet',
      'anthropic/claude-3-opus'
    ])
  }),

  // Mock SSE endpoint
  http.get('/api/v1/tasks/:taskId/stream', () => {
    return new HttpResponse('data: {"type": "completed"}\n\n', {
      headers: {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive'
      }
    })
  })
)

// Test wrapper component
const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <ThemeProvider>
    {children}
  </ThemeProvider>
)

describe('Usage Statistics Integration Tests', () => {
  beforeEach(() => {
    server.listen()
    // Reset task store
    useTaskStore.setState(useTaskStore.getInitialState())
  })

  afterEach(() => {
    server.resetHandlers()
    vi.clearAllMocks()
  })

  afterAll(() => {
    server.close()
  })

  describe('Full Usage Statistics Workflow', () => {
    it('should display complete usage statistics dashboard', async () => {
      const user = userEvent.setup()

      render(
        <TestWrapper>
          <ViewTaskPage taskId={1} />
        </TestWrapper>
      )

      // Wait for task data to load
      await waitFor(() => {
        expect(screen.getByText('Task #1')).toBeInTheDocument()
      })

      // Verify generation usage statistics are displayed
      await waitFor(() => {
        expect(screen.getByText('Generation Usage')).toBeInTheDocument()
      })

      // Check individual generation usage stats
      // Use getAllByText since there might be multiple elements with 'gpt-4'
      const gpt4Elements = screen.getAllByText('gpt-4')
      expect(gpt4Elements.length).toBeGreaterThan(0)
      // Use getAllByText for numbers that might appear multiple times
      const hundredElements = screen.getAllByText('100')
      expect(hundredElements.length).toBeGreaterThan(0) // prompt tokens
      const oneFiftyElements = screen.getAllByText('150')
      expect(oneFiftyElements.length).toBeGreaterThan(0) // completion tokens
      const twoFiftyElements = screen.getAllByText('250')
      expect(twoFiftyElements.length).toBeGreaterThan(0) // total tokens
      expect(screen.getByText('$0.002500')).toBeInTheDocument() // cost

      // Verify evaluation usage statistics
      await waitFor(() => {
        expect(screen.getByText('Evaluation Usage')).toBeInTheDocument()
      })

      // Check aggregated totals
      await waitFor(() => {
        // Total generation tokens: 250 + 500 = 750
        // Total evaluation tokens: 300 + 450 = 750
        // Combined total: 1500
        expect(screen.getByText('1,500')).toBeInTheDocument()

        // Total cost: 0.002500 + 0.005000 + 0.003000 + 0.004500 = 0.015000
        expect(screen.getByText('$0.015000')).toBeInTheDocument()
      })
    })

    it('should handle model selection and display usage statistics correctly', async () => {
      const user = userEvent.setup()

      render(
        <TestWrapper>
          <ViewTaskPage taskId={1} />
        </TestWrapper>
      )

      // Wait for data to load
      await waitFor(() => {
        expect(screen.getByText('Task #1')).toBeInTheDocument()
      })

      // Find and interact with model selector dropdown
      const modelSelectors = await screen.findAllByDisplayValue(/gpt-4/)
      expect(modelSelectors.length).toBeGreaterThan(0)

      // Switch to another model - use the first selector
      await user.selectOptions(modelSelectors[0], '1') // Select second generation

      // Verify usage statistics update for selected model
      await waitFor(() => {
        const claudeElements = screen.getAllByText('claude-3-sonnet')
        expect(claudeElements.length).toBeGreaterThan(0)
        expect(screen.getByText('$0.005000')).toBeInTheDocument()
      })
    })

    it('should display correct usage indicators for missing data', async () => {
      // Override mock to include generation without usage stats
      // Need to override both endpoints since ViewTaskPage might call both
      server.use(
        http.get('/api/v1/tasks/:taskId/status', ({ params }) => {
          const { taskId } = params
          return HttpResponse.json({
            id: parseInt(taskId as string),
            prompt: 'Test prompt',
            system_prompt: 'Test system',
            status: 'COMPLETED',
            requested_models: ['openai/gpt-4', 'test/legacy-model'],
            created_at: '2024-01-01T10:00:00Z',
            updated_at: '2024-01-01T10:05:00Z',
            generations: [
              mockGenerationWithUsageStats,
              {
                id: 2,
                task_id: parseInt(taskId as string),
                model_id_used: 'test/legacy-model',
                blind_id: 'blind-legacy',
                output_text: 'Legacy output without stats',
                reasoning_text: null,
                error_message: null,
                created_at: '2024-01-01T10:05:00Z',
                prompt_tokens: null,
                completion_tokens: null,
                total_tokens: null,
                reasoning_tokens: null,
                cached_tokens: null,
                cost_credits: null,
                generation_id: null
              }
            ],
            evaluations: []
          })
        }),
        http.get('/api/v1/tasks/:taskId', ({ params }) => {
          const { taskId } = params
          return HttpResponse.json({
            id: parseInt(taskId as string),
            prompt: 'Test prompt',
            system_prompt: 'Test system',
            status: 'COMPLETED',
            outputs: [
              mockGenerationWithUsageStats,
              {
                id: 2,
                task_id: parseInt(taskId as string),
                model_id_used: 'test/legacy-model',
                blind_id: 'blind-legacy',
                output_text: 'Legacy output without stats',
                reasoning_text: null,
                error_message: null,
                created_at: '2024-01-01T10:05:00Z',
                prompt_tokens: null,
                completion_tokens: null,
                total_tokens: null,
                reasoning_tokens: null,
                cached_tokens: null,
                cost_credits: null,
                generation_id: null
              }
            ],
            evaluations: [],
            created_at: '2024-01-01T10:00:00Z'
          })
        })
      )

      const user = userEvent.setup()
      
      render(
        <TestWrapper>
          <ViewTaskPage taskId={1} />
        </TestWrapper>
      )

      // Wait for data to load
      await waitFor(() => {
        expect(screen.getByText('Task #1')).toBeInTheDocument()
      })

      // Find the model selector and change to the second option
      const modelSelectors = await screen.findAllByRole('combobox')
      const generationSelector = modelSelectors[0] // First selector should be generation model
      
      // Select the legacy model option
      await user.selectOptions(generationSelector, '1')

      // Wait for the model change to take effect
      await waitFor(() => {
        // The dropdown should now show value '1' (second option)
        expect(generationSelector).toHaveValue('1')
      })
      
      // After selecting the legacy model without usage stats,
      // the UI should show the unavailable message
      await waitFor(() => {
        expect(screen.getByText('Usage statistics are not available for this generation')).toBeInTheDocument()
      }, { timeout: 3000 })
      
      // Verify the model name is displayed (use getAllByText since it appears in multiple places)
      const legacyModelElements = screen.getAllByText('legacy-model')
      expect(legacyModelElements.length).toBeGreaterThan(0)
      
      // Verify that the unavailable message is shown instead of usage data
      // The presence of the "not available" message is the key indicator
      // There might be other "Cost" labels in the UI (e.g., in the Usage Dashboard)
      const unavailableMessage = screen.getByText('Usage statistics are not available for this generation')
      expect(unavailableMessage).toBeInTheDocument()
      
      // Also verify it shows in the usage dashboard
      await waitFor(() => {
        expect(screen.getByText('1 without data')).toBeInTheDocument()
      })
    })

    it('should handle real-time updates with usage statistics', async () => {
      // Note: In a real application, SSE updates would be handled through EventSource
      // For this test, we verify that the initial usage statistics are displayed correctly
      
      render(
        <TestWrapper>
          <ViewTaskPage taskId={1} />
        </TestWrapper>
      )

      // Wait for initial load
      await waitFor(() => {
        expect(screen.getByText('Task #1')).toBeInTheDocument()
      })

      // Verify the initial usage statistics are displayed
      await waitFor(() => {
        const hundredElements = screen.getAllByText('100')
        expect(hundredElements.length).toBeGreaterThan(0) // prompt tokens
        expect(screen.getByText('$0.002500')).toBeInTheDocument() // cost
      })

      // Verify usage statistics are properly rendered for both generations
      const gpt4Elements = screen.getAllByText('gpt-4')
      expect(gpt4Elements.length).toBeGreaterThan(0)
      
      // Check that usage dashboard is updated
      await waitFor(() => {
        expect(screen.getByText('Usage Dashboard')).toBeInTheDocument()
      })
    })

    it('should calculate and display aggregated statistics correctly', async () => {
      render(
        <TestWrapper>
          <ViewTaskPage taskId={1} />
        </TestWrapper>
      )

      // Wait for full load
      await waitFor(() => {
        expect(screen.getByText('Usage Dashboard')).toBeInTheDocument()
      })

      // Verify generation usage aggregation
      await waitFor(() => {
        expect(screen.getByText('Generation Usage')).toBeInTheDocument()
        // Should show "2 models" since we have 2 generations with usage stats
        expect(screen.getByText('2 models')).toBeInTheDocument()
      })

      // Verify evaluation usage aggregation  
      await waitFor(() => {
        expect(screen.getByText('Evaluation Usage')).toBeInTheDocument()
        // Should show "2 evaluators" since we have 2 rankings with usage stats
        expect(screen.getByText('2 evaluators')).toBeInTheDocument()
      })

      // Verify combined totals are calculated correctly
      const totalTokensElement = await screen.findByText('1,500')
      expect(totalTokensElement).toBeInTheDocument()

      const totalCostElement = await screen.findByText('$0.015000')
      expect(totalCostElement).toBeInTheDocument()
    })

    it('should handle copy functionality with usage statistics', async () => {
      const user = userEvent.setup()

      // Spy on the clipboard writeText
      const writeTextSpy = vi.spyOn(navigator.clipboard, 'writeText')

      render(
        <TestWrapper>
          <ViewTaskPage taskId={1} />
        </TestWrapper>
      )

      // Wait for data to load
      await waitFor(() => {
        expect(screen.getByText('Task #1')).toBeInTheDocument()
      })

      // Find and click copy all outputs button
      const copyAllButton = screen.getByTitle('Copy all generated outputs')
      await user.click(copyAllButton)

      // Verify clipboard was called and contains usage information
      expect(writeTextSpy).toHaveBeenCalled()
      const copiedText = writeTextSpy.mock.calls[0][0]
      expect(copiedText).toContain('Task ID: 1')
      expect(copiedText).toContain('Model: gpt-4')
      expect(copiedText).toContain('Model: claude-3-sonnet')
      
      // Clean up the spy
      writeTextSpy.mockRestore()
    })
  })

  describe('Error Handling', () => {
    it('should handle API errors gracefully', async () => {
      // Mock API error
      server.use(
        http.get('/api/v1/tasks/:taskId', () => {
          return new HttpResponse(null, { status: 500 })
        })
      )

      render(
        <TestWrapper>
          <ViewTaskPage taskId={999} />
        </TestWrapper>
      )

      // Should show loading initially
      expect(screen.getByText('Loading task 999...')).toBeInTheDocument()

      // Error handling would be implemented in the actual component
      // This test ensures the component doesn't crash with API errors
    })

    it('should handle malformed usage statistics data', async () => {
      // Mock response with malformed data
      server.use(
        http.get('/api/v1/tasks/:taskId', ({ params }) => {
          return HttpResponse.json({
            id: parseInt(params.taskId as string),
            prompt: 'Test prompt',
            status: 'COMPLETED',
            outputs: [
              {
                ...mockGenerationWithUsageStats,
                prompt_tokens: 'invalid', // Invalid data type
                cost_credits: null,
                total_tokens: undefined
              }
            ],
            evaluations: [],
            created_at: '2024-01-01T10:00:00Z'
          })
        })
      )

      expect(() => {
        render(
          <TestWrapper>
            <ViewTaskPage taskId={1} />
          </TestWrapper>
        )
      }).not.toThrow()

      // Component should handle malformed data gracefully
      await waitFor(() => {
        expect(screen.getByText('Task #1')).toBeInTheDocument()
      })
    })
  })

  describe('Performance', () => {
    it('should handle large datasets efficiently', async () => {
      // Mock response with many generations
      const manyGenerations = Array.from({ length: 50 }, (_, index) => ({
        ...mockGenerationWithUsageStats,
        id: index + 1,
        blind_id: `blind-${index + 1}`,
        generation_id: `gen-${index + 1}`
      }))

      server.use(
        http.get('/api/v1/tasks/:taskId/status', ({ params }) => {
          return HttpResponse.json({
            id: parseInt(params.taskId as string),
            prompt: 'Large dataset test',
            system_prompt: 'Test system prompt',
            status: 'COMPLETED',
            requested_models: Array.from({ length: 50 }, (_, i) => `model-${i}`),
            created_at: '2024-01-01T10:00:00Z',
            updated_at: '2024-01-01T10:05:00Z',
            generations: manyGenerations,
            evaluations: []
          })
        }),
        http.get('/api/v1/tasks/:taskId', ({ params }) => {
          return HttpResponse.json({
            id: parseInt(params.taskId as string),
            prompt: 'Large dataset test',
            status: 'COMPLETED',
            outputs: manyGenerations,
            evaluations: [],
            created_at: '2024-01-01T10:00:00Z'
          })
        })
      )

      const startTime = performance.now()
      
      render(
        <TestWrapper>
          <ViewTaskPage taskId={1} />
        </TestWrapper>
      )

      await waitFor(() => {
        expect(screen.getByText('Task #1')).toBeInTheDocument()
      }, { timeout: 10000 })

      const endTime = performance.now()
      
      // Should render within reasonable time (less than 5 seconds for large dataset)
      expect(endTime - startTime).toBeLessThan(5000)
    })
  })
})