import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { describe, it, expect, vi, beforeEach } from 'vitest'
import PromptInput from '../../components/PromptInput'
import ModelSelector from '../../components/ModelSelector'

describe('Model Selection Integration', () => {
  const mockModels = [
    'openai/gpt-4',
    'openai/gpt-3.5-turbo',
    'anthropic/claude-3-sonnet',
    'anthropic/claude-3-haiku',
    'google/gemini-pro'
  ]

  const mockOnSubmit = vi.fn()
  const mockOnSelectionChange = vi.fn()

  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('PromptInput and ModelSelector Integration', () => {
    it('integrates model selection with prompt submission', async () => {
      render(
        <PromptInput
          onSubmit={mockOnSubmit}
          isLoading={false}
          availableModels={mockModels}
        />
      )

      // Fill in prompt
      const promptTextarea = screen.getByPlaceholderText('Enter your prompt here...')
      fireEvent.change(promptTextarea, { target: { value: 'Test integration prompt' } })

      // Select multiple models
      const gpt4Checkbox = screen.getByRole('checkbox', { name: /gpt-4/ })
      const claudeCheckbox = screen.getByRole('checkbox', { name: /claude-3-sonnet/ })
      
      fireEvent.click(gpt4Checkbox)
      fireEvent.click(claudeCheckbox)

      // Submit form
      const submitButton = screen.getByRole('button', { name: /generate/i })
      fireEvent.click(submitButton)

      await waitFor(() => {
        expect(mockOnSubmit).toHaveBeenCalledWith(
          'Test integration prompt',
          ['openai/gpt-4', 'anthropic/claude-3-sonnet'],
          ''
        )
      })
    })

    it('prevents submission when no models are selected', async () => {
      render(
        <PromptInput
          onSubmit={mockOnSubmit}
          isLoading={false}
          availableModels={mockModels}
        />
      )

      // Fill in prompt but don't select any models
      const promptTextarea = screen.getByPlaceholderText('Enter your prompt here...')
      fireEvent.change(promptTextarea, { target: { value: 'Test prompt without models' } })

      // Try to submit
      const submitButton = screen.getByRole('button', { name: /generate/i })
      fireEvent.click(submitButton)

      // Should not submit
      expect(mockOnSubmit).not.toHaveBeenCalled()
      
      // Should show validation error
      expect(screen.getByText('Please select at least one model')).toBeInTheDocument()
    })

    it('handles model selection state changes correctly', async () => {
      render(
        <PromptInput
          onSubmit={mockOnSubmit}
          isLoading={false}
          availableModels={mockModels}
        />
      )

      // Initially should show validation error
      expect(screen.getByText('Please select at least one model')).toBeInTheDocument()

      // Select a model
      const gpt4Checkbox = screen.getByRole('checkbox', { name: /gpt-4/ })
      fireEvent.click(gpt4Checkbox)

      // Validation error should disappear
      await waitFor(() => {
        expect(screen.queryByText('Please select at least one model')).not.toBeInTheDocument()
      })

      // Deselect the model
      fireEvent.click(gpt4Checkbox)

      // Validation error should reappear
      await waitFor(() => {
        expect(screen.getByText('Please select at least one model')).toBeInTheDocument()
      })
    })
  })

  describe('Standalone ModelSelector Integration', () => {
    it('calls selection change handler correctly', async () => {
      render(
        <ModelSelector
          availableModels={mockModels}
          onSelectionChange={mockOnSelectionChange}
        />
      )

      // Select first model
      const gpt4Checkbox = screen.getByRole('checkbox', { name: /gpt-4/ })
      fireEvent.click(gpt4Checkbox)

      await waitFor(() => {
        expect(mockOnSelectionChange).toHaveBeenCalledWith(['openai/gpt-4'])
      })

      // Select second model
      const claudeCheckbox = screen.getByRole('checkbox', { name: /claude-3-sonnet/ })
      fireEvent.click(claudeCheckbox)

      await waitFor(() => {
        expect(mockOnSelectionChange).toHaveBeenCalledWith(['openai/gpt-4', 'anthropic/claude-3-sonnet'])
      })
    })

    it('handles initial selection correctly', () => {
      render(
        <ModelSelector
          availableModels={mockModels}
          onSelectionChange={mockOnSelectionChange}
          initialSelection={['openai/gpt-4', 'google/gemini-pro']}
        />
      )

      // Check that initial models are selected
      const gpt4Checkbox = screen.getByRole('checkbox', { name: /gpt-4/ })
      const geminiCheckbox = screen.getByRole('checkbox', { name: /gemini-pro/ })
      const claudeCheckbox = screen.getByRole('checkbox', { name: /claude-3-sonnet/ })

      expect(gpt4Checkbox).toBeChecked()
      expect(geminiCheckbox).toBeChecked()
      expect(claudeCheckbox).not.toBeChecked()

      // Should not show validation error
      expect(screen.queryByText('Please select at least one model')).not.toBeInTheDocument()
    })

    it('updates when initialSelection prop changes', async () => {
      const { rerender } = render(
        <ModelSelector
          availableModels={mockModels}
          onSelectionChange={mockOnSelectionChange}
          initialSelection={['openai/gpt-4']}
        />
      )

      expect(screen.getByRole('checkbox', { name: /gpt-4/ })).toBeChecked()

      rerender(
        <ModelSelector
          availableModels={mockModels}
          onSelectionChange={mockOnSelectionChange}
          initialSelection={['anthropic/claude-3-sonnet']}
        />
      )

      await waitFor(() => {
        expect(screen.getByRole('checkbox', { name: /claude-3-sonnet/ })).toBeChecked()
        expect(screen.getByRole('checkbox', { name: /gpt-4/ })).not.toBeChecked()
      })
    })
  })

  describe('Model Provider Grouping', () => {
    it('displays models grouped by provider correctly', () => {
      render(
        <ModelSelector
          availableModels={mockModels}
          onSelectionChange={mockOnSelectionChange}
        />
      )

      // Check OpenAI models
      expect(screen.getAllByText('openai')).toHaveLength(2) // gpt-4 and gpt-3.5-turbo
      expect(screen.getByText('gpt-4')).toBeInTheDocument()
      expect(screen.getByText('gpt-3.5-turbo')).toBeInTheDocument()

      // Check Anthropic models
      expect(screen.getAllByText('anthropic')).toHaveLength(2) // claude-3-sonnet and claude-3-haiku
      expect(screen.getByText('claude-3-sonnet')).toBeInTheDocument()
      expect(screen.getByText('claude-3-haiku')).toBeInTheDocument()

      // Check Google models
      expect(screen.getByText('google')).toBeInTheDocument()
      expect(screen.getByText('gemini-pro')).toBeInTheDocument()
    })

    it('handles models without provider prefix', () => {
      const modelsWithoutProvider = ['standalone-model', 'another-model']
      
      render(
        <ModelSelector
          availableModels={modelsWithoutProvider}
          onSelectionChange={mockOnSelectionChange}
        />
      )

      expect(screen.getByText('standalone-model')).toBeInTheDocument()
      expect(screen.getByText('another-model')).toBeInTheDocument()
    })
  })

  describe('Disabled State Integration', () => {
    it('disables all model selection when loading', () => {
      render(
        <PromptInput
          onSubmit={mockOnSubmit}
          isLoading={true}
          availableModels={mockModels}
        />
      )

      // All checkboxes should be disabled
      const checkboxes = screen.getAllByRole('checkbox')
      checkboxes.forEach(checkbox => {
        expect(checkbox).toBeDisabled()
      })

      // Submit button should be disabled (text changes to "Generating..." when loading)
      const submitButton = screen.getByRole('button', { name: /generating/i })
      expect(submitButton).toBeDisabled()
    })

    it('disables ModelSelector when disabled prop is true', () => {
      render(
        <ModelSelector
          availableModels={mockModels}
          onSelectionChange={mockOnSelectionChange}
          disabled={true}
        />
      )

      const checkboxes = screen.getAllByRole('checkbox')
      checkboxes.forEach(checkbox => {
        expect(checkbox).toBeDisabled()
      })
    })
  })

  describe('Form Validation Integration', () => {
    it('validates both prompt and model selection', async () => {
      render(
        <PromptInput
          onSubmit={mockOnSubmit}
          isLoading={false}
          availableModels={mockModels}
        />
      )

      // Try to submit with empty prompt and no models
      const submitButton = screen.getByRole('button', { name: /generate/i })
      fireEvent.click(submitButton)

      expect(mockOnSubmit).not.toHaveBeenCalled()
      expect(screen.getByText('Please select at least one model')).toBeInTheDocument()

      // Add prompt but still no models
      const promptTextarea = screen.getByPlaceholderText('Enter your prompt here...')
      fireEvent.change(promptTextarea, { target: { value: 'Test prompt' } })
      fireEvent.click(submitButton)

      expect(mockOnSubmit).not.toHaveBeenCalled()

      // Add model selection
      const gpt4Checkbox = screen.getByRole('checkbox', { name: /gpt-4/ })
      fireEvent.click(gpt4Checkbox)

      // Now should be able to submit
      fireEvent.click(submitButton)

      await waitFor(() => {
        expect(mockOnSubmit).toHaveBeenCalledWith('Test prompt', ['openai/gpt-4'], '')
      })
    })
  })

  describe('System Prompt Integration', () => {
    it('includes system prompt in submission when provided', async () => {
      render(
        <PromptInput
          onSubmit={mockOnSubmit}
          isLoading={false}
          availableModels={mockModels}
        />
      )

      // Expand system prompt section
      const systemPromptToggle = screen.getByText('System Prompt (Optional)')
      fireEvent.click(systemPromptToggle)

      await waitFor(() => {
        const systemPromptTextarea = screen.getByPlaceholderText(/Enter system prompt here/)
        fireEvent.change(systemPromptTextarea, { target: { value: 'Custom system prompt' } })
      })

      // Fill user prompt and select model
      const promptTextarea = screen.getByPlaceholderText('Enter your prompt here...')
      fireEvent.change(promptTextarea, { target: { value: 'User prompt' } })

      const gpt4Checkbox = screen.getByRole('checkbox', { name: /gpt-4/ })
      fireEvent.click(gpt4Checkbox)

      // Submit
      const submitButton = screen.getByRole('button', { name: /generate/i })
      fireEvent.click(submitButton)

      await waitFor(() => {
        expect(mockOnSubmit).toHaveBeenCalledWith(
          'User prompt',
          ['openai/gpt-4'],
          'Custom system prompt'
        )
      })
    })
  })
}) 