import { Generation, Ranking } from '../../api/apiClient'

export const mockGenerationWithUsageStats: Generation = {
  id: 1,
  task_id: 1,
  model_id_used: 'openai/gpt-4',
  blind_id: 'blind-123',
  output_text: 'This is a sample output',
  reasoning_text: 'This is the reasoning process',
  error_message: null,
  created_at: '2024-01-01T10:00:00Z',
  prompt_tokens: 100,
  completion_tokens: 150,
  total_tokens: 250,
  reasoning_tokens: 50,
  cached_tokens: 25,
  cost_credits: 0.002500,
  generation_id: 'gen-123456'
}

export const mockGenerationWithoutUsageStats: Generation = {
  id: 2,
  task_id: 1,
  model_id_used: 'anthropic/claude-3-sonnet',
  blind_id: 'blind-456',
  output_text: 'Another sample output',
  reasoning_text: null,
  error_message: null,
  created_at: '2024-01-01T10:05:00Z',
  prompt_tokens: null,
  completion_tokens: null,
  total_tokens: null,
  reasoning_tokens: null,
  cached_tokens: null,
  cost_credits: null,
  generation_id: null
}

export const mockGenerationWithError: Generation = {
  id: 3,
  task_id: 1,
  model_id_used: 'google/gemini-pro',
  blind_id: 'blind-789',
  output_text: null,
  reasoning_text: null,
  error_message: 'Rate limit exceeded',
  created_at: '2024-01-01T10:10:00Z',
  prompt_tokens: null,
  completion_tokens: null,
  total_tokens: null,
  reasoning_tokens: null,
  cached_tokens: null,
  cost_credits: null,
  generation_id: null
}

export const mockRankingWithUsageStats: Ranking = {
  id: 1,
  evaluation_id: 1,
  evaluator_model_id: 'openai/gpt-4',
  ranked_list_json: [1, 2, 3],
  reasoning_text: 'Evaluation reasoning',
  error_message: null,
  created_at: '2024-01-01T11:00:00Z',
  prompt_tokens: 200,
  completion_tokens: 100,
  total_tokens: 300,
  reasoning_tokens: 75,
  cached_tokens: 50,
  cost_credits: 0.003000,
  generation_id: 'eval-123456'
}

export const mockRankingWithoutUsageStats: Ranking = {
  id: 2,
  evaluation_id: 1,
  evaluator_model_id: 'anthropic/claude-3-sonnet',
  ranked_list_json: [2, 1, 3],
  reasoning_text: 'Another evaluation',
  error_message: null,
  created_at: '2024-01-01T11:05:00Z',
  prompt_tokens: null,
  completion_tokens: null,
  total_tokens: null,
  reasoning_tokens: null,
  cached_tokens: null,
  cost_credits: null,
  generation_id: null
}

export const mockCombinedUsageData = {
  generationUsage: {
    prompt_tokens: 300,
    completion_tokens: 450,
    total_tokens: 750,
    reasoning_tokens: 150,
    cached_tokens: 75,
    cost_credits: 0.007500,
    count: 3,
    totalGenerations: 4
  },
  evaluationUsage: {
    prompt_tokens: 600,
    completion_tokens: 300,
    total_tokens: 900,
    reasoning_tokens: 225,
    cached_tokens: 150,
    cost_credits: 0.009000,
    count: 2,
    totalEvaluations: 3
  }
}