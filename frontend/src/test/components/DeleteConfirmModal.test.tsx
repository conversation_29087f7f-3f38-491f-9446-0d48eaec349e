import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import DeleteConfirmModal from '../../components/DeleteConfirmModal'

describe('DeleteConfirmModal', () => {
  const defaultProps = {
    isOpen: true,
    onClose: vi.fn(),
    onConfirm: vi.fn(),
    taskId: 123,
    taskPreview: 'This is a sample task preview text that shows what the task contains.'
  }

  beforeEach(() => {
    vi.clearAllMocks()
    // Reset body overflow style
    document.body.style.overflow = 'unset'
  })

  afterEach(() => {
    vi.clearAllMocks()
    // Clean up body overflow style
    document.body.style.overflow = 'unset'
  })

  describe('Rendering', () => {
    it('renders when isOpen is true', () => {
      render(<DeleteConfirmModal {...defaultProps} />)
      
      expect(screen.getByRole('heading', { name: 'Delete Task' })).toBeInTheDocument()
      expect(screen.getByText('Are you sure you want to delete Task #123?')).toBeInTheDocument()
    })

    it('does not render when isOpen is false', () => {
      render(<DeleteConfirmModal {...defaultProps} isOpen={false} />)
      
      expect(screen.queryByRole('heading', { name: 'Delete Task' })).not.toBeInTheDocument()
    })

    it('displays task ID correctly', () => {
      render(<DeleteConfirmModal {...defaultProps} taskId={456} />)
      
      expect(screen.getByText('Are you sure you want to delete Task #456?')).toBeInTheDocument()
    })

    it('displays task preview when provided', () => {
      const taskPreview = 'Custom task preview content'
      render(<DeleteConfirmModal {...defaultProps} taskPreview={taskPreview} />)
      
      expect(screen.getByText('Task Preview:')).toBeInTheDocument()
      expect(screen.getByText(taskPreview)).toBeInTheDocument()
    })

    it('does not display task preview section when not provided', () => {
      render(<DeleteConfirmModal {...defaultProps} taskPreview={undefined} />)
      
      expect(screen.queryByText('Task Preview:')).not.toBeInTheDocument()
    })

    it('displays warning message', () => {
      render(<DeleteConfirmModal {...defaultProps} />)
      
      expect(screen.getByText('This action cannot be undone')).toBeInTheDocument()
      expect(screen.getByText(/This will permanently delete the task/)).toBeInTheDocument()
    })
  })

  describe('Modal Controls', () => {
    it('renders close button with proper aria label', () => {
      render(<DeleteConfirmModal {...defaultProps} />)
      
      const closeButton = screen.getByLabelText('Close modal')
      expect(closeButton).toBeInTheDocument()
    })

    it('renders cancel and delete buttons', () => {
      render(<DeleteConfirmModal {...defaultProps} />)
      
      expect(screen.getByRole('button', { name: 'Cancel' })).toBeInTheDocument()
      expect(screen.getByRole('button', { name: 'Delete Task' })).toBeInTheDocument()
    })
  })

  describe('User Interactions', () => {
    it('calls onClose when close button is clicked', () => {
      render(<DeleteConfirmModal {...defaultProps} />)
      
      const closeButton = screen.getByLabelText('Close modal')
      fireEvent.click(closeButton)
      
      expect(defaultProps.onClose).toHaveBeenCalledTimes(1)
    })

    it('calls onClose when cancel button is clicked', () => {
      render(<DeleteConfirmModal {...defaultProps} />)
      
      const cancelButton = screen.getByRole('button', { name: 'Cancel' })
      fireEvent.click(cancelButton)
      
      expect(defaultProps.onClose).toHaveBeenCalledTimes(1)
    })

    it('calls onConfirm and onClose when delete button is clicked', () => {
      render(<DeleteConfirmModal {...defaultProps} />)
      
      const deleteButton = screen.getByRole('button', { name: 'Delete Task' })
      fireEvent.click(deleteButton)
      
      expect(defaultProps.onConfirm).toHaveBeenCalledTimes(1)
      expect(defaultProps.onClose).toHaveBeenCalledTimes(1)
    })

    it('calls onClose when backdrop is clicked', () => {
      render(<DeleteConfirmModal {...defaultProps} />)
      
      // Click on the backdrop (the outer div)
      const backdrop = screen.getByRole('heading', { name: 'Delete Task' }).closest('[class*="fixed inset-0"]')
      fireEvent.click(backdrop!)
      
      expect(defaultProps.onClose).toHaveBeenCalledTimes(1)
    })

    it('does not call onClose when modal content is clicked', () => {
      render(<DeleteConfirmModal {...defaultProps} />)
      
      // Click on the modal content
      const modalContent = screen.getByText('Are you sure you want to delete Task #123?')
      fireEvent.click(modalContent)
      
      expect(defaultProps.onClose).not.toHaveBeenCalled()
    })
  })

  describe('Keyboard Navigation', () => {
    it('calls onClose when Escape key is pressed', () => {
      render(<DeleteConfirmModal {...defaultProps} />)
      
      fireEvent.keyDown(document, { key: 'Escape' })
      
      expect(defaultProps.onClose).toHaveBeenCalledTimes(1)
    })

    it('does not call onClose when other keys are pressed', () => {
      render(<DeleteConfirmModal {...defaultProps} />)
      
      fireEvent.keyDown(document, { key: 'Enter' })
      fireEvent.keyDown(document, { key: 'Space' })
      fireEvent.keyDown(document, { key: 'Tab' })
      
      expect(defaultProps.onClose).not.toHaveBeenCalled()
    })

    it('does not respond to Escape key when modal is closed', () => {
      render(<DeleteConfirmModal {...defaultProps} isOpen={false} />)
      
      fireEvent.keyDown(document, { key: 'Escape' })
      
      expect(defaultProps.onClose).not.toHaveBeenCalled()
    })
  })

  describe('Body Overflow Management', () => {
    it('sets body overflow to hidden when modal is open', () => {
      render(<DeleteConfirmModal {...defaultProps} />)
      
      expect(document.body.style.overflow).toBe('hidden')
    })

    it('does not set body overflow when modal is closed', () => {
      render(<DeleteConfirmModal {...defaultProps} isOpen={false} />)
      
      expect(document.body.style.overflow).toBe('unset')
    })

    it('resets body overflow on unmount', () => {
      const { unmount } = render(<DeleteConfirmModal {...defaultProps} />)
      
      expect(document.body.style.overflow).toBe('hidden')
      
      unmount()
      
      expect(document.body.style.overflow).toBe('unset')
    })

    it('resets body overflow when modal closes', () => {
      const { rerender } = render(<DeleteConfirmModal {...defaultProps} />)
      
      expect(document.body.style.overflow).toBe('hidden')
      
      rerender(<DeleteConfirmModal {...defaultProps} isOpen={false} />)
      
      expect(document.body.style.overflow).toBe('unset')
    })
  })

  describe('Event Listener Management', () => {
    it('adds keydown event listener when modal opens', () => {
      const addEventListenerSpy = vi.spyOn(document, 'addEventListener')
      
      render(<DeleteConfirmModal {...defaultProps} />)
      
      expect(addEventListenerSpy).toHaveBeenCalledWith('keydown', expect.any(Function))
      
      addEventListenerSpy.mockRestore()
    })

    it('removes keydown event listener when modal closes', () => {
      const removeEventListenerSpy = vi.spyOn(document, 'removeEventListener')
      
      const { rerender } = render(<DeleteConfirmModal {...defaultProps} />)
      
      rerender(<DeleteConfirmModal {...defaultProps} isOpen={false} />)
      
      expect(removeEventListenerSpy).toHaveBeenCalledWith('keydown', expect.any(Function))
      
      removeEventListenerSpy.mockRestore()
    })

    it('removes keydown event listener on unmount', () => {
      const removeEventListenerSpy = vi.spyOn(document, 'removeEventListener')
      
      const { unmount } = render(<DeleteConfirmModal {...defaultProps} />)
      
      unmount()
      
      expect(removeEventListenerSpy).toHaveBeenCalledWith('keydown', expect.any(Function))
      
      removeEventListenerSpy.mockRestore()
    })
  })

  describe('Accessibility', () => {
    it('has proper focus management', () => {
      render(<DeleteConfirmModal {...defaultProps} />)
      
      const cancelButton = screen.getByRole('button', { name: 'Cancel' })
      const deleteButton = screen.getByRole('button', { name: 'Delete Task' })
      
      // Buttons should be focusable (they don't have explicit tabindex but are naturally focusable)
      expect(cancelButton).toBeInTheDocument()
      expect(deleteButton).toBeInTheDocument()
    })

    it('has proper ARIA labels', () => {
      render(<DeleteConfirmModal {...defaultProps} />)
      
      const closeButton = screen.getByLabelText('Close modal')
      expect(closeButton).toBeInTheDocument()
    })

    it('has proper focus ring styles', () => {
      render(<DeleteConfirmModal {...defaultProps} />)
      
      const cancelButton = screen.getByRole('button', { name: 'Cancel' })
      const deleteButton = screen.getByRole('button', { name: 'Delete Task' })
      
      expect(cancelButton).toHaveClass('focus:outline-none', 'focus:ring-2')
      expect(deleteButton).toHaveClass('focus:outline-none', 'focus:ring-2')
    })
  })

  describe('Visual Design', () => {
    it('applies correct styling classes', () => {
      render(<DeleteConfirmModal {...defaultProps} />)
      
      const backdrop = screen.getByRole('heading', { name: 'Delete Task' }).closest('[class*="fixed inset-0"]')
      expect(backdrop).toHaveClass('bg-black/50', 'backdrop-blur-sm')
      
      const deleteButton = screen.getByRole('button', { name: 'Delete Task' })
      expect(deleteButton).toHaveClass('bg-red-600', 'hover:bg-red-700')
    })

    it('has proper theme support', () => {
      render(<DeleteConfirmModal {...defaultProps} />)
      
      const modalContent = screen.getByText('Are you sure you want to delete Task #123?').closest('div[class*="bg-light-component"]')
      expect(modalContent).toHaveClass('bg-light-component', 'dark:bg-dark-component')
    })

    it('displays warning icon', () => {
      render(<DeleteConfirmModal {...defaultProps} />)
      
      // Check for AlertTriangle icons (there should be 2 - one in header, one in warning)
      const icons = document.querySelectorAll('svg')
      expect(icons.length).toBeGreaterThanOrEqual(2)
    })
  })

  describe('Edge Cases', () => {
    it('handles empty task preview gracefully', () => {
      render(<DeleteConfirmModal {...defaultProps} taskPreview="" />)
      
      expect(screen.queryByText('Task Preview:')).not.toBeInTheDocument()
    })

    it('handles very long task preview', () => {
      const longPreview = 'A'.repeat(500)
      render(<DeleteConfirmModal {...defaultProps} taskPreview={longPreview} />)
      
      const previewElement = screen.getByText(longPreview)
      expect(previewElement).toHaveClass('line-clamp-3')
    })

    it('handles task ID of 0', () => {
      render(<DeleteConfirmModal {...defaultProps} taskId={0} />)
      
      expect(screen.getByText('Are you sure you want to delete Task #0?')).toBeInTheDocument()
    })

    it('handles negative task ID', () => {
      render(<DeleteConfirmModal {...defaultProps} taskId={-1} />)
      
      expect(screen.getByText('Are you sure you want to delete Task #-1?')).toBeInTheDocument()
    })
  })

  describe('Multiple Instances', () => {
    it('handles multiple rapid open/close cycles', async () => {
      const { rerender } = render(<DeleteConfirmModal {...defaultProps} isOpen={false} />)
      
      // Rapidly toggle the modal
      rerender(<DeleteConfirmModal {...defaultProps} isOpen={true} />)
      rerender(<DeleteConfirmModal {...defaultProps} isOpen={false} />)
      rerender(<DeleteConfirmModal {...defaultProps} isOpen={true} />)
      
      expect(screen.getByRole('heading', { name: 'Delete Task' })).toBeInTheDocument()
      expect(document.body.style.overflow).toBe('hidden')
    })
  })
}) 