import { describe, it, expect, beforeEach } from 'vitest'
import { render, screen } from '@testing-library/react'
import UsageStatsCard from '../../components/UsageStatsCard'

describe('UsageStatsCard', () => {
  beforeEach(() => {
    // Clear any previous renders
  })

  describe('With usage statistics available', () => {
    const defaultProps = {
      title: 'Generation Usage Statistics',
      modelName: 'gpt-4',
      promptTokens: 100,
      completionTokens: 150,
      totalTokens: 250,
      reasoningTokens: 50,
      cachedTokens: 25,
      costCredits: 0.002500,
      generationId: 'gen-123456',
      iconType: 'generation' as const
    }

    it('should render usage statistics with all fields', () => {
      render(<UsageStatsCard {...defaultProps} />)

      expect(screen.getByText('gpt-4')).toBeInTheDocument()
      expect(screen.getByText('100')).toBeInTheDocument() // prompt tokens
      expect(screen.getByText('150')).toBeInTheDocument() // completion tokens
      expect(screen.getByText('250')).toBeInTheDocument() // total tokens
      expect(screen.getByText('50')).toBeInTheDocument() // reasoning tokens
      expect(screen.getByText('25')).toBeInTheDocument() // cached tokens
      expect(screen.getByText('$0.002500')).toBeInTheDocument() // cost
    })

    it('should format large numbers with locale separators', () => {
      const propsWithLargeNumbers = {
        ...defaultProps,
        promptTokens: 1000000,
        completionTokens: 2500000,
        totalTokens: 3500000
      }

      render(<UsageStatsCard {...propsWithLargeNumbers} />)

      expect(screen.getByText('1,000,000')).toBeInTheDocument()
      expect(screen.getByText('2,500,000')).toBeInTheDocument()
      expect(screen.getByText('3,500,000')).toBeInTheDocument()
    })

    it('should handle zero cost correctly', () => {
      const propsWithZeroCost = {
        ...defaultProps,
        costCredits: 0
      }

      render(<UsageStatsCard {...propsWithZeroCost} />)

      expect(screen.getByText('$0.000000')).toBeInTheDocument()
    })

    it('should only show reasoning tokens when they exist and are greater than 0', () => {
      // Test with reasoning tokens present
      const { unmount } = render(<UsageStatsCard {...defaultProps} />)
      expect(screen.getByText('Reasoning')).toBeInTheDocument()
      unmount()
      
      // Test with reasoning tokens = 0 (should not show)
      render(<UsageStatsCard {...{ ...defaultProps, reasoningTokens: 0 }} />)
      expect(screen.queryByText('Reasoning')).not.toBeInTheDocument()
    })

    it('should not show reasoning tokens when null', () => {
      render(<UsageStatsCard {...{ ...defaultProps, reasoningTokens: null }} />)
      expect(screen.queryByText('Reasoning')).not.toBeInTheDocument()
    })

    it('should only show cached tokens when they exist and are greater than 0', () => {
      // Test with cached tokens present
      const { unmount } = render(<UsageStatsCard {...defaultProps} />)
      expect(screen.getByText('Cached')).toBeInTheDocument()
      unmount()

      // Test with cached tokens = 0 (should not show)
      render(<UsageStatsCard {...{ ...defaultProps, cachedTokens: 0 }} />)
      expect(screen.queryByText('Cached')).not.toBeInTheDocument()
    })

    it('should not show cached tokens when null', () => {
      render(<UsageStatsCard {...{ ...defaultProps, cachedTokens: null }} />)
      expect(screen.queryByText('Cached')).not.toBeInTheDocument()
    })

    it('should apply correct color classes for evaluation type', () => {
      const evaluationProps = {
        ...defaultProps,
        iconType: 'evaluation' as const
      }

      const { container } = render(<UsageStatsCard {...evaluationProps} />)
      
      // Check for evaluation-specific color class
      const totalTokenElement = screen.getByText('250').closest('.text-green-600')
      expect(totalTokenElement).toBeInTheDocument()
    })

    it('should apply correct color classes for generation type', () => {
      const { container } = render(<UsageStatsCard {...defaultProps} />)
      
      // Check for generation-specific color class
      const totalTokenElement = screen.getByText('250').closest('.text-blue-600')
      expect(totalTokenElement).toBeInTheDocument()
    })
  })

  describe('Without usage statistics (unavailable message)', () => {
    const unavailableProps = {
      title: 'Usage Statistics',
      modelName: 'claude-3-sonnet',
      showUnavailableMessage: true,
      unavailableMessage: 'Usage statistics are not available',
      unavailableSubMessage: 'This feature was added after this was created',
      iconType: 'generation' as const
    }

    it('should show unavailable message when no usage data', () => {
      render(<UsageStatsCard {...unavailableProps} />)

      expect(screen.getByText('claude-3-sonnet')).toBeInTheDocument()
      expect(screen.getByText('Usage statistics are not available')).toBeInTheDocument()
      expect(screen.queryByText('Prompt')).not.toBeInTheDocument()
      expect(screen.queryByText('Completion')).not.toBeInTheDocument()
    })

    it('should show unavailable message when explicitly requested', () => {
      const propsWithDataButUnavailableFlag = {
        ...unavailableProps,
        promptTokens: 100,
        completionTokens: 150,
        totalTokens: 250,
        showUnavailableMessage: true
      }

      render(<UsageStatsCard {...propsWithDataButUnavailableFlag} />)

      expect(screen.getByText('Usage statistics are not available')).toBeInTheDocument()
      expect(screen.queryByText('100')).not.toBeInTheDocument()
    })

    it('should show unavailable message when no usage data is present', () => {
      const propsWithoutAnyData = {
        title: 'Usage Statistics',
        modelName: 'test-model',
        iconType: 'generation' as const
      }

      render(<UsageStatsCard {...propsWithoutAnyData} />)

      expect(screen.getByText('Usage statistics are not available')).toBeInTheDocument()
    })
  })

  describe('Partial usage statistics', () => {
    it('should handle missing prompt tokens', () => {
      const propsWithoutPromptTokens = {
        title: 'Usage Statistics',
        modelName: 'test-model',
        completionTokens: 150,
        totalTokens: 150,
        costCredits: 0.001500,
        iconType: 'generation' as const
      }

      render(<UsageStatsCard {...propsWithoutPromptTokens} />)

      expect(screen.queryByText('Prompt')).not.toBeInTheDocument()
      expect(screen.getByText('Completion')).toBeInTheDocument()
      expect(screen.getByText('Total')).toBeInTheDocument()
      expect(screen.getByText('Cost')).toBeInTheDocument()
    })

    it('should handle missing completion tokens', () => {
      const propsWithoutCompletionTokens = {
        title: 'Usage Statistics',
        modelName: 'test-model',
        promptTokens: 100,
        totalTokens: 100,
        costCredits: 0.001000,
        iconType: 'generation' as const
      }

      render(<UsageStatsCard {...propsWithoutCompletionTokens} />)

      expect(screen.getByText('Prompt')).toBeInTheDocument()
      expect(screen.queryByText('Completion')).not.toBeInTheDocument()
      expect(screen.getByText('Total')).toBeInTheDocument()
      expect(screen.getByText('Cost')).toBeInTheDocument()
    })

    it('should handle only cost information', () => {
      const propsWithOnlyCost = {
        title: 'Usage Statistics',
        modelName: 'test-model',
        costCredits: 0.005000,
        iconType: 'evaluation' as const
      }

      render(<UsageStatsCard {...propsWithOnlyCost} />)

      expect(screen.queryByText('Prompt')).not.toBeInTheDocument()
      expect(screen.queryByText('Completion')).not.toBeInTheDocument()
      expect(screen.queryByText('Total')).not.toBeInTheDocument()
      expect(screen.getByText('Cost')).toBeInTheDocument()
      expect(screen.getByText('$0.005000')).toBeInTheDocument()
    })
  })

  describe('Edge cases', () => {
    it('should handle null values gracefully', () => {
      const propsWithNulls = {
        title: 'Usage Statistics',
        modelName: 'test-model',
        promptTokens: null,
        completionTokens: null,
        totalTokens: null,
        reasoningTokens: null,
        cachedTokens: null,
        costCredits: null,
        iconType: 'generation' as const
      }

      render(<UsageStatsCard {...propsWithNulls} />)

      expect(screen.getByText('Usage statistics are not available')).toBeInTheDocument()
    })

    it('should handle undefined values gracefully', () => {
      const propsWithUndefined = {
        title: 'Usage Statistics',
        modelName: 'test-model',
        iconType: 'generation' as const
      }

      render(<UsageStatsCard {...propsWithUndefined} />)

      expect(screen.getByText('Usage statistics are not available')).toBeInTheDocument()
    })

    it('should handle very small cost values', () => {
      const propsWithSmallCost = {
        title: 'Usage Statistics',
        modelName: 'test-model',
        costCredits: 0.000001,
        iconType: 'generation' as const
      }

      render(<UsageStatsCard {...propsWithSmallCost} />)

      expect(screen.getByText('$0.000001')).toBeInTheDocument()
    })
  })

  describe('Model name display', () => {
    it('should display full model name', () => {
      const props = {
        title: 'Usage Statistics',
        modelName: 'gpt-4-turbo-preview',
        totalTokens: 100,
        iconType: 'generation' as const
      }

      render(<UsageStatsCard {...props} />)

      expect(screen.getByText('gpt-4-turbo-preview')).toBeInTheDocument()
    })

    it('should handle very long model names', () => {
      const props = {
        title: 'Usage Statistics',
        modelName: 'very-long-model-name-that-might-cause-layout-issues',
        totalTokens: 100,
        iconType: 'generation' as const
      }

      render(<UsageStatsCard {...props} />)

      expect(screen.getByText('very-long-model-name-that-might-cause-layout-issues')).toBeInTheDocument()
    })
  })
})