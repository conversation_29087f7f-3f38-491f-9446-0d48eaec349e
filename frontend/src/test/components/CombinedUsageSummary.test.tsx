import { describe, it, expect } from 'vitest'
import { render, screen } from '@testing-library/react'
import CombinedUsageSummary from '../../components/CombinedUsageSummary'
import { mockCombinedUsageData } from '../mocks/usageStatsMocks'

describe('CombinedUsageSummary', () => {
  describe('With both generation and evaluation usage data', () => {
    it('should render complete usage dashboard', () => {
      render(
        <CombinedUsageSummary
          generationUsage={mockCombinedUsageData.generationUsage}
          evaluationUsage={mockCombinedUsageData.evaluationUsage}
        />
      )

      expect(screen.getByText('Usage Dashboard')).toBeInTheDocument()
      expect(screen.getByText('Generation Usage')).toBeInTheDocument()
      expect(screen.getByText('Evaluation Usage')).toBeInTheDocument()
    })

    it('should display total statistics correctly', () => {
      render(
        <CombinedUsageSummary
          generationUsage={mockCombinedUsageData.generationUsage}
          evaluationUsage={mockCombinedUsageData.evaluationUsage}
        />
      )

      // Total tokens: 750 (generation) + 900 (evaluation) = 1650
      expect(screen.getByText('1,650')).toBeInTheDocument()
      
      // Total cost: 0.007500 + 0.009000 = 0.016500
      expect(screen.getByText('$0.016500')).toBeInTheDocument()
    })

    it('should display reasoning tokens when available', () => {
      render(
        <CombinedUsageSummary
          generationUsage={mockCombinedUsageData.generationUsage}
          evaluationUsage={mockCombinedUsageData.evaluationUsage}
        />
      )

      // Total reasoning tokens: 150 (generation) + 225 (evaluation) = 375
      expect(screen.getByText('375')).toBeInTheDocument()
    })

    it('should display cached tokens when available', () => {
      render(
        <CombinedUsageSummary
          generationUsage={mockCombinedUsageData.generationUsage}
          evaluationUsage={mockCombinedUsageData.evaluationUsage}
        />
      )

      // Total cached tokens: 75 (generation) + 150 (evaluation) = 225
      expect(screen.getByText('225')).toBeInTheDocument()
    })

    it('should show model/evaluator counts correctly', () => {
      render(
        <CombinedUsageSummary
          generationUsage={mockCombinedUsageData.generationUsage}
          evaluationUsage={mockCombinedUsageData.evaluationUsage}
        />
      )

      expect(screen.getByText('3 models')).toBeInTheDocument()
      expect(screen.getByText('2 evaluators')).toBeInTheDocument()
    })

    it('should show "without data" indicators when applicable', () => {
      const generationUsageWithMissing = {
        ...mockCombinedUsageData.generationUsage,
        count: 2,
        totalGenerations: 4
      }

      const evaluationUsageWithMissing = {
        ...mockCombinedUsageData.evaluationUsage,
        count: 1,
        totalEvaluations: 3
      }

      render(
        <CombinedUsageSummary
          generationUsage={generationUsageWithMissing}
          evaluationUsage={evaluationUsageWithMissing}
        />
      )

      // For generation section: 4 total - 2 with data = 2 without data
      expect(screen.getByText('Generation Usage')).toBeInTheDocument()
      const generationWithoutData = screen.getAllByText('2 without data')[0]
      expect(generationWithoutData).toBeInTheDocument()
      
      // For evaluation section: 3 total - 1 with data = 2 without data  
      expect(screen.getByText('Evaluation Usage')).toBeInTheDocument()
      const evaluationWithoutData = screen.getAllByText('2 without data')[1]
      expect(evaluationWithoutData).toBeInTheDocument()
    })
  })

  describe('With only generation usage data', () => {
    it('should render only generation usage section', () => {
      render(
        <CombinedUsageSummary
          generationUsage={mockCombinedUsageData.generationUsage}
        />
      )

      expect(screen.getByText('Usage Dashboard')).toBeInTheDocument()
      expect(screen.getByText('Generation Usage')).toBeInTheDocument()
      expect(screen.queryByText('Evaluation Usage')).not.toBeInTheDocument()
    })

    it('should not display total statistics section when only one usage type', () => {
      render(
        <CombinedUsageSummary
          generationUsage={mockCombinedUsageData.generationUsage}
        />
      )

      // Should not show the combined totals section
      expect(screen.queryByText('Total Tokens')).not.toBeInTheDocument()
      expect(screen.queryByText('Total Cost')).not.toBeInTheDocument()
    })
  })

  describe('With only evaluation usage data', () => {
    it('should render only evaluation usage section', () => {
      render(
        <CombinedUsageSummary
          evaluationUsage={mockCombinedUsageData.evaluationUsage}
        />
      )

      expect(screen.getByText('Usage Dashboard')).toBeInTheDocument()
      expect(screen.queryByText('Generation Usage')).not.toBeInTheDocument()
      expect(screen.getByText('Evaluation Usage')).toBeInTheDocument()
    })
  })

  describe('With no usage data', () => {
    it('should not render when no data provided', () => {
      const { container } = render(<CombinedUsageSummary />)
      expect(container.firstChild).toBeNull()
    })

    it('should not render when usage data has no tokens or cost', () => {
      const emptyGenerationUsage = {
        prompt_tokens: 0,
        completion_tokens: 0,
        total_tokens: 0,
        reasoning_tokens: 0,
        cached_tokens: 0,
        cost_credits: 0,
        count: 0,
        totalGenerations: 0
      }

      const emptyEvaluationUsage = {
        prompt_tokens: 0,
        completion_tokens: 0,
        total_tokens: 0,
        reasoning_tokens: 0,
        cached_tokens: 0,
        cost_credits: 0,
        count: 0,
        totalEvaluations: 0
      }

      const { container } = render(
        <CombinedUsageSummary
          generationUsage={emptyGenerationUsage}
          evaluationUsage={emptyEvaluationUsage}
        />
      )
      
      expect(container.firstChild).toBeNull()
    })
  })

  describe('Edge cases and special scenarios', () => {
    it('should handle zero reasoning tokens correctly', () => {
      const usageWithoutReasoning = {
        ...mockCombinedUsageData.generationUsage,
        reasoning_tokens: 0
      }

      const evaluationWithoutReasoning = {
        ...mockCombinedUsageData.evaluationUsage,
        reasoning_tokens: 0
      }

      render(
        <CombinedUsageSummary
          generationUsage={usageWithoutReasoning}
          evaluationUsage={evaluationWithoutReasoning}
        />
      )

      // Should not show reasoning tokens section when total is 0
      expect(screen.queryByText('Reasoning')).not.toBeInTheDocument()
    })

    it('should handle zero cached tokens correctly', () => {
      const usageWithoutCached = {
        ...mockCombinedUsageData.generationUsage,
        cached_tokens: 0
      }

      const evaluationWithoutCached = {
        ...mockCombinedUsageData.evaluationUsage,
        cached_tokens: 0
      }

      render(
        <CombinedUsageSummary
          generationUsage={usageWithoutCached}
          evaluationUsage={evaluationWithoutCached}
        />
      )

      // Should not show cached tokens section when total is 0
      expect(screen.queryByText('Cached')).not.toBeInTheDocument()
    })

    it('should handle very large numbers correctly', () => {
      const largeGenerationUsage = {
        prompt_tokens: 1000000,
        completion_tokens: 2000000,
        total_tokens: 3000000,
        reasoning_tokens: 500000,
        cached_tokens: 250000,
        cost_credits: 30.000000,
        count: 10,
        totalGenerations: 10
      }

      const largeEvaluationUsage = {
        prompt_tokens: 800000,
        completion_tokens: 1200000,
        total_tokens: 2000000,
        reasoning_tokens: 400000,
        cached_tokens: 200000,
        cost_credits: 20.000000,
        count: 5,
        totalEvaluations: 5
      }

      render(
        <CombinedUsageSummary
          generationUsage={largeGenerationUsage}
          evaluationUsage={largeEvaluationUsage}
        />
      )

      // Check large number formatting
      expect(screen.getByText('5,000,000')).toBeInTheDocument() // total tokens
      expect(screen.getByText('$50.000000')).toBeInTheDocument() // total cost
      expect(screen.getByText('900,000')).toBeInTheDocument() // total reasoning
      expect(screen.getByText('450,000')).toBeInTheDocument() // total cached
    })

    it('should handle singular vs plural correctly', () => {
      const singleModelUsage = {
        ...mockCombinedUsageData.generationUsage,
        count: 1,
        totalGenerations: 1
      }

      const singleEvaluatorUsage = {
        ...mockCombinedUsageData.evaluationUsage,
        count: 1,
        totalEvaluations: 1
      }

      render(
        <CombinedUsageSummary
          generationUsage={singleModelUsage}
          evaluationUsage={singleEvaluatorUsage}
        />
      )

      expect(screen.getByText('1 model')).toBeInTheDocument()
      expect(screen.getByText('1 evaluator')).toBeInTheDocument()
    })
  })

  describe('Component structure and styling', () => {
    it('should have correct CSS classes for styling', () => {
      const { container } = render(
        <CombinedUsageSummary
          generationUsage={mockCombinedUsageData.generationUsage}
          evaluationUsage={mockCombinedUsageData.evaluationUsage}
        />
      )

      // Check for main container classes
      const mainContainer = container.firstChild as HTMLElement
      expect(mainContainer).toHaveClass('bg-gradient-to-br')
      expect(mainContainer).toHaveClass('shadow-md')
      expect(mainContainer).toHaveClass('rounded-md')
    })

    it('should render icons correctly', () => {
      const { container } = render(
        <CombinedUsageSummary
          generationUsage={mockCombinedUsageData.generationUsage}
          evaluationUsage={mockCombinedUsageData.evaluationUsage}
        />
      )

      // SVG icons should be present
      const svgElements = container.querySelectorAll('svg')
      expect(svgElements.length).toBeGreaterThan(0)
      
      // Should have dashboard icon, generation icon, and evaluation icon
      expect(svgElements.length).toBeGreaterThanOrEqual(3)
    })
  })
})