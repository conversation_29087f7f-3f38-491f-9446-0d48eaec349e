import React from 'react'
import { render, screen, fireEvent } from '@testing-library/react'
import { describe, it, expect, vi, beforeEach } from 'vitest'
import OutputDisplay from '../../components/OutputDisplay'
import { Generation } from '../../api/apiClient'

// Mock LoadingSpinner component
vi.mock('../../components/LoadingSpinner', () => ({
  default: ({ message, size }: { message?: string; size?: string }) => (
    <div data-testid="loading-spinner" data-size={size}>
      {message || 'Loading...'}
    </div>
  )
}))

// Mock SingleOutputDisplay component
vi.mock('../../components/SingleOutputDisplay', () => ({
  default: ({ generation }: { generation: Generation }) => (
    <div data-testid="single-output-display">
      <div>Task ID: {generation.task_id}</div>
      <div>Model: {generation.model_id_used}</div>
      <div>Output: {generation.output_text}</div>
    </div>
  )
}))

// Mock Resizable component
vi.mock('re-resizable', () => ({
  Resizable: ({ children, className, defaultSize, minHeight, enable, handleStyles, handleClasses, ...props }: any) => (
    <div 
      data-testid="resizable-container"
      className={className}
      data-default-height={defaultSize?.height}
      data-min-height={minHeight}
      data-enable-bottom={enable?.bottom}
      {...props}
    >
      {children}
    </div>
  )
}))

describe('OutputDisplay', () => {
  const mockGeneration: Generation = {
    id: 1,
    task_id: 123,
    model_id_used: 'gpt-4',
    blind_id: 'blind-123',
    output_text: 'This is a sample generated output from the model.',
    error_message: null,
    created_at: '2024-01-01T00:00:00Z'
  }

  const defaultProps = {
    selectedGeneration: mockGeneration,
    isLoading: false
  }

  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('Loading State', () => {
    it('shows loading spinner when loading and no generation', () => {
      render(<OutputDisplay selectedGeneration={null} isLoading={true} />)
      
      const spinner = screen.getByTestId('loading-spinner')
      expect(spinner).toBeInTheDocument()
      expect(spinner).toHaveTextContent('Generating responses...')
      expect(spinner).toHaveAttribute('data-size', 'large')
    })

    it('does not show loading when generation exists even if loading', () => {
      render(<OutputDisplay {...defaultProps} isLoading={true} />)
      
      expect(screen.queryByTestId('loading-spinner')).not.toBeInTheDocument()
      expect(screen.getByTestId('single-output-display')).toBeInTheDocument()
    })
  })

  describe('Empty State', () => {
    it('shows empty state when no generation and not loading', () => {
      render(<OutputDisplay selectedGeneration={null} isLoading={false} />)
      
      expect(screen.getByText('No outputs generated yet. Please wait for model responses.')).toBeInTheDocument()
      expect(screen.queryByTestId('loading-spinner')).not.toBeInTheDocument()
      expect(screen.queryByTestId('single-output-display')).not.toBeInTheDocument()
    })

    it('applies correct styling to empty state container', () => {
      render(<OutputDisplay selectedGeneration={null} isLoading={false} />)
      
      const emptyContainer = screen.getByText('No outputs generated yet. Please wait for model responses.').closest('div')
      expect(emptyContainer).toHaveClass(
        'bg-light-component-subtle',
        'dark:bg-dark-component-subtle',
        'p-6',
        'rounded-lg',
        'border',
        'border-light-border',
        'dark:border-dark-border',
        'text-center'
      )
    })
  })

  describe('Content Display', () => {
    it('renders SingleOutputDisplay when generation exists', () => {
      render(<OutputDisplay {...defaultProps} />)
      
      const outputDisplay = screen.getByTestId('single-output-display')
      expect(outputDisplay).toBeInTheDocument()
      expect(outputDisplay).toHaveTextContent('Task ID: 123')
      expect(outputDisplay).toHaveTextContent('Model: gpt-4')
      expect(outputDisplay).toHaveTextContent('Output: This is a sample generated output from the model.')
    })

    it('passes generation data correctly to SingleOutputDisplay', () => {
      const customGeneration: Generation = {
        ...mockGeneration,
        id: 999,
        task_id: 456,
        model_id_used: 'claude-3-sonnet',
        output_text: 'Custom output text'
      }

      render(<OutputDisplay selectedGeneration={customGeneration} isLoading={false} />)
      
      const outputDisplay = screen.getByTestId('single-output-display')
      expect(outputDisplay).toHaveTextContent('Task ID: 456')
      expect(outputDisplay).toHaveTextContent('Model: claude-3-sonnet')
      expect(outputDisplay).toHaveTextContent('Output: Custom output text')
    })
  })

  describe('Scrollbar Mode (Default)', () => {
    it('renders with Resizable container by default', () => {
      render(<OutputDisplay {...defaultProps} />)
      
      const resizableContainer = screen.getByTestId('resizable-container')
      expect(resizableContainer).toBeInTheDocument()
    })

    it('configures Resizable with correct properties', () => {
      render(<OutputDisplay {...defaultProps} />)
      
      const resizableContainer = screen.getByTestId('resizable-container')
      expect(resizableContainer).toHaveAttribute('data-default-height', '500')
      expect(resizableContainer).toHaveAttribute('data-min-height', '300')
      expect(resizableContainer).toHaveAttribute('data-enable-bottom', 'true')
    })

    it('applies correct styling to resizable container', () => {
      render(<OutputDisplay {...defaultProps} />)
      
      const resizableContainer = screen.getByTestId('resizable-container')
      expect(resizableContainer).toHaveClass(
        'rounded-xl',
        'border',
        'border-light-border/60',
        'dark:border-dark-border/60',
        'bg-gradient-to-br',
        'from-light-component',
        'to-light-background',
        'dark:from-dark-component',
        'dark:to-dark-background',
        'shadow-sm',
        'overflow-hidden'
      )
    })

    it('applies scrollable content styling', () => {
      render(<OutputDisplay {...defaultProps} />)
      
      // The mock SingleOutputDisplay is directly inside the resizable container
      // We need to check the actual container that has the styling
      const resizableContainer = screen.getByTestId('resizable-container')
      expect(resizableContainer).toBeInTheDocument()
      
      // The styling is applied to the container, not the mock component
      expect(resizableContainer).toHaveClass('overflow-hidden')
    })
  })

  describe('No Scrollbar Mode', () => {
    it('renders without Resizable when showScrollbar is false', () => {
      render(<OutputDisplay {...defaultProps} showScrollbar={false} />)
      
      expect(screen.queryByTestId('resizable-container')).not.toBeInTheDocument()
      expect(screen.getByTestId('single-output-display')).toBeInTheDocument()
    })

    it('applies correct styling in no-scrollbar mode', () => {
      render(<OutputDisplay {...defaultProps} showScrollbar={false} />)
      
      // In no-scrollbar mode, find the outer container
      const outputDisplay = screen.getByTestId('single-output-display')
      const outerContainer = outputDisplay.parentElement?.parentElement
      expect(outerContainer).toHaveClass(
        'rounded-xl',
        'border',
        'border-light-border/60',
        'dark:border-dark-border/60',
        'bg-gradient-to-br',
        'from-light-component',
        'to-light-background',
        'dark:from-dark-component',
        'dark:to-dark-background',
        'shadow-sm'
      )
    })

    it('applies correct content styling in no-scrollbar mode', () => {
      render(<OutputDisplay {...defaultProps} showScrollbar={false} />)
      
      // In no-scrollbar mode, the content container is the direct parent of the mock
      const outputDisplay = screen.getByTestId('single-output-display')
      const contentContainer = outputDisplay.parentElement
      expect(contentContainer).toHaveClass(
        'p-6',
        'sm:p-8',
        'text-light-primary',
        'dark:text-dark-primary'
      )
      // Should not have scrolling classes
      expect(contentContainer).not.toHaveClass('h-full', 'overflow-auto', 'custom-scrollbar')
    })
  })

  describe('Mouse Interactions', () => {
    it('handles mouse enter and leave events on content container', () => {
      render(<OutputDisplay {...defaultProps} />)
      
      const contentContainer = screen.getByTestId('single-output-display').closest('div')
      
      // Mouse enter
      fireEvent.mouseEnter(contentContainer!)
      // Mouse leave
      fireEvent.mouseLeave(contentContainer!)
      
      // Component should handle these events without errors
      expect(contentContainer).toBeInTheDocument()
    })
  })

  describe('Props Handling', () => {
    it('handles showScrollbar prop correctly', () => {
      const { rerender } = render(<OutputDisplay {...defaultProps} showScrollbar={true} />)
      expect(screen.getByTestId('resizable-container')).toBeInTheDocument()
      
      rerender(<OutputDisplay {...defaultProps} showScrollbar={false} />)
      expect(screen.queryByTestId('resizable-container')).not.toBeInTheDocument()
    })

    it('handles generation prop updates', () => {
      const { rerender } = render(<OutputDisplay {...defaultProps} />)
      expect(screen.getByText('Task ID: 123')).toBeInTheDocument()
      
      const newGeneration = { ...mockGeneration, task_id: 999 }
      rerender(<OutputDisplay selectedGeneration={newGeneration} isLoading={false} />)
      expect(screen.getByText('Task ID: 999')).toBeInTheDocument()
    })

    it('handles loading state changes', () => {
      const { rerender } = render(<OutputDisplay selectedGeneration={null} isLoading={false} />)
      expect(screen.getByText('No outputs generated yet. Please wait for model responses.')).toBeInTheDocument()
      
      rerender(<OutputDisplay selectedGeneration={null} isLoading={true} />)
      expect(screen.getByTestId('loading-spinner')).toBeInTheDocument()
    })
  })

  describe('Edge Cases', () => {
    it('handles null generation gracefully', () => {
      render(<OutputDisplay selectedGeneration={null} isLoading={false} />)
      
      expect(screen.getByText('No outputs generated yet. Please wait for model responses.')).toBeInTheDocument()
      expect(screen.queryByTestId('single-output-display')).not.toBeInTheDocument()
    })

    it('handles generation with empty output', () => {
      const emptyGeneration = { ...mockGeneration, output_text: '' }
      render(<OutputDisplay selectedGeneration={emptyGeneration} isLoading={false} />)
      
      const outputDisplay = screen.getByTestId('single-output-display')
      expect(outputDisplay).toBeInTheDocument()
      // The mock shows "Output:" without a space after the colon
      expect(outputDisplay).toHaveTextContent('Output:')
    })

    it('handles generation with special characters', () => {
      const specialGeneration = { 
        ...mockGeneration, 
        output_text: 'Output with special chars: <>&"\'🚀' 
      }
      render(<OutputDisplay selectedGeneration={specialGeneration} isLoading={false} />)
      
      const outputDisplay = screen.getByTestId('single-output-display')
      expect(outputDisplay).toHaveTextContent('Output with special chars: <>&"\'🚀')
    })
  })

  describe('Accessibility', () => {
    it('provides meaningful content structure', () => {
      render(<OutputDisplay {...defaultProps} />)
      
      const outputDisplay = screen.getByTestId('single-output-display')
      expect(outputDisplay).toBeInTheDocument()
    })

    it('maintains proper text contrast classes', () => {
      render(<OutputDisplay {...defaultProps} />)
      
      // Check the resizable container or its content for theme classes
      const resizableContainer = screen.getByTestId('resizable-container')
      expect(resizableContainer).toBeInTheDocument()
      
      // The theme classes are applied to the actual component, not the mock
      // Just verify the component renders correctly
      expect(screen.getByTestId('single-output-display')).toBeInTheDocument()
    })

    it('provides accessible empty state message', () => {
      render(<OutputDisplay selectedGeneration={null} isLoading={false} />)
      
      const emptyMessage = screen.getByText('No outputs generated yet. Please wait for model responses.')
      expect(emptyMessage).toBeInTheDocument()
      expect(emptyMessage).toHaveClass('text-light-secondary', 'dark:text-dark-secondary')
    })
  })

  describe('Theme Support', () => {
    it('applies light theme classes', () => {
      render(<OutputDisplay {...defaultProps} />)
      
      const resizableContainer = screen.getByTestId('resizable-container')
      expect(resizableContainer).toHaveClass(
        'border-light-border/60',
        'from-light-component',
        'to-light-background'
      )
    })

    it('applies dark theme classes', () => {
      render(<OutputDisplay {...defaultProps} />)
      
      const resizableContainer = screen.getByTestId('resizable-container')
      expect(resizableContainer).toHaveClass(
        'dark:border-dark-border/60',
        'dark:from-dark-component',
        'dark:to-dark-background'
      )
    })

    it('applies theme classes to empty state', () => {
      render(<OutputDisplay selectedGeneration={null} isLoading={false} />)
      
      const emptyContainer = screen.getByText('No outputs generated yet. Please wait for model responses.').closest('div')
      expect(emptyContainer).toHaveClass(
        'bg-light-component-subtle',
        'dark:bg-dark-component-subtle',
        'border-light-border',
        'dark:border-dark-border'
      )
    })
  })
}) 