import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { describe, it, expect, vi, beforeEach } from 'vitest'
import ModelSelector from '../../components/ModelSelector'

describe('ModelSelector', () => {
  const mockModels = [
    'openai/gpt-4',
    'openai/gpt-3.5-turbo',
    'anthropic/claude-3-sonnet',
    'anthropic/claude-3-haiku',
    'google/gemini-pro',
    'meta/llama-2-70b'
  ]

  const mockOnSelectionChange = vi.fn()

  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('Basic Rendering', () => {
    it('renders with default title', () => {
      render(
        <ModelSelector
          availableModels={mockModels}
          onSelectionChange={mockOnSelectionChange}
        />
      )

      expect(screen.getByText('Select Generator Models')).toBeInTheDocument()
    })

    it('renders with custom title', () => {
      render(
        <ModelSelector
          availableModels={mockModels}
          onSelectionChange={mockOnSelectionChange}
          title="Choose Models"
        />
      )

      expect(screen.getByText('Choose Models')).toBeInTheDocument()
    })

    it('renders all available models', () => {
      render(
        <ModelSelector
          availableModels={mockModels}
          onSelectionChange={mockOnSelectionChange}
        />
      )

      // Check provider names (use getAllByText for duplicates)
      expect(screen.getAllByText('openai')).toHaveLength(2) // gpt-4 and gpt-3.5-turbo
      expect(screen.getAllByText('anthropic')).toHaveLength(2) // claude-3-sonnet and claude-3-haiku
      expect(screen.getByText('google')).toBeInTheDocument()
      expect(screen.getByText('meta')).toBeInTheDocument()

      // Check model names
      expect(screen.getByText('gpt-4')).toBeInTheDocument()
      expect(screen.getByText('gpt-3.5-turbo')).toBeInTheDocument()
      expect(screen.getByText('claude-3-sonnet')).toBeInTheDocument()
      expect(screen.getByText('claude-3-haiku')).toBeInTheDocument()
      expect(screen.getByText('gemini-pro')).toBeInTheDocument()
      expect(screen.getByText('llama-2-70b')).toBeInTheDocument()
    })

    it('renders model without provider correctly', () => {
      render(
        <ModelSelector
          availableModels={['standalone-model']}
          onSelectionChange={mockOnSelectionChange}
        />
      )

      expect(screen.getByText('standalone-model')).toBeInTheDocument()
      // Should not have a separate provider name
      expect(screen.queryByText('standalone')).not.toBeInTheDocument()
    })
  })

  describe('Model Selection', () => {
    it('allows selecting a single model', async () => {
      render(
        <ModelSelector
          availableModels={mockModels}
          onSelectionChange={mockOnSelectionChange}
        />
      )

      const gpt4Checkbox = screen.getByRole('checkbox', { name: /gpt-4/ })
      fireEvent.click(gpt4Checkbox)

      await waitFor(() => {
        expect(mockOnSelectionChange).toHaveBeenCalledWith(['openai/gpt-4'])
      })
      expect(gpt4Checkbox).toBeChecked()
    })

    it('allows selecting multiple models', async () => {
      render(
        <ModelSelector
          availableModels={mockModels}
          onSelectionChange={mockOnSelectionChange}
        />
      )

      const gpt4Checkbox = screen.getByRole('checkbox', { name: /gpt-4/ })
      const claudeCheckbox = screen.getByRole('checkbox', { name: /claude-3-sonnet/ })

      fireEvent.click(gpt4Checkbox)
      fireEvent.click(claudeCheckbox)

      await waitFor(() => {
        expect(mockOnSelectionChange).toHaveBeenCalledWith(['openai/gpt-4', 'anthropic/claude-3-sonnet'])
      })

      expect(gpt4Checkbox).toBeChecked()
      expect(claudeCheckbox).toBeChecked()
    })

    it('allows deselecting models', async () => {
      render(
        <ModelSelector
          availableModels={mockModels}
          onSelectionChange={mockOnSelectionChange}
          initialSelection={['openai/gpt-4']}
        />
      )

      const gpt4Checkbox = screen.getByRole('checkbox', { name: /gpt-4/ })
      expect(gpt4Checkbox).toBeChecked()

      fireEvent.click(gpt4Checkbox)

      await waitFor(() => {
        expect(mockOnSelectionChange).toHaveBeenCalledWith([])
      })
      expect(gpt4Checkbox).not.toBeChecked()
    })
  })

  describe('Initial Selection', () => {
    it('renders with initial selection', () => {
      render(
        <ModelSelector
          availableModels={mockModels}
          onSelectionChange={mockOnSelectionChange}
          initialSelection={['openai/gpt-4', 'anthropic/claude-3-sonnet']}
        />
      )

      const gpt4Checkbox = screen.getByRole('checkbox', { name: /gpt-4/ })
      const claudeCheckbox = screen.getByRole('checkbox', { name: /claude-3-sonnet/ })
      const gpt35Checkbox = screen.getByRole('checkbox', { name: /gpt-3.5-turbo/ })

      expect(gpt4Checkbox).toBeChecked()
      expect(claudeCheckbox).toBeChecked()
      expect(gpt35Checkbox).not.toBeChecked()
    })

    it('updates selection when initialSelection prop changes', async () => {
      const { rerender } = render(
        <ModelSelector
          availableModels={mockModels}
          onSelectionChange={mockOnSelectionChange}
          initialSelection={['openai/gpt-4']}
        />
      )

      expect(screen.getByRole('checkbox', { name: /gpt-4/ })).toBeChecked()

      rerender(
        <ModelSelector
          availableModels={mockModels}
          onSelectionChange={mockOnSelectionChange}
          initialSelection={['anthropic/claude-3-sonnet']}
        />
      )

      await waitFor(() => {
        expect(screen.getByRole('checkbox', { name: /claude-3-sonnet/ })).toBeChecked()
        expect(screen.getByRole('checkbox', { name: /gpt-4/ })).not.toBeChecked()
      })
    })
  })

  describe('Disabled State', () => {
    it('disables all checkboxes when disabled prop is true', () => {
      render(
        <ModelSelector
          availableModels={mockModels}
          onSelectionChange={mockOnSelectionChange}
          disabled={true}
        />
      )

      const checkboxes = screen.getAllByRole('checkbox')
      checkboxes.forEach(checkbox => {
        expect(checkbox).toBeDisabled()
      })
    })

    it('applies disabled styling when disabled', () => {
      render(
        <ModelSelector
          availableModels={mockModels}
          onSelectionChange={mockOnSelectionChange}
          disabled={true}
        />
      )

      const labels = screen.getAllByRole('checkbox').map(checkbox => checkbox.closest('label'))
      labels.forEach(label => {
        expect(label).toHaveClass('opacity-50', 'cursor-not-allowed')
      })
    })

    it('does not change state when disabled checkbox is clicked', () => {
      render(
        <ModelSelector
          availableModels={mockModels}
          onSelectionChange={mockOnSelectionChange}
          disabled={true}
        />
      )

      const gpt4Checkbox = screen.getByRole('checkbox', { name: /gpt-4/ })
      
      // Checkbox should be disabled
      expect(gpt4Checkbox).toBeDisabled()
      
      // Even if we try to click, the checkbox state shouldn't change
      expect(gpt4Checkbox).not.toBeChecked()
      
      // The disabled checkbox should not be interactive
      expect(gpt4Checkbox).toHaveAttribute('disabled')
    })
  })

  describe('Validation', () => {
    it('shows error message when no models are selected', () => {
      render(
        <ModelSelector
          availableModels={mockModels}
          onSelectionChange={mockOnSelectionChange}
        />
      )

      expect(screen.getByText('Please select at least one model')).toBeInTheDocument()
    })

    it('hides error message when models are selected', async () => {
      render(
        <ModelSelector
          availableModels={mockModels}
          onSelectionChange={mockOnSelectionChange}
        />
      )

      expect(screen.getByText('Please select at least one model')).toBeInTheDocument()

      const gpt4Checkbox = screen.getByRole('checkbox', { name: /gpt-4/ })
      fireEvent.click(gpt4Checkbox)

      await waitFor(() => {
        expect(screen.queryByText('Please select at least one model')).not.toBeInTheDocument()
      })
    })

    it('shows error message again when all models are deselected', async () => {
      render(
        <ModelSelector
          availableModels={mockModels}
          onSelectionChange={mockOnSelectionChange}
          initialSelection={['openai/gpt-4']}
        />
      )

      expect(screen.queryByText('Please select at least one model')).not.toBeInTheDocument()

      const gpt4Checkbox = screen.getByRole('checkbox', { name: /gpt-4/ })
      fireEvent.click(gpt4Checkbox)

      await waitFor(() => {
        expect(screen.getByText('Please select at least one model')).toBeInTheDocument()
      })
    })
  })

  describe('Styling and Accessibility', () => {
    it('applies correct styling for selected models', () => {
      render(
        <ModelSelector
          availableModels={mockModels}
          onSelectionChange={mockOnSelectionChange}
          initialSelection={['openai/gpt-4']}
        />
      )

      const selectedLabel = screen.getByRole('checkbox', { name: /gpt-4/ }).closest('label')
      const unselectedLabel = screen.getByRole('checkbox', { name: /gpt-3.5-turbo/ }).closest('label')

      expect(selectedLabel).toHaveClass('border-light-accent', 'dark:border-dark-accent')
      expect(unselectedLabel).toHaveClass('border-light-border/30', 'dark:border-dark-border/30')
    })

    it('has proper accessibility attributes', () => {
      render(
        <ModelSelector
          availableModels={mockModels}
          onSelectionChange={mockOnSelectionChange}
        />
      )

      const checkboxes = screen.getAllByRole('checkbox')
      checkboxes.forEach(checkbox => {
        expect(checkbox).toHaveAttribute('type', 'checkbox')
      })
    })

    it('renders Bot icons for all models', () => {
      render(
        <ModelSelector
          availableModels={mockModels}
          onSelectionChange={mockOnSelectionChange}
        />
      )

      // Each model should have a Bot icon (lucide-react Bot component)
      const labels = screen.getAllByRole('checkbox').map(checkbox => checkbox.closest('label'))
      expect(labels).toHaveLength(mockModels.length)
    })
  })

  describe('Edge Cases', () => {
    it('handles empty model list', () => {
      render(
        <ModelSelector
          availableModels={[]}
          onSelectionChange={mockOnSelectionChange}
        />
      )

      expect(screen.getByText('Please select at least one model')).toBeInTheDocument()
      expect(screen.queryByRole('checkbox')).not.toBeInTheDocument()
    })

    it('handles models with special characters', () => {
      const specialModels = ['provider/model-with-dashes', 'provider/model_with_underscores', 'provider/model.with.dots']
      
      render(
        <ModelSelector
          availableModels={specialModels}
          onSelectionChange={mockOnSelectionChange}
        />
      )

      expect(screen.getByText('model-with-dashes')).toBeInTheDocument()
      expect(screen.getByText('model_with_underscores')).toBeInTheDocument()
      expect(screen.getByText('model.with.dots')).toBeInTheDocument()
    })

    it('handles very long model names', () => {
      const longModels = ['provider/very-long-model-name-that-might-cause-layout-issues']
      
      render(
        <ModelSelector
          availableModels={longModels}
          onSelectionChange={mockOnSelectionChange}
        />
      )

      expect(screen.getByText('very-long-model-name-that-might-cause-layout-issues')).toBeInTheDocument()
    })
  })
}) 