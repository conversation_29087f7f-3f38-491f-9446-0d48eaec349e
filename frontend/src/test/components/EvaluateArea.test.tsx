import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { describe, it, expect, vi, beforeEach } from 'vitest'
import EvaluateArea from '../../components/EvaluateArea'

// Mock ModelSelector component
vi.mock('../../components/ModelSelector', () => ({
  default: ({ title, availableModels, onSelectionChange, disabled }: any) => (
    <div data-testid="model-selector">
      <h3>{title}</h3>
      <div>Available models: {availableModels.length}</div>
      <button 
        onClick={() => onSelectionChange(['gpt-4', 'claude-3'])}
        disabled={disabled}
        data-testid="select-models"
      >
        Select Models
      </button>
    </div>
  )
}))

describe('EvaluateArea', () => {
  const defaultProps = {
    availableModels: ['gpt-4', 'claude-3', 'gemini-pro'],
    onEvaluate: vi.fn(),
    isLoading: false,
    disabled: false,
    validOutputsCount: 3
  }

  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('Insufficient Outputs Warning', () => {
    it('shows warning when validOutputsCount is less than 2', () => {
      render(<EvaluateArea {...defaultProps} validOutputsCount={1} />)
      
      expect(screen.getByText(/Need at least 2 valid outputs to perform evaluation/)).toBeInTheDocument()
      expect(screen.getByText(/Currently have 1 valid output\./)).toBeInTheDocument()
      
      // Should not show the evaluation form
      expect(screen.queryByTestId('model-selector')).not.toBeInTheDocument()
    })

    it('shows warning with plural form for 0 outputs', () => {
      render(<EvaluateArea {...defaultProps} validOutputsCount={0} />)
      
      expect(screen.getByText(/Currently have 0 valid outputs\./)).toBeInTheDocument()
    })

    it('does not show warning when validOutputsCount is 2 or more', () => {
      render(<EvaluateArea {...defaultProps} validOutputsCount={2} />)
      
      expect(screen.queryByText(/Need at least 2 valid outputs/)).not.toBeInTheDocument()
      expect(screen.getByTestId('model-selector')).toBeInTheDocument()
    })
  })

  describe('Model Selection', () => {
    it('renders ModelSelector with correct props', () => {
      render(<EvaluateArea {...defaultProps} />)
      
      expect(screen.getByText('Select Evaluator Models')).toBeInTheDocument()
      expect(screen.getByText('Available models: 3')).toBeInTheDocument()
    })

    it('passes disabled state to ModelSelector', () => {
      render(<EvaluateArea {...defaultProps} disabled={true} />)
      
      const selectButton = screen.getByTestId('select-models')
      expect(selectButton).toBeDisabled()
    })

    it('passes loading state to ModelSelector', () => {
      render(<EvaluateArea {...defaultProps} isLoading={true} />)
      
      const selectButton = screen.getByTestId('select-models')
      expect(selectButton).toBeDisabled()
    })
  })

  describe('Blind IDs Checkbox', () => {
    it('renders blind IDs checkbox checked by default', () => {
      render(<EvaluateArea {...defaultProps} />)
      
      const checkbox = screen.getByLabelText('Use Blind IDs for this Evaluation')
      expect(checkbox).toBeChecked()
    })

    it('toggles blind IDs checkbox', () => {
      render(<EvaluateArea {...defaultProps} />)
      
      const checkbox = screen.getByLabelText('Use Blind IDs for this Evaluation')
      fireEvent.click(checkbox)
      
      expect(checkbox).not.toBeChecked()
    })

    it('disables blind IDs checkbox when loading', () => {
      render(<EvaluateArea {...defaultProps} isLoading={true} />)
      
      const checkbox = screen.getByLabelText('Use Blind IDs for this Evaluation')
      expect(checkbox).toBeDisabled()
    })

    it('disables blind IDs checkbox when disabled', () => {
      render(<EvaluateArea {...defaultProps} disabled={true} />)
      
      const checkbox = screen.getByLabelText('Use Blind IDs for this Evaluation')
      expect(checkbox).toBeDisabled()
    })
  })

  describe('Custom Prompt Functionality', () => {
    it('renders custom prompt checkbox unchecked by default', () => {
      render(<EvaluateArea {...defaultProps} />)
      
      const checkbox = screen.getByLabelText('Use Custom Evaluation Prompt')
      expect(checkbox).not.toBeChecked()
    })

    it('shows custom prompt textarea when checkbox is checked', () => {
      render(<EvaluateArea {...defaultProps} />)
      
      const checkbox = screen.getByLabelText('Use Custom Evaluation Prompt')
      fireEvent.click(checkbox)
      
      expect(screen.getByLabelText('Custom Evaluation Instructions')).toBeInTheDocument()
      expect(screen.getByPlaceholderText('Enter your custom evaluation instructions...')).toBeInTheDocument()
    })

    it('hides custom prompt textarea when checkbox is unchecked', () => {
      render(<EvaluateArea {...defaultProps} />)
      
      const checkbox = screen.getByLabelText('Use Custom Evaluation Prompt')
      fireEvent.click(checkbox)
      fireEvent.click(checkbox)
      
      expect(screen.queryByLabelText('Custom Evaluation Instructions')).not.toBeInTheDocument()
    })

    it('allows typing in custom prompt textarea', () => {
      render(<EvaluateArea {...defaultProps} />)
      
      const checkbox = screen.getByLabelText('Use Custom Evaluation Prompt')
      fireEvent.click(checkbox)
      
      const textarea = screen.getByLabelText('Custom Evaluation Instructions')
      fireEvent.change(textarea, { target: { value: 'Custom evaluation prompt' } })
      
      expect(textarea).toHaveValue('Custom evaluation prompt')
    })

    it('clears custom prompt when clear button is clicked', () => {
      render(<EvaluateArea {...defaultProps} />)
      
      const checkbox = screen.getByLabelText('Use Custom Evaluation Prompt')
      fireEvent.click(checkbox)
      
      const textarea = screen.getByLabelText('Custom Evaluation Instructions')
      fireEvent.change(textarea, { target: { value: 'Custom evaluation prompt' } })
      
      const clearButton = screen.getByText('Clear')
      fireEvent.click(clearButton)
      
      expect(textarea).toHaveValue('')
    })

    it('disables clear button when textarea is empty', () => {
      render(<EvaluateArea {...defaultProps} />)
      
      const checkbox = screen.getByLabelText('Use Custom Evaluation Prompt')
      fireEvent.click(checkbox)
      
      const clearButton = screen.getByText('Clear')
      expect(clearButton).toBeDisabled()
    })
  })

  describe('Default Prompt Display', () => {
    it('shows default prompt when show button is clicked', () => {
      render(<EvaluateArea {...defaultProps} />)
      
      const showButton = screen.getByText('Show Default Prompt')
      fireEvent.click(showButton)
      
      expect(screen.getByText('Default Evaluation Prompt')).toBeInTheDocument()
      expect(screen.getByText(/Evaluate the quality of the generated outputs/)).toBeInTheDocument()
      expect(screen.getByText('Hide Default Prompt')).toBeInTheDocument()
    })

    it('hides default prompt when hide button is clicked', () => {
      render(<EvaluateArea {...defaultProps} />)
      
      const showButton = screen.getByText('Show Default Prompt')
      fireEvent.click(showButton)
      
      const hideButton = screen.getByText('Hide Default Prompt')
      fireEvent.click(hideButton)
      
      expect(screen.queryByText('Default Evaluation Prompt')).not.toBeInTheDocument()
      expect(screen.getByText('Show Default Prompt')).toBeInTheDocument()
    })

    it('copies default prompt to custom prompt when copy button is clicked', () => {
      render(<EvaluateArea {...defaultProps} />)
      
      const showButton = screen.getByText('Show Default Prompt')
      fireEvent.click(showButton)
      
      const copyButton = screen.getByText('Copy to Custom')
      fireEvent.click(copyButton)
      
      // Should enable custom prompt and populate textarea
      const textarea = screen.getByLabelText('Custom Evaluation Instructions')
      expect(textarea).toHaveValue('Evaluate the quality of the generated outputs based on the original task prompt. Consider relevance, completeness, coherence, and accuracy.')
      
      const customCheckbox = screen.getByLabelText('Use Custom Evaluation Prompt')
      expect(customCheckbox).toBeChecked()
    })
  })

  describe('Evaluation Button', () => {
    it('shows "Generate Evaluation Report" text by default', () => {
      render(<EvaluateArea {...defaultProps} />)
      
      expect(screen.getByText('Generate Evaluation Report')).toBeInTheDocument()
    })

    it('shows "Evaluating..." text when loading', () => {
      render(<EvaluateArea {...defaultProps} isLoading={true} />)
      
      expect(screen.getByText('Evaluating...')).toBeInTheDocument()
    })

    it('is disabled when no models are selected', () => {
      render(<EvaluateArea {...defaultProps} />)
      
      const button = screen.getByText('Generate Evaluation Report')
      expect(button).toBeDisabled()
    })

    it('is enabled when models are selected and outputs are sufficient', () => {
      render(<EvaluateArea {...defaultProps} />)
      
      // Select models
      const selectButton = screen.getByTestId('select-models')
      fireEvent.click(selectButton)
      
      const button = screen.getByText('Generate Evaluation Report')
      expect(button).not.toBeDisabled()
    })

    it('is disabled when loading', () => {
      render(<EvaluateArea {...defaultProps} isLoading={true} />)
      
      const button = screen.getByText('Evaluating...')
      expect(button).toBeDisabled()
    })

    it('is disabled when component is disabled', () => {
      render(<EvaluateArea {...defaultProps} disabled={true} />)
      
      const button = screen.getByText('Generate Evaluation Report')
      expect(button).toBeDisabled()
    })
  })

  describe('Error Handling', () => {
    it('shows error when trying to evaluate with insufficient outputs', () => {
      render(<EvaluateArea {...defaultProps} validOutputsCount={1} />)
      
      // Component should show warning, not evaluation form
      expect(screen.getByText(/Need at least 2 valid outputs to perform evaluation/)).toBeInTheDocument()
    })

    it('button is disabled when no models are selected', () => {
      render(<EvaluateArea {...defaultProps} />)
      
      const button = screen.getByText('Generate Evaluation Report')
      expect(button).toBeDisabled()
      
      // No error should be shown since button is disabled
      expect(screen.queryByText('Please select at least one evaluator model.')).not.toBeInTheDocument()
    })

    it('clears error when models are selected after error state', () => {
      // This test simulates a scenario where error might be set programmatically
      // and then cleared when models are selected
      render(<EvaluateArea {...defaultProps} />)
      
      // Select models first to enable the button
      const selectButton = screen.getByTestId('select-models')
      fireEvent.click(selectButton)
      
      // Button should now be enabled
      const button = screen.getByText('Generate Evaluation Report')
      expect(button).not.toBeDisabled()
      
      // No error should be visible
      expect(screen.queryByText('Please select at least one evaluator model.')).not.toBeInTheDocument()
    })
  })

  describe('Evaluation Callback', () => {
    it('calls onEvaluate with correct parameters when using default prompt', () => {
      render(<EvaluateArea {...defaultProps} />)
      
      // Select models
      const selectButton = screen.getByTestId('select-models')
      fireEvent.click(selectButton)
      
      // Click evaluate
      const button = screen.getByText('Generate Evaluation Report')
      fireEvent.click(button)
      
      expect(defaultProps.onEvaluate).toHaveBeenCalledWith(['gpt-4', 'claude-3'], true, undefined)
    })

    it('calls onEvaluate with custom prompt when enabled', () => {
      render(<EvaluateArea {...defaultProps} />)
      
      // Select models
      const selectButton = screen.getByTestId('select-models')
      fireEvent.click(selectButton)
      
      // Enable custom prompt
      const customCheckbox = screen.getByLabelText('Use Custom Evaluation Prompt')
      fireEvent.click(customCheckbox)
      
      // Enter custom prompt
      const textarea = screen.getByLabelText('Custom Evaluation Instructions')
      fireEvent.change(textarea, { target: { value: 'My custom prompt' } })
      
      // Click evaluate
      const button = screen.getByText('Generate Evaluation Report')
      fireEvent.click(button)
      
      expect(defaultProps.onEvaluate).toHaveBeenCalledWith(['gpt-4', 'claude-3'], true, 'My custom prompt')
    })

    it('calls onEvaluate with blind IDs disabled when unchecked', () => {
      render(<EvaluateArea {...defaultProps} />)
      
      // Select models
      const selectButton = screen.getByTestId('select-models')
      fireEvent.click(selectButton)
      
      // Disable blind IDs
      const blindCheckbox = screen.getByLabelText('Use Blind IDs for this Evaluation')
      fireEvent.click(blindCheckbox)
      
      // Click evaluate
      const button = screen.getByText('Generate Evaluation Report')
      fireEvent.click(button)
      
      expect(defaultProps.onEvaluate).toHaveBeenCalledWith(['gpt-4', 'claude-3'], false, undefined)
    })
  })

  describe('Accessibility', () => {
    it('has proper labels for form elements', () => {
      render(<EvaluateArea {...defaultProps} />)
      
      expect(screen.getByLabelText('Use Blind IDs for this Evaluation')).toBeInTheDocument()
      expect(screen.getByLabelText('Use Custom Evaluation Prompt')).toBeInTheDocument()
    })

    it('has proper labels for custom prompt textarea', () => {
      render(<EvaluateArea {...defaultProps} />)
      
      const checkbox = screen.getByLabelText('Use Custom Evaluation Prompt')
      fireEvent.click(checkbox)
      
      expect(screen.getByLabelText('Custom Evaluation Instructions')).toBeInTheDocument()
    })

    it('has proper button text and states', () => {
      render(<EvaluateArea {...defaultProps} />)
      
      const button = screen.getByRole('button', { name: 'Generate Evaluation Report' })
      expect(button).toBeInTheDocument()
    })
  })

  describe('Theme Support', () => {
    it('applies correct theme classes to form elements', () => {
      render(<EvaluateArea {...defaultProps} />)
      
      const checkbox = screen.getByLabelText('Use Custom Evaluation Prompt')
      fireEvent.click(checkbox)
      
      const textarea = screen.getByLabelText('Custom Evaluation Instructions')
      expect(textarea).toHaveClass(
        'bg-light-component',
        'dark:bg-dark-component',
        'text-light-text',
        'dark:text-dark-text'
      )
    })
  })
}) 