import React from 'react'
import { render, screen } from '@testing-library/react'
import { describe, it, expect } from 'vitest'
import LoadingSpinner from '../../components/LoadingSpinner'

describe('LoadingSpinner', () => {
  describe('Basic Rendering', () => {
    it('renders with default message', () => {
      render(<LoadingSpinner />)
      
      expect(screen.getByText('Loading...')).toBeInTheDocument()
    })

    it('renders with custom message', () => {
      render(<LoadingSpinner message="Custom loading message" />)
      
      expect(screen.getByText('Custom loading message')).toBeInTheDocument()
    })

    it('renders without message when message is empty', () => {
      render(<LoadingSpinner message="" />)
      
      expect(screen.queryByText('Loading...')).not.toBeInTheDocument()
    })
  })

  describe('Size Variants', () => {
    it('renders with small size', () => {
      render(<LoadingSpinner size="small" />)
      
      const spinner = document.querySelector('div[class*="animate-spin"]')
      expect(spinner).toHaveClass('h-6', 'w-6')
    })

    it('renders with medium size (default)', () => {
      render(<LoadingSpinner />)
      
      const spinner = document.querySelector('div[class*="animate-spin"]')
      expect(spinner).toHaveClass('h-10', 'w-10')
    })

    it('renders with large size', () => {
      render(<LoadingSpinner size="large" />)
      
      const spinner = document.querySelector('div[class*="animate-spin"]')
      expect(spinner).toHaveClass('h-16', 'w-16')
    })
  })

  describe('Styling and Animation', () => {
    it('has proper spinning animation', () => {
      render(<LoadingSpinner />)
      
      const spinner = document.querySelector('div[class*="animate-spin"]')
      expect(spinner).toHaveClass('animate-spin')
    })

    it('applies correct theme colors', () => {
      render(<LoadingSpinner />)
      
      const spinner = document.querySelector('div[class*="animate-spin"]')
      expect(spinner).toHaveClass('border-t-light-accent', 'dark:border-t-dark-accent')
    })

    it('has proper container structure', () => {
      render(<LoadingSpinner />)
      
      const container = document.querySelector('div[class*="flex flex-col items-center justify-center space-y-6"]')
      expect(container).toHaveClass('flex', 'flex-col', 'items-center', 'justify-center', 'space-y-6')
    })
  })

  describe('Layout and Structure', () => {
    it('renders with proper container layout', () => {
      render(<LoadingSpinner message="Loading data..." />)
      
      const mainContainer = document.querySelector('div[class*="flex flex-col items-center justify-center space-y-6"]')
      expect(mainContainer).toHaveClass('flex', 'flex-col', 'items-center', 'justify-center', 'space-y-6')
    })

    it('includes animated dots when message is present', () => {
      render(<LoadingSpinner message="Loading..." />)
      
      const dots = document.querySelectorAll('div[class*="animate-bounce"]')
      expect(dots).toHaveLength(3)
    })
  })

  describe('Message Styling', () => {
    it('applies correct text styling to message', () => {
      render(<LoadingSpinner message="Custom message" />)
      
      const message = screen.getByText('Custom message')
      expect(message).toHaveClass('text-sm', 'font-medium', 'text-light-primary', 'dark:text-dark-primary')
    })

    it('positions message correctly relative to spinner', () => {
      render(<LoadingSpinner message="Loading data..." />)
      
      const messageContainer = screen.getByText('Loading data...').parentElement
      expect(messageContainer).toHaveClass('text-center', 'space-y-3')
    })
  })

  describe('Edge Cases', () => {
    it('handles very long messages', () => {
      const longMessage = 'This is a very long loading message that should still render properly without breaking the layout or causing any issues'
      render(<LoadingSpinner message={longMessage} />)
      
      expect(screen.getByText(longMessage)).toBeInTheDocument()
    })

    it('handles special characters in message', () => {
      const specialMessage = 'Loading... 50% (10/20) files processed! 🚀'
      render(<LoadingSpinner message={specialMessage} />)
      
      expect(screen.getByText(specialMessage)).toBeInTheDocument()
    })

    it('renders correctly with all props combined', () => {
      render(<LoadingSpinner message="Processing large dataset..." size="large" />)
      
      const spinner = document.querySelector('div[class*="animate-spin"]')
      expect(spinner).toHaveClass('h-16', 'w-16')
      expect(screen.getByText('Processing large dataset...')).toBeInTheDocument()
    })
  })

  describe('Accessibility', () => {
    it('provides meaningful text content', () => {
      render(<LoadingSpinner message="Loading user data" />)
      
      expect(screen.getByText('Loading user data')).toBeInTheDocument()
    })

    it('maintains proper semantic structure', () => {
      render(<LoadingSpinner />)
      
      const message = screen.getByText('Loading...')
      expect(message.tagName).toBe('P')
    })
  })

  describe('Theme Compatibility', () => {
    it('supports light theme colors for spinner', () => {
      render(<LoadingSpinner />)
      
      const spinner = document.querySelector('div[class*="animate-spin"]')
      expect(spinner).toHaveClass('border-t-light-accent')
    })

    it('supports dark theme colors for spinner', () => {
      render(<LoadingSpinner />)
      
      const spinner = document.querySelector('div[class*="animate-spin"]')
      expect(spinner).toHaveClass('dark:border-t-dark-accent')
    })

    it('supports theme colors for message text', () => {
      render(<LoadingSpinner message="Loading..." />)
      
      const message = screen.getByText('Loading...')
      expect(message).toHaveClass('text-light-primary', 'dark:text-dark-primary')
    })

    it('supports theme colors for animated dots', () => {
      render(<LoadingSpinner message="Loading..." />)
      
      const dots = document.querySelectorAll('div[class*="animate-bounce"]')
      expect(dots[0]).toHaveClass('bg-light-accent', 'dark:bg-dark-accent')
    })
  })
}) 