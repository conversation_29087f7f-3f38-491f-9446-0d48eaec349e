import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { vi, describe, it, beforeEach, expect } from 'vitest';
import EvaluationSidebar from '../../components/EvaluationSidebar';

// Mock the date-fns format function
vi.mock('date-fns', () => ({
  format: vi.fn((date, formatStr) => {
    if (formatStr === 'MMM d, yyyy') {
      return 'Jan 1, 2024';
    }
    return date.toString();
  })
}));

// Mock DeleteConfirmModal
vi.mock('../../components/DeleteConfirmModal', () => ({
  default: ({ isOpen, onClose, onConfirm, title, message }: any) => (
    isOpen ? (
      <div data-testid="delete-modal">
        <h3>{title}</h3>
        <p>{message}</p>
        <button onClick={onConfirm}>Confirm</button>
        <button onClick={onClose}>Cancel</button>
      </div>
    ) : null
  )
}));

// Mock LoadingSpinner
vi.mock('../../components/LoadingSpinner', () => ({
  default: ({ message }: { message: string }) => (
    <div data-testid="loading-spinner">{message}</div>
  )
}));

// Mock statusUtils
vi.mock('../../utils/statusUtils', () => ({
  getSidebarStatusColor: vi.fn((status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-700';
      case 'running':
        return 'bg-yellow-100 text-yellow-700';
      case 'failed':
        return 'bg-red-100 text-red-700';
      default:
        return 'bg-gray-100 text-gray-700';
    }
  })
}));

const mockEvaluationItems = [
  {
    id: '1',
    name: 'Test Evaluation 1',
    description: 'Test description 1',
    status: 'completed',
    created_at: '2024-01-01T00:00:00Z',
    evaluation_type: '0-1'
  },
  {
    id: '2',
    name: 'Test Evaluation 2',
    description: 'Test description 2',
    status: 'running',
    created_at: '2024-01-02T00:00:00Z',
    evaluation_type: '90-100'
  },
  {
    id: '3',
    name: 'Test Evaluation 3',
    description: 'Test description 3',
    status: 'failed',
    created_at: '2024-01-03T00:00:00Z',
    evaluation_type: '0-1'
  }
];

const defaultConfig = {
  searchPlaceholder: 'Search evaluations...',
  newButtonTitle: 'New Evaluation',
  allButtonTitle: 'All Evaluations',
  emptyMessage: 'No evaluations yet',
  getItemTitle: (item: any) => item.name,
  getItemSubtitle: (item: any) => item.description,
  showTypeIcon: true,
  getTypeIcon: (item: any) => <span data-testid="type-icon">{item.evaluation_type}</span>
};

const defaultProps = {
  items: mockEvaluationItems,
  isLoading: false,
  error: null,
  onSelectItem: vi.fn(),
  onDeleteItem: vi.fn(),
  currentItemId: null,
  onNewEvaluation: vi.fn(),
  onAllEvaluations: vi.fn(),
  config: defaultConfig
};

describe('EvaluationSidebar', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Rendering', () => {
    it('renders the sidebar with evaluation items', () => {
      render(<EvaluationSidebar {...defaultProps} />);
      
      expect(screen.getByText('History')).toBeInTheDocument();
      expect(screen.getByText('Test Evaluation 1')).toBeInTheDocument();
      expect(screen.getByText('Test Evaluation 2')).toBeInTheDocument();
      expect(screen.getByText('Test Evaluation 3')).toBeInTheDocument();
    });

    it('renders loading state', () => {
      render(<EvaluationSidebar {...defaultProps} isLoading={true} />);
      
      expect(screen.getByTestId('loading-spinner')).toBeInTheDocument();
      expect(screen.getByText('Loading evaluations...')).toBeInTheDocument();
    });

    it('renders error state', () => {
      const errorMessage = 'Failed to load evaluations';
      render(<EvaluationSidebar {...defaultProps} error={errorMessage} />);
      
      expect(screen.getByText(errorMessage)).toBeInTheDocument();
    });

    it('renders empty state when no items', () => {
      render(<EvaluationSidebar {...defaultProps} items={[]} />);
      
      expect(screen.getByText('No evaluations yet')).toBeInTheDocument();
    });

    it('highlights the current item', () => {
      render(<EvaluationSidebar {...defaultProps} currentItemId="2" />);
      
      const currentItem = screen.getByText('Test Evaluation 2').closest('[role="button"]');
      expect(currentItem).toHaveClass('bg-light-accent/10');
    });
  });

  describe('Search Functionality', () => {
    it('shows search input when search button is clicked', () => {
      render(<EvaluationSidebar {...defaultProps} />);
      
      const searchButton = screen.getByTitle('Search history');
      fireEvent.click(searchButton);
      
      expect(screen.getByPlaceholderText('Search evaluations...')).toBeInTheDocument();
    });

    it('filters items based on search term', async () => {
      render(<EvaluationSidebar {...defaultProps} />);
      
      // Open search
      const searchButton = screen.getByTitle('Search history');
      fireEvent.click(searchButton);
      
      // Search for specific evaluation
      const searchInput = screen.getByPlaceholderText('Search evaluations...');
      fireEvent.change(searchInput, { target: { value: 'Test Evaluation 1' } });
      
      await waitFor(() => {
        expect(screen.getByText('Test Evaluation 1')).toBeInTheDocument();
        expect(screen.queryByText('Test Evaluation 2')).not.toBeInTheDocument();
        expect(screen.queryByText('Test Evaluation 3')).not.toBeInTheDocument();
      });
    });

    it('shows no results message when search yields no matches', async () => {
      render(<EvaluationSidebar {...defaultProps} />);
      
      // Open search
      const searchButton = screen.getByTitle('Search history');
      fireEvent.click(searchButton);
      
      // Search for non-existent evaluation
      const searchInput = screen.getByPlaceholderText('Search evaluations...');
      fireEvent.change(searchInput, { target: { value: 'Non-existent' } });
      
      await waitFor(() => {
        expect(screen.getByText('No results for "Non-existent"')).toBeInTheDocument();
      });
    });
  });

  describe('Navigation Actions', () => {
    it('calls onNewEvaluation when new button is clicked', () => {
      render(<EvaluationSidebar {...defaultProps} />);
      
      const newButton = screen.getByTitle('New Evaluation');
      fireEvent.click(newButton);
      
      expect(defaultProps.onNewEvaluation).toHaveBeenCalledTimes(1);
    });

    it('calls onAllEvaluations when all button is clicked', () => {
      render(<EvaluationSidebar {...defaultProps} />);
      
      const allButton = screen.getByTitle('All Evaluations');
      fireEvent.click(allButton);
      
      expect(defaultProps.onAllEvaluations).toHaveBeenCalledTimes(1);
    });

    it('calls onSelectItem when evaluation item is clicked', () => {
      render(<EvaluationSidebar {...defaultProps} />);
      
      const evaluationItem = screen.getByText('Test Evaluation 1');
      fireEvent.click(evaluationItem);
      
      expect(defaultProps.onSelectItem).toHaveBeenCalledWith('1');
    });

    it('supports keyboard navigation for evaluation items', () => {
      render(<EvaluationSidebar {...defaultProps} />);
      
      const evaluationItem = screen.getByText('Test Evaluation 1').closest('[role="button"]');
      fireEvent.keyDown(evaluationItem!, { key: 'Enter' });
      
      expect(defaultProps.onSelectItem).toHaveBeenCalledWith('1');
    });
  });

  describe('Delete Functionality', () => {
    it('shows delete modal when delete button is clicked', () => {
      render(<EvaluationSidebar {...defaultProps} />);
      
      const deleteButtons = screen.getAllByTitle('Delete');
      fireEvent.click(deleteButtons[0]);
      
      expect(screen.getByTestId('delete-modal')).toBeInTheDocument();
      expect(screen.getByText('Delete Evaluation')).toBeInTheDocument();
    });

    it('calls onDeleteItem when delete is confirmed', async () => {
      render(<EvaluationSidebar {...defaultProps} />);
      
      // Click delete button
      const deleteButtons = screen.getAllByTitle('Delete');
      fireEvent.click(deleteButtons[0]);
      
      // Confirm deletion
      const confirmButton = screen.getByText('Confirm');
      fireEvent.click(confirmButton);
      
      await waitFor(() => {
        expect(defaultProps.onDeleteItem).toHaveBeenCalledWith('1');
      });
    });

    it('closes delete modal when cancel is clicked', () => {
      render(<EvaluationSidebar {...defaultProps} />);
      
      // Click delete button
      const deleteButtons = screen.getAllByTitle('Delete');
      fireEvent.click(deleteButtons[0]);
      
      // Cancel deletion
      const cancelButton = screen.getByText('Cancel');
      fireEvent.click(cancelButton);
      
      expect(screen.queryByTestId('delete-modal')).not.toBeInTheDocument();
    });

    it('does not show delete buttons when onDeleteItem is not provided', () => {
      const propsWithoutDelete = { ...defaultProps, onDeleteItem: undefined };
      render(<EvaluationSidebar {...propsWithoutDelete} />);
      
      expect(screen.queryByTitle('Delete')).not.toBeInTheDocument();
    });
  });

  describe('Status Display', () => {
    it('displays status badges for each evaluation', () => {
      render(<EvaluationSidebar {...defaultProps} />);
      
      expect(screen.getByText('completed')).toBeInTheDocument();
      expect(screen.getByText('running')).toBeInTheDocument();
      expect(screen.getByText('failed')).toBeInTheDocument();
    });

    it('applies correct status colors', () => {
      render(<EvaluationSidebar {...defaultProps} />);
      
      const completedStatus = screen.getByText('completed');
      const runningStatus = screen.getByText('running');
      const failedStatus = screen.getByText('failed');
      
      expect(completedStatus).toHaveClass('bg-green-100', 'text-green-700');
      expect(runningStatus).toHaveClass('bg-yellow-100', 'text-yellow-700');
      expect(failedStatus).toHaveClass('bg-red-100', 'text-red-700');
    });
  });

  describe('Date Formatting', () => {
    it('formats dates correctly', () => {
      render(<EvaluationSidebar {...defaultProps} />);
      
      // All dates should be formatted as "Jan 1, 2024" due to our mock
      const dateElements = screen.getAllByText('Jan 1, 2024');
      expect(dateElements).toHaveLength(3);
    });
  });

  describe('Configuration Options', () => {
    it('uses custom search placeholder', () => {
      const customConfig = {
        ...defaultConfig,
        searchPlaceholder: 'Custom search placeholder'
      };
      render(<EvaluationSidebar {...defaultProps} config={customConfig} />);
      
      const searchButton = screen.getByTitle('Search history');
      fireEvent.click(searchButton);
      
      expect(screen.getByPlaceholderText('Custom search placeholder')).toBeInTheDocument();
    });

    it('uses custom empty message', () => {
      const customConfig = {
        ...defaultConfig,
        emptyMessage: 'Custom empty message'
      };
      render(<EvaluationSidebar {...defaultProps} items={[]} config={customConfig} />);
      
      expect(screen.getByText('Custom empty message')).toBeInTheDocument();
    });

    it('uses custom item title getter', () => {
      const customConfig = {
        ...defaultConfig,
        getItemTitle: (item: any) => `Custom: ${item.name}`
      };
      render(<EvaluationSidebar {...defaultProps} config={customConfig} />);
      
      expect(screen.getByText('Custom: Test Evaluation 1')).toBeInTheDocument();
    });
  });

  describe('Accessibility', () => {
    it('has proper ARIA labels and roles', () => {
      render(<EvaluationSidebar {...defaultProps} />);
      
      const evaluationItems = screen.getAllByRole('button');
      expect(evaluationItems.length).toBeGreaterThan(0);
      
      // Check that evaluation items have proper role and tabindex
      const evaluationButtons = evaluationItems.filter(item => 
        item.textContent?.includes('Test Evaluation')
      );
      
      evaluationButtons.forEach(item => {
        expect(item).toHaveAttribute('tabIndex', '0');
      });
    });

    it('supports keyboard navigation', () => {
      render(<EvaluationSidebar {...defaultProps} />);
      
      const firstItem = screen.getByText('Test Evaluation 1').closest('[role="button"]');
      fireEvent.keyDown(firstItem!, { key: ' ' });
      
      expect(defaultProps.onSelectItem).toHaveBeenCalledWith('1');
    });
  });

  describe('Desktop Sidebar Collapse', () => {
    it('calls onToggleDesktopCollapse when collapse button is clicked', () => {
      const onToggleDesktopCollapse = vi.fn();
      render(
        <EvaluationSidebar 
          {...defaultProps} 
          onToggleDesktopCollapse={onToggleDesktopCollapse}
        />
      );
      
      const collapseButton = screen.getByTitle('Close sidebar');
      fireEvent.click(collapseButton);
      
      expect(onToggleDesktopCollapse).toHaveBeenCalledTimes(1);
    });

    it('does not show collapse button when onToggleDesktopCollapse is not provided', () => {
      render(<EvaluationSidebar {...defaultProps} />);
      
      expect(screen.queryByTitle('Close sidebar')).not.toBeInTheDocument();
    });
  });
}); 