import React from 'react'
import { render, screen, fireEvent } from '@testing-library/react'
import { describe, it, expect, vi, beforeEach } from 'vitest'
import ReportViewer from '../../components/ReportViewer'
import { EvaluationReportResponse, Ranking } from '../../api/apiClient'

// Mock UsageStatsCard component
vi.mock('../../components/UsageStatsCard', () => ({
  default: ({ title, modelName, showUnavailableMessage, unavailableMessage, iconType }: any) => (
    <div data-testid="usage-stats-card">
      <div>Title: {title}</div>
      <div>Model: {modelName}</div>
      <div>Icon Type: {iconType}</div>
      {showUnavailableMessage && (
        <div>Unavailable: {unavailableMessage}</div>
      )}
    </div>
  )
}))

describe('ReportViewer', () => {
  const mockRanking: Ranking = {
    id: 1,
    evaluation_id: 123,
    evaluator_model_id: 'openai/gpt-4',
    ranked_list_json: [1, 2, 3],
    reasoning_text: 'This is the evaluation reasoning text explaining the ranking.',
    error_message: null,
    created_at: '2024-01-01T00:00:00Z',
    prompt_tokens: 100,
    completion_tokens: 50,
    total_tokens: 150,
    cost_credits: 0.001,
    generation_id: 'gen-123'
  }

  const mockReport: EvaluationReportResponse = {
    evaluation_id: 123,
    task_id: 456,
    status: 'completed',
    rankings: [mockRanking]
  }

  const defaultProps = {
    report: mockReport,
    getDisplayId: vi.fn((id: number) => `Model-${id}`),
    evaluationUsedBlindIds: false,
    blindIdToModelNameMap: {},
    revealGlobalReasoning: false,
    onToggleRevealGlobalReasoning: vi.fn()
  }

  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('Empty State', () => {
    it('shows empty state when no report', () => {
      render(<ReportViewer {...defaultProps} report={null} />)
      
      expect(screen.getByText('No evaluation results available yet')).toBeInTheDocument()
      const svgIcon = document.querySelector('svg')
      expect(svgIcon).toBeInTheDocument()
      expect(svgIcon).toHaveClass('w-12', 'h-12', 'mx-auto', 'text-light-secondary', 'dark:text-dark-secondary', 'mb-4')
    })

    it('shows empty state when report has no rankings', () => {
      const emptyReport = { ...mockReport, rankings: [] }
      render(<ReportViewer {...defaultProps} report={emptyReport} />)
      
      expect(screen.getByText('No evaluation results available yet')).toBeInTheDocument()
    })

    it('applies correct styling to empty state', () => {
      render(<ReportViewer {...defaultProps} report={null} />)
      
      const emptyContainer = screen.getByText('No evaluation results available yet').closest('div')
      expect(emptyContainer).toHaveClass(
        'text-center',
        'py-12',
        'bg-light-background/50',
        'dark:bg-dark-background/50',
        'rounded-xl',
        'border-2',
        'border-dashed',
        'border-light-border',
        'dark:border-dark-border'
      )
    })
  })

  describe('Custom Evaluation Prompt', () => {
    it('shows custom evaluation prompt when provided', () => {
      const customPrompt = 'This is a custom evaluation prompt for testing.'
      render(<ReportViewer {...defaultProps} evaluationPrompt={customPrompt} />)
      
      expect(screen.getByText('Custom Evaluation Prompt Used')).toBeInTheDocument()
      expect(screen.getByText(customPrompt)).toBeInTheDocument()
    })

    it('does not show custom prompt section when not provided', () => {
      render(<ReportViewer {...defaultProps} />)
      
      expect(screen.queryByText('Custom Evaluation Prompt Used')).not.toBeInTheDocument()
    })

    it('applies correct styling to custom prompt section', () => {
      const customPrompt = 'Custom prompt text'
      render(<ReportViewer {...defaultProps} evaluationPrompt={customPrompt} />)
      
      const promptContainer = screen.getByText(customPrompt).closest('div')
      expect(promptContainer).toHaveClass(
        'text-sm',
        'text-light-primary',
        'dark:text-dark-primary',
        'bg-light-background/30',
        'dark:bg-dark-background/30',
        'p-3',
        'rounded-md'
      )
    })
  })

  describe('Error Handling', () => {
    it('displays error message when ranking has error', () => {
      const errorRanking = { ...mockRanking, error_message: 'Evaluation failed due to API error' }
      const errorReport = { ...mockReport, rankings: [errorRanking] }
      
      render(<ReportViewer {...defaultProps} report={errorReport} />)
      
      expect(screen.getByText('Error during evaluation:')).toBeInTheDocument()
      expect(screen.getByText('Evaluation failed due to API error')).toBeInTheDocument()
    })

    it('applies correct styling to error message', () => {
      const errorRanking = { ...mockRanking, error_message: 'Test error' }
      const errorReport = { ...mockReport, rankings: [errorRanking] }
      
      render(<ReportViewer {...defaultProps} report={errorReport} />)
      
      const errorContainer = screen.getByText('Error during evaluation:').closest('div')?.parentElement
      expect(errorContainer).toHaveClass(
        'flex',
        'items-start',
        'space-x-3',
        'p-4',
        'text-red-800',
        'dark:text-red-200',
        'bg-gradient-to-r',
        'from-red-50',
        'to-red-100',
        'dark:from-red-900/20',
        'dark:to-red-800/20',
        'rounded-lg',
        'border',
        'border-red-200',
        'dark:border-red-700/50'
      )
    })
  })

  describe('Rankings Display', () => {
    it('displays rankings in correct order', () => {
      render(<ReportViewer {...defaultProps} />)
      
      expect(screen.getByText('Rankings (Best to Worst)')).toBeInTheDocument()
      
      // Check that getDisplayId is called for each ranking
      expect(defaultProps.getDisplayId).toHaveBeenCalledWith(1)
      expect(defaultProps.getDisplayId).toHaveBeenCalledWith(2)
      expect(defaultProps.getDisplayId).toHaveBeenCalledWith(3)
      
      // Check that display IDs are shown
      expect(screen.getByText('Model-1')).toBeInTheDocument()
      expect(screen.getByText('Model-2')).toBeInTheDocument()
      expect(screen.getByText('Model-3')).toBeInTheDocument()
    })

    it('applies correct rank badges', () => {
      render(<ReportViewer {...defaultProps} />)
      
      // Find rank badges by their numbers
      const firstPlace = screen.getByText('1')
      const secondPlace = screen.getByText('2')
      const thirdPlace = screen.getByText('3')
      
      // Check gold, silver, bronze styling
      expect(firstPlace).toHaveClass('bg-yellow-500', 'text-white')
      expect(secondPlace).toHaveClass('bg-gray-400', 'text-white')
      expect(thirdPlace).toHaveClass('bg-orange-500', 'text-white')
    })

    it('handles rankings with more than 3 items', () => {
      const longRanking = { ...mockRanking, ranked_list_json: [1, 2, 3, 4, 5] }
      const longReport = { ...mockReport, rankings: [longRanking] }
      
      render(<ReportViewer {...defaultProps} report={longReport} />)
      
      const fourthPlace = screen.getByText('4')
      const fifthPlace = screen.getByText('5')
      
      // Check default styling for ranks beyond 3
      expect(fourthPlace).toHaveClass(
        'bg-light-component-subtle',
        'dark:bg-dark-component-subtle',
        'text-light-secondary',
        'dark:text-dark-secondary'
      )
      expect(fifthPlace).toHaveClass(
        'bg-light-component-subtle',
        'dark:bg-dark-component-subtle',
        'text-light-secondary',
        'dark:text-dark-secondary'
      )
    })
  })

  describe('Reasoning Display', () => {
    it('displays reasoning text', () => {
      render(<ReportViewer {...defaultProps} />)
      
      expect(screen.getByText('Reasoning')).toBeInTheDocument()
      expect(screen.getByText('This is the evaluation reasoning text explaining the ranking.')).toBeInTheDocument()
    })

    it('handles null reasoning text', () => {
      const noReasoningRanking = { ...mockRanking, reasoning_text: null }
      const noReasoningReport = { ...mockReport, rankings: [noReasoningRanking] }
      
      render(<ReportViewer {...defaultProps} report={noReasoningReport} />)
      
      expect(screen.getByText('Reasoning')).toBeInTheDocument()
      // Should not crash and should render the section
    })

    it('applies correct styling to reasoning text', () => {
      render(<ReportViewer {...defaultProps} />)
      
      const reasoningText = screen.getByText('This is the evaluation reasoning text explaining the ranking.')
      expect(reasoningText).toHaveClass(
        'whitespace-pre-wrap',
        'text-light-primary',
        'dark:text-dark-primary',
        'leading-relaxed'
      )
    })
  })

  describe('Blind IDs and Model Name Revelation', () => {
    const blindIdProps = {
      ...defaultProps,
      evaluationUsedBlindIds: true,
      blindIdToModelNameMap: {
        'blind-123': 'GPT-4',
        'blind-456': 'Claude-3'
      }
    }

    it('shows reveal checkbox when evaluation used blind IDs', () => {
      render(<ReportViewer {...blindIdProps} />)
      
      expect(screen.getByText('Reveal actual model names')).toBeInTheDocument()
      expect(screen.getByRole('checkbox')).toBeInTheDocument()
    })

    it('does not show reveal checkbox when evaluation did not use blind IDs', () => {
      render(<ReportViewer {...defaultProps} />)
      
      expect(screen.queryByText('Reveal actual model names')).not.toBeInTheDocument()
      expect(screen.queryByRole('checkbox')).not.toBeInTheDocument()
    })

    it('calls toggle function when checkbox is clicked', () => {
      render(<ReportViewer {...blindIdProps} />)
      
      const checkbox = screen.getByRole('checkbox')
      fireEvent.click(checkbox)
      
      expect(defaultProps.onToggleRevealGlobalReasoning).toHaveBeenCalledTimes(1)
    })

    it('reflects checkbox state correctly', () => {
      const { rerender } = render(<ReportViewer {...blindIdProps} />)
      
      let checkbox = screen.getByRole('checkbox')
      expect(checkbox).not.toBeChecked()
      
      rerender(<ReportViewer {...blindIdProps} revealGlobalReasoning={true} />)
      
      checkbox = screen.getByRole('checkbox')
      expect(checkbox).toBeChecked()
    })

    it('replaces blind IDs with model names when revealed', () => {
      const reasoningWithBlindIds = 'Model blind-123 performed better than blind-456 in this task.'
      const blindRanking = { ...mockRanking, reasoning_text: reasoningWithBlindIds }
      const blindReport = { ...mockReport, rankings: [blindRanking] }
      
      render(
        <ReportViewer 
          {...blindIdProps} 
          report={blindReport}
          revealGlobalReasoning={true}
        />
      )
      
      // Should show model names instead of blind IDs
      expect(screen.getByText('GPT-4')).toBeInTheDocument()
      expect(screen.getByText('Claude-3')).toBeInTheDocument()
    })
  })

  describe('Usage Statistics', () => {
    it('displays usage stats card for ranking with generation ID', () => {
      render(<ReportViewer {...defaultProps} />)
      
      const usageStatsCard = screen.getByTestId('usage-stats-card')
      expect(usageStatsCard).toBeInTheDocument()
      expect(usageStatsCard).toHaveTextContent('Title: Evaluation Usage Statistics')
      expect(usageStatsCard).toHaveTextContent('Model: gpt-4')
      expect(usageStatsCard).toHaveTextContent('Icon Type: evaluation')
    })

    it('displays unavailable message for ranking without generation ID', () => {
      const noGenIdRanking = { ...mockRanking, generation_id: null }
      const noGenIdReport = { ...mockReport, rankings: [noGenIdRanking] }
      
      render(<ReportViewer {...defaultProps} report={noGenIdReport} />)
      
      const usageStatsCard = screen.getByTestId('usage-stats-card')
      expect(usageStatsCard).toHaveTextContent('Unavailable: Usage statistics are not available for this evaluation')
    })
  })

  describe('Multiple Rankings', () => {
    it('handles multiple rankings correctly', () => {
      const secondRanking: Ranking = {
        ...mockRanking,
        id: 2,
        evaluator_model_id: 'anthropic/claude-3',
        reasoning_text: 'Second evaluator reasoning'
      }
      const multiReport = { ...mockReport, rankings: [mockRanking, secondRanking] }
      
      render(<ReportViewer {...defaultProps} report={multiReport} />)
      
      // Should show both reasoning texts
      expect(screen.getByText('This is the evaluation reasoning text explaining the ranking.')).toBeInTheDocument()
      expect(screen.getByText('Second evaluator reasoning')).toBeInTheDocument()
      
      // Should show both usage stats cards
      const usageStatsCards = screen.getAllByTestId('usage-stats-card')
      expect(usageStatsCards).toHaveLength(2)
    })
  })

  describe('Props Handling', () => {
    it('handles getDisplayId function correctly', () => {
      const customGetDisplayId = vi.fn((id: number) => `Custom-${id}`)
      render(<ReportViewer {...defaultProps} getDisplayId={customGetDisplayId} />)
      
      expect(customGetDisplayId).toHaveBeenCalledWith(1)
      expect(customGetDisplayId).toHaveBeenCalledWith(2)
      expect(customGetDisplayId).toHaveBeenCalledWith(3)
      
      expect(screen.getByText('Custom-1')).toBeInTheDocument()
      expect(screen.getByText('Custom-2')).toBeInTheDocument()
      expect(screen.getByText('Custom-3')).toBeInTheDocument()
    })

    it('handles empty blindIdToModelNameMap', () => {
      const emptyMapProps = { ...defaultProps, evaluationUsedBlindIds: true, blindIdToModelNameMap: {} }
      render(<ReportViewer {...emptyMapProps} />)
      
      // Should not crash and should render normally
      expect(screen.getByText('Reasoning')).toBeInTheDocument()
    })
  })

  describe('Accessibility', () => {
    it('provides proper labels for checkbox', () => {
      const blindIdProps = {
        ...defaultProps,
        evaluationUsedBlindIds: true,
        blindIdToModelNameMap: { 'blind-123': 'GPT-4' }
      }
      
      render(<ReportViewer {...blindIdProps} />)
      
      const checkbox = screen.getByRole('checkbox')
      expect(checkbox).toHaveAttribute('id', `revealModelNames-${mockRanking.id}`)
      
      const label = screen.getByLabelText('Reveal actual model names')
      expect(label).toBeInTheDocument()
    })

    it('maintains proper heading structure', () => {
      render(<ReportViewer {...defaultProps} />)
      
      // Check for section headings
      expect(screen.getByText('Rankings (Best to Worst)')).toBeInTheDocument()
      expect(screen.getByText('Reasoning')).toBeInTheDocument()
    })
  })

  describe('Theme Support', () => {
    it('applies light and dark theme classes', () => {
      render(<ReportViewer {...defaultProps} />)
      
      // Check for theme classes in various elements
      const reasoningText = screen.getByText('This is the evaluation reasoning text explaining the ranking.')
      expect(reasoningText).toHaveClass('text-light-primary', 'dark:text-dark-primary')
    })

    it('applies theme classes to empty state', () => {
      render(<ReportViewer {...defaultProps} report={null} />)
      
      const emptyContainer = screen.getByText('No evaluation results available yet').closest('div')
      expect(emptyContainer).toHaveClass(
        'bg-light-background/50',
        'dark:bg-dark-background/50',
        'border-light-border',
        'dark:border-dark-border'
      )
    })
  })

  describe('Edge Cases', () => {
    it('handles empty ranked_list_json', () => {
      const emptyRanking = { ...mockRanking, ranked_list_json: [] }
      const emptyReport = { ...mockReport, rankings: [emptyRanking] }
      
      render(<ReportViewer {...defaultProps} report={emptyReport} />)
      
      // Should show reasoning section but no rankings
      expect(screen.getByText('Rankings (Best to Worst)')).toBeInTheDocument()
      expect(screen.getByText('Reasoning')).toBeInTheDocument()
    })

    it('handles special characters in reasoning text', () => {
      const specialReasoning = 'Reasoning with special chars: <>&"\'🚀'
      const specialRanking = { ...mockRanking, reasoning_text: specialReasoning }
      const specialReport = { ...mockReport, rankings: [specialRanking] }
      
      render(<ReportViewer {...defaultProps} report={specialReport} />)
      
      expect(screen.getByText(specialReasoning)).toBeInTheDocument()
    })

    it('handles very long reasoning text', () => {
      const longReasoning = 'This is a very long reasoning text that should be displayed properly without breaking the layout. '.repeat(10)
      const longRanking = { ...mockRanking, reasoning_text: longReasoning }
      const longReport = { ...mockReport, rankings: [longRanking] }
      
      render(<ReportViewer {...defaultProps} report={longReport} />)
      
      expect(screen.getByText((content, element) => {
        return element?.tagName.toLowerCase() === 'pre' && 
               content.includes('This is a very long reasoning text that should be displayed properly without breaking the layout.')
      })).toBeInTheDocument()
    })
  })
}) 