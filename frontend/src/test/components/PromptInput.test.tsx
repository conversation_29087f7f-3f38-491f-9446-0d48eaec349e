import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { describe, it, expect, vi, beforeEach } from 'vitest'
import PromptInput from '../../components/PromptInput'

describe('PromptInput', () => {
  const mockModels = [
    'openai/gpt-4',
    'openai/gpt-3.5-turbo',
    'anthropic/claude-3-sonnet',
    'google/gemini-pro'
  ]

  const mockOnSubmit = vi.fn()

  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('Editable Mode (Default)', () => {
    it('renders all form elements', () => {
      render(
        <PromptInput
          onSubmit={mockOnSubmit}
          isLoading={false}
          availableModels={mockModels}
        />
      )

      // System prompt section (collapsed by default)
      expect(screen.getByText('System Prompt (Optional)')).toBeInTheDocument()
      
      // User prompt
      expect(screen.getByText('User Prompt')).toBeInTheDocument()
      expect(screen.getByPlaceholderText('Enter your prompt here...')).toBeInTheDocument()
      
      // Model selection
      expect(screen.getByText('Select Generator Models')).toBeInTheDocument()
      
      // Submit button
      expect(screen.getByRole('button', { name: /generate/i })).toBeInTheDocument()
    })

    it('expands system prompt section when clicked', async () => {
      render(
        <PromptInput
          onSubmit={mockOnSubmit}
          isLoading={false}
          availableModels={mockModels}
        />
      )

      const systemPromptToggle = screen.getByText('System Prompt (Optional)')
      fireEvent.click(systemPromptToggle)

      await waitFor(() => {
        expect(screen.getByPlaceholderText(/Enter system prompt here/)).toBeInTheDocument()
      })
    })

    it('renders all available models as checkboxes', () => {
      render(
        <PromptInput
          onSubmit={mockOnSubmit}
          isLoading={false}
          availableModels={mockModels}
        />
      )

      // Check provider names - use getAllByText since "openai" appears twice
      const openaiElements = screen.getAllByText('openai')
      expect(openaiElements).toHaveLength(2) // gpt-4 and gpt-3.5-turbo
      expect(screen.getByText('anthropic')).toBeInTheDocument()
      expect(screen.getByText('google')).toBeInTheDocument()

      // Check model names
      expect(screen.getByText('gpt-4')).toBeInTheDocument()
      expect(screen.getByText('gpt-3.5-turbo')).toBeInTheDocument()
      expect(screen.getByText('claude-3-sonnet')).toBeInTheDocument()
      expect(screen.getByText('gemini-pro')).toBeInTheDocument()
    })

    it('shows validation error when no models selected', () => {
      render(
        <PromptInput
          onSubmit={mockOnSubmit}
          isLoading={false}
          availableModels={mockModels}
        />
      )

      expect(screen.getByText('Please select at least one model')).toBeInTheDocument()
    })
  })

  describe('Form Submission', () => {
    it('submits form with prompt and selected models', async () => {
      render(
        <PromptInput
          onSubmit={mockOnSubmit}
          isLoading={false}
          availableModels={mockModels}
        />
      )

      // Fill in prompt
      const promptTextarea = screen.getByPlaceholderText('Enter your prompt here...')
      fireEvent.change(promptTextarea, { target: { value: 'Test prompt' } })

      // Select a model
      const gpt4Checkbox = screen.getByRole('checkbox', { name: /gpt-4/ })
      fireEvent.click(gpt4Checkbox)

      // Submit form
      const submitButton = screen.getByRole('button', { name: /generate/i })
      fireEvent.click(submitButton)

      await waitFor(() => {
        expect(mockOnSubmit).toHaveBeenCalledWith('Test prompt', ['openai/gpt-4'], '')
      })
    })

    it('does not submit when prompt is empty', async () => {
      render(
        <PromptInput
          onSubmit={mockOnSubmit}
          isLoading={false}
          availableModels={mockModels}
        />
      )

      // Select a model but leave prompt empty
      const gpt4Checkbox = screen.getByRole('checkbox', { name: /gpt-4/ })
      fireEvent.click(gpt4Checkbox)

      // Try to submit form
      const submitButton = screen.getByRole('button', { name: /generate/i })
      fireEvent.click(submitButton)

      // Should not call onSubmit
      expect(mockOnSubmit).not.toHaveBeenCalled()
    })
  })

  describe('Read-Only Mode', () => {
    it('renders in read-only mode when isReadOnly is true', () => {
      render(
        <PromptInput
          onSubmit={mockOnSubmit}
          isLoading={false}
          availableModels={mockModels}
          isReadOnly={true}
          initialPrompt="Read-only prompt"
          initialSystemPrompt="Read-only system prompt"
          initialSelectedModels={['openai/gpt-4']}
        />
      )

      // Should show PromptDisplay components instead of form elements
      expect(screen.getByText('System Prompt')).toBeInTheDocument()
      expect(screen.getByText('User Prompt')).toBeInTheDocument()
      expect(screen.getByText('Models Used')).toBeInTheDocument()

      // Should not show form elements
      expect(screen.queryByPlaceholderText('Enter your prompt here...')).not.toBeInTheDocument()
      expect(screen.queryByRole('button', { name: /generate/i })).not.toBeInTheDocument()
      expect(screen.queryByRole('checkbox')).not.toBeInTheDocument()
    })

    it('displays selected models as badges in read-only mode', () => {
      render(
        <PromptInput
          onSubmit={mockOnSubmit}
          isLoading={false}
          availableModels={mockModels}
          isReadOnly={true}
          initialSelectedModels={['openai/gpt-4', 'anthropic/claude-3-sonnet']}
        />
      )

      // Should show model names as badges
      expect(screen.getByText('gpt-4')).toBeInTheDocument()
      expect(screen.getByText('claude-3-sonnet')).toBeInTheDocument()
    })
  })

  describe('Loading State', () => {
    it('disables form elements when loading', () => {
      render(
        <PromptInput
          onSubmit={mockOnSubmit}
          isLoading={true}
          availableModels={mockModels}
        />
      )

      // Text areas should be disabled
      const promptTextarea = screen.getByPlaceholderText('Enter your prompt here...')
      expect(promptTextarea).toBeDisabled()

      // Submit button should be disabled and show "Generating..." text
      const submitButton = screen.getByRole('button', { name: /generating/i })
      expect(submitButton).toBeDisabled()
    })
  })
}) 