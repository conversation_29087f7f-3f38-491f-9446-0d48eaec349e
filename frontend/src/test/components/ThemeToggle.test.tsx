import React from 'react'
import { render, screen, fireEvent } from '@testing-library/react'
import { describe, it, expect, vi, beforeEach } from 'vitest'
import ThemeToggle from '../../components/ThemeToggle'

// Mock the theme context
const mockThemeContext = {
  resolvedTheme: 'light' as 'light' | 'dark',
  setTheme: vi.fn()
}

vi.mock('../../context/ThemeContext', () => ({
  useTheme: () => mockThemeContext
}))

describe('ThemeToggle', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    mockThemeContext.resolvedTheme = 'light'
  })

  describe('Basic Rendering', () => {
    it('renders toggle button', () => {
      render(<ThemeToggle />)
      
      const button = screen.getByRole('button')
      expect(button).toBeInTheDocument()
    })

    it('has proper accessibility attributes', () => {
      render(<ThemeToggle />)
      
      const button = screen.getByRole('button')
      expect(button).toHaveAttribute('title')
    })
  })

  describe('Theme Toggle Functionality', () => {
    it('calls setTheme when clicked', () => {
      render(<ThemeToggle />)
      
      const button = screen.getByRole('button')
      fireEvent.click(button)
      
      expect(mockThemeContext.setTheme).toHaveBeenCalledTimes(1)
      expect(mockThemeContext.setTheme).toHaveBeenCalledWith('dark')
    })

    it('calls setTheme multiple times for multiple clicks', () => {
      render(<ThemeToggle />)
      
      const button = screen.getByRole('button')
      fireEvent.click(button)
      fireEvent.click(button)
      fireEvent.click(button)
      
      expect(mockThemeContext.setTheme).toHaveBeenCalledTimes(3)
    })
  })

  describe('Theme States', () => {
    it('shows appropriate label for light theme', () => {
      mockThemeContext.resolvedTheme = 'light'
      render(<ThemeToggle />)
      
      const button = screen.getByRole('button')
      expect(button).toHaveAttribute('title', 'Switch to dark mode')
    })

    it('shows appropriate label for dark theme', () => {
      mockThemeContext.resolvedTheme = 'dark'
      render(<ThemeToggle />)
      
      const button = screen.getByRole('button')
      expect(button).toHaveAttribute('title', 'Switch to light mode')
    })
  })

  describe('Accessibility', () => {
    it('is focusable', () => {
      render(<ThemeToggle />)
      
      const button = screen.getByRole('button')
      button.focus()
      
      expect(document.activeElement).toBe(button)
    })

    it('updates when theme context changes', () => {
      const { rerender } = render(<ThemeToggle />)
      
      // Start with light theme
      expect(screen.getByRole('button')).toHaveAttribute('title', 'Switch to dark mode')
      
      // Change to dark theme
      mockThemeContext.resolvedTheme = 'dark'
      rerender(<ThemeToggle />)
      
      expect(screen.getByRole('button')).toHaveAttribute('title', 'Switch to light mode')
    })
  })
}) 