import React from 'react'
import { render, screen, waitFor } from '@testing-library/react'
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import SystemStatus from '../../components/SystemStatus'
import { HealthStatus } from '../../api/apiClient'

// Mock the health store
const mockHealthStore = {
  health: null as any,
  isLoading: false,
  startHealthMonitoring: vi.fn(),
  stopHealthMonitoring: vi.fn()
}

vi.mock('../../store/healthStore', () => ({
  default: () => mockHealthStore
}))

describe('SystemStatus', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    // Reset mock store state
    mockHealthStore.health = null
    mockHealthStore.isLoading = false
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  describe('Component Lifecycle', () => {
    it('starts health monitoring on mount', () => {
      render(<SystemStatus />)
      
      expect(mockHealthStore.startHealthMonitoring).toHaveBeenCalledTimes(1)
    })

    it('stops health monitoring on unmount', () => {
      const { unmount } = render(<SystemStatus />)
      
      unmount()
      
      expect(mockHealthStore.stopHealthMonitoring).toHaveBeenCalledTimes(1)
    })
  })

  describe('Loading State', () => {
    it('shows loading state when isLoading is true and no health data', () => {
      mockHealthStore.isLoading = true
      mockHealthStore.health = null
      
      render(<SystemStatus />)
      
      expect(screen.getByText('Checking System...')).toBeInTheDocument()
      
      // Should show pulsing gray dot
      const dot = screen.getByText('Checking System...').previousElementSibling
      expect(dot).toHaveClass('bg-gray-500', 'animate-pulse')
    })

    it('does not show loading state when health data exists', () => {
      mockHealthStore.isLoading = true
      mockHealthStore.health = {
        status: HealthStatus.HEALTHY,
        timestamp: new Date().toISOString(),
        version: '1.0.0',
        database: {},
        services: {}
      }
      
      render(<SystemStatus />)
      
      expect(screen.queryByText('Checking System...')).not.toBeInTheDocument()
      expect(screen.getByText('System Active')).toBeInTheDocument()
    })
  })

  describe('Health Status Display', () => {
    it('displays healthy status correctly', () => {
      mockHealthStore.health = {
        status: HealthStatus.HEALTHY,
        timestamp: new Date().toISOString(),
        version: '1.0.0',
        database: {},
        services: {}
      }
      
      render(<SystemStatus />)
      
      expect(screen.getByText('System Active')).toBeInTheDocument()
      
      const statusText = screen.getByText('System Active')
      expect(statusText).toHaveClass('text-green-500')
      
      // Check dot styling
      const dot = statusText.previousElementSibling
      expect(dot).toHaveClass('bg-green-500', 'animate-pulse')
    })

    it('displays degraded status correctly', () => {
      mockHealthStore.health = {
        status: HealthStatus.DEGRADED,
        timestamp: new Date().toISOString(),
        version: '1.0.0',
        database: {},
        services: {}
      }
      
      render(<SystemStatus />)
      
      expect(screen.getByText('System Degraded')).toBeInTheDocument()
      
      const statusText = screen.getByText('System Degraded')
      expect(statusText).toHaveClass('text-yellow-500')
      
      // Check dot styling
      const dot = statusText.previousElementSibling
      expect(dot).toHaveClass('bg-yellow-500', 'animate-ping')
    })

    it('displays unhealthy status correctly', () => {
      mockHealthStore.health = {
        status: HealthStatus.UNHEALTHY,
        timestamp: new Date().toISOString(),
        version: '1.0.0',
        database: {},
        services: {}
      }
      
      render(<SystemStatus />)
      
      expect(screen.getByText('System Issues')).toBeInTheDocument()
      
      const statusText = screen.getByText('System Issues')
      expect(statusText).toHaveClass('text-red-500')
      
      // Check dot styling
      const dot = statusText.previousElementSibling
      expect(dot).toHaveClass('bg-red-500', 'animate-bounce')
    })

    it('displays unknown status correctly', () => {
      mockHealthStore.health = {
        status: undefined as any,
        timestamp: new Date().toISOString(),
        version: '1.0.0',
        database: {},
        services: {}
      }
      
      render(<SystemStatus />)
      
      expect(screen.getByText('Checking...')).toBeInTheDocument()
      
      const statusText = screen.getByText('Checking...')
      expect(statusText).toHaveClass('text-gray-500')
      
      // Check dot styling
      const dot = statusText.previousElementSibling
      expect(dot).toHaveClass('bg-gray-500', 'animate-pulse')
    })
  })

  describe('Version Display', () => {
    it('displays version when available', () => {
      mockHealthStore.health = {
        status: HealthStatus.HEALTHY,
        timestamp: new Date().toISOString(),
        version: '2.1.0',
        database: {},
        services: {}
      }
      
      render(<SystemStatus />)
      
      expect(screen.getByText('v2.1.0')).toBeInTheDocument()
    })

    it('does not display version when unknown', () => {
      mockHealthStore.health = {
        status: HealthStatus.HEALTHY,
        timestamp: new Date().toISOString(),
        version: 'unknown',
        database: {},
        services: {}
      }
      
      render(<SystemStatus />)
      
      expect(screen.queryByText(/^v/)).not.toBeInTheDocument()
    })

    it('does not display version when not provided', () => {
      mockHealthStore.health = {
        status: HealthStatus.HEALTHY,
        timestamp: new Date().toISOString(),
        version: 'unknown',
        database: {},
        services: {}
      }
      
      render(<SystemStatus />)
      
      expect(screen.queryByText(/^v/)).not.toBeInTheDocument()
    })
  })

  describe('Tooltip and Accessibility', () => {
    it('shows tooltip with timestamp when health data is available', () => {
      const timestamp = new Date('2024-01-01T12:00:00Z')
      mockHealthStore.health = {
        status: HealthStatus.HEALTHY,
        timestamp: timestamp.toISOString(),
        version: '1.0.0',
        database: {},
        services: {}
      }
      
      render(<SystemStatus />)
      
      const container = screen.getByText('System Active').closest('div')
      expect(container).toHaveAttribute('title', `Last checked: ${timestamp.toLocaleTimeString()}`)
    })

    it('shows fallback tooltip when no health data', () => {
      mockHealthStore.health = null
      mockHealthStore.isLoading = false
      
      render(<SystemStatus />)
      
      const container = screen.getByText('Checking...').closest('div')
      expect(container).toHaveAttribute('title', 'System status unknown')
    })

    it('has proper cursor styling', () => {
      mockHealthStore.health = {
        status: HealthStatus.HEALTHY,
        timestamp: new Date().toISOString(),
        version: '1.0.0',
        database: {},
        services: {}
      }
      
      render(<SystemStatus />)
      
      const container = screen.getByText('System Active').closest('div')
      expect(container).toHaveClass('cursor-help')
    })
  })

  describe('Responsive Design', () => {
    it('is hidden on small screens', () => {
      mockHealthStore.health = {
        status: HealthStatus.HEALTHY,
        timestamp: new Date().toISOString(),
        version: '1.0.0',
        database: {},
        services: {}
      }
      
      render(<SystemStatus />)
      
      const container = screen.getByText('System Active').closest('div')
      expect(container).toHaveClass('hidden', 'sm:flex')
    })
  })

  describe('Theme Support', () => {
    it('applies correct theme classes', () => {
      mockHealthStore.health = {
        status: HealthStatus.HEALTHY,
        timestamp: new Date().toISOString(),
        version: '1.0.0',
        database: {},
        services: {}
      }
      
      render(<SystemStatus />)
      
      const container = screen.getByText('System Active').closest('div')
      expect(container).toHaveClass(
        'text-light-secondary',
        'dark:text-dark-secondary',
        'bg-light-component-subtle/50',
        'dark:bg-dark-component-subtle/50'
      )
    })

    it('applies hover theme classes', () => {
      mockHealthStore.health = {
        status: HealthStatus.HEALTHY,
        timestamp: new Date().toISOString(),
        version: '1.0.0',
        database: {},
        services: {}
      }
      
      render(<SystemStatus />)
      
      const container = screen.getByText('System Active').closest('div')
      expect(container).toHaveClass(
        'hover:bg-light-component-subtle',
        'dark:hover:bg-dark-component-subtle'
      )
    })
  })

  describe('Status Updates', () => {
    it('updates display when health status changes', async () => {
      // Start with healthy status
      mockHealthStore.health = {
        status: HealthStatus.HEALTHY,
        timestamp: new Date().toISOString(),
        version: '1.0.0',
        database: {},
        services: {}
      }
      
      const { rerender } = render(<SystemStatus />)
      expect(screen.getByText('System Active')).toBeInTheDocument()
      
      // Change to unhealthy status
      mockHealthStore.health = {
        status: HealthStatus.UNHEALTHY,
        timestamp: new Date().toISOString(),
        version: '1.0.0',
        database: {},
        services: {}
      }
      
      rerender(<SystemStatus />)
      
      await waitFor(() => {
        expect(screen.getByText('System Issues')).toBeInTheDocument()
        expect(screen.queryByText('System Active')).not.toBeInTheDocument()
      })
    })
  })
}) 