import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import useHealthStore from '../../store/healthStore'
import { HealthStatus } from '../../api/apiClient'

// Mock the API client
vi.mock('../../api/apiClient', () => ({
  api: {
    getHealth: vi.fn()
  },
  HealthStatus: {
    HEALTHY: 'healthy',
    DEGRADED: 'degraded',
    UNHEALTHY: 'unhealthy',
    UNKNOWN: 'unknown'
  }
}))

// Import the mocked module to get access to the mock function
import { api } from '../../api/apiClient'
const mockGetHealth = vi.mocked(api.getHealth)

// Helper function to create proper mock health data
const createMockHealthData = (overrides: Partial<any> = {}) => ({
  status: HealthStatus.HEALTHY,
  timestamp: new Date().toISOString(),
  version: '1.0.0',
  database: {
    connection: { status: HealthStatus.HEALTHY, message: 'OK' },
    tables: { status: HealthStatus.HEALTHY, message: 'OK' }
  },
  services: {
    api: { status: HealthStatus.HEALTHY, message: 'OK' }
  },
  ...overrides
})

describe('useHealthStore', () => {
  let consoleErrorSpy: any

  beforeEach(() => {
    vi.clearAllMocks()
    vi.clearAllTimers()
    vi.useFakeTimers()
    
    // Mock console.error
    consoleErrorSpy = vi.spyOn(console, 'error').mockImplementation(() => {})
    
    // Reset store state
    const store = useHealthStore.getState()
    store.stopHealthMonitoring()
    useHealthStore.setState({
      health: null,
      isLoading: false,
      error: null,
      lastChecked: null,
      checkInterval: null
    })
  })

  afterEach(() => {
    vi.useRealTimers()
    const store = useHealthStore.getState()
    store.stopHealthMonitoring()
    consoleErrorSpy.mockRestore()
  })

  describe('Initial State', () => {
    it('has correct initial state', () => {
      const store = useHealthStore.getState()
      
      expect(store.health).toBeNull()
      expect(store.isLoading).toBe(false)
      expect(store.error).toBeNull()
      expect(store.lastChecked).toBeNull()
      expect(store.checkInterval).toBeNull()
    })
  })

  describe('checkHealth', () => {
    it('sets loading state during health check', async () => {
      const mockHealthData = createMockHealthData()

      mockGetHealth.mockResolvedValue(mockHealthData)

      const store = useHealthStore.getState()
      const checkPromise = store.checkHealth()

      // Should be loading
      expect(useHealthStore.getState().isLoading).toBe(true)

      await checkPromise

      // Should no longer be loading
      expect(useHealthStore.getState().isLoading).toBe(false)
    })

    it('updates health data on successful check', async () => {
      const mockHealthData = createMockHealthData()

      mockGetHealth.mockResolvedValue(mockHealthData)

      const store = useHealthStore.getState()
      await store.checkHealth()

      const state = useHealthStore.getState()
      expect(state.health).toEqual(mockHealthData)
      expect(state.error).toBeNull()
      expect(state.lastChecked).toBeInstanceOf(Date)
    })

    it('handles API errors gracefully', async () => {
      const errorMessage = 'Network error'
      mockGetHealth.mockRejectedValue(new Error(errorMessage))

      const store = useHealthStore.getState()
      await store.checkHealth()

      const state = useHealthStore.getState()
      expect(state.error).toBe(errorMessage)
      expect(state.health?.status).toBe(HealthStatus.UNHEALTHY)
      expect(state.health?.services?.api?.message).toBe(errorMessage)
    })

    it('creates fallback health response on error', async () => {
      mockGetHealth.mockRejectedValue(new Error('Connection failed'))

      const store = useHealthStore.getState()
      await store.checkHealth()

      const state = useHealthStore.getState()
      expect(state.health).not.toBeNull()
      expect(state.health?.status).toBe(HealthStatus.UNHEALTHY)
      expect(state.health?.version).toBe('unknown')
      expect(state.health?.database?.connection?.status).toBe(HealthStatus.UNKNOWN)
      expect(state.health?.database?.tables?.status).toBe(HealthStatus.UNKNOWN)
    })

    it('handles non-Error exceptions', async () => {
      mockGetHealth.mockRejectedValue('String error')

      const store = useHealthStore.getState()
      await store.checkHealth()

      const state = useHealthStore.getState()
      expect(state.error).toBe('Health check failed')
      expect(state.health?.status).toBe(HealthStatus.UNHEALTHY)
    })
  })

  describe('startHealthMonitoring', () => {
    it('performs initial health check', async () => {
      const mockHealthData = createMockHealthData()

      mockGetHealth.mockResolvedValue(mockHealthData)

      const store = useHealthStore.getState()
      store.startHealthMonitoring()

      // The initial check happens immediately, not via timer
      // Wait for the promise to resolve
      await vi.waitFor(() => {
        return useHealthStore.getState().health !== null
      })

      expect(mockGetHealth).toHaveBeenCalledTimes(1)
      expect(useHealthStore.getState().health).toEqual(mockHealthData)
    })

    it('sets up periodic health checks', async () => {
      const mockHealthData = createMockHealthData()

      mockGetHealth.mockResolvedValue(mockHealthData)

      const store = useHealthStore.getState()
      store.startHealthMonitoring()

      // Wait for initial check to complete
      await vi.waitFor(() => {
        return useHealthStore.getState().health !== null
      })
      expect(mockGetHealth).toHaveBeenCalledTimes(1)

      // Clear mock to track only periodic calls
      mockGetHealth.mockClear()

      // Advance time by 30 seconds (health check interval)
      vi.advanceTimersByTime(30000)
      
      // Wait for the periodic check to complete
      await vi.waitFor(() => {
        return mockGetHealth.mock.calls.length >= 1
      })
      expect(mockGetHealth).toHaveBeenCalledTimes(1)

      // Another 30 seconds
      vi.advanceTimersByTime(30000)
      
      // Wait for the second periodic check
      await vi.waitFor(() => {
        return mockGetHealth.mock.calls.length >= 2
      })
      expect(mockGetHealth).toHaveBeenCalledTimes(2)
    })

    it('clears existing interval before setting new one', async () => {
      const mockHealthData = createMockHealthData()

      mockGetHealth.mockResolvedValue(mockHealthData)

      const store = useHealthStore.getState()
      
      // Start monitoring twice
      store.startHealthMonitoring()
      const firstInterval = useHealthStore.getState().checkInterval
      
      store.startHealthMonitoring()
      const secondInterval = useHealthStore.getState().checkInterval

      // Should have different interval IDs
      expect(firstInterval).not.toBe(secondInterval)
      expect(secondInterval).not.toBeNull()
    })

    it('stores interval reference', () => {
      const store = useHealthStore.getState()
      store.startHealthMonitoring()

      const state = useHealthStore.getState()
      expect(state.checkInterval).not.toBeNull()
      expect(typeof state.checkInterval).toBe('object') // NodeJS.Timeout
    })
  })

  describe('stopHealthMonitoring', () => {
    it('clears the health check interval', async () => {
      const mockHealthData = createMockHealthData()

      mockGetHealth.mockResolvedValue(mockHealthData)

      const store = useHealthStore.getState()
      store.startHealthMonitoring()

      // Verify interval is set
      expect(useHealthStore.getState().checkInterval).not.toBeNull()

      // Stop monitoring
      store.stopHealthMonitoring()

      // Interval should be cleared
      expect(useHealthStore.getState().checkInterval).toBeNull()
    })

    it('handles case when no interval is set', () => {
      const store = useHealthStore.getState()
      
      // Should not throw when no interval is set
      expect(() => store.stopHealthMonitoring()).not.toThrow()
      expect(useHealthStore.getState().checkInterval).toBeNull()
    })

    it('stops periodic checks', async () => {
      const mockHealthData = createMockHealthData()

      mockGetHealth.mockResolvedValue(mockHealthData)

      const store = useHealthStore.getState()
      store.startHealthMonitoring()

      // Wait for initial check
      await vi.waitFor(() => {
        return useHealthStore.getState().health !== null
      })
      expect(mockGetHealth).toHaveBeenCalledTimes(1)

      // Stop monitoring
      store.stopHealthMonitoring()

      // Advance time - should not trigger more calls
      vi.advanceTimersByTime(60000)
      await vi.runOnlyPendingTimersAsync()
      expect(mockGetHealth).toHaveBeenCalledTimes(1) // Still only 1 call
    })
  })

  describe('Error Recovery', () => {
    it('continues monitoring after API errors', async () => {
      // First call fails
      mockGetHealth.mockRejectedValueOnce(new Error('Network error'))
      
      // Second call succeeds
      const mockHealthData = createMockHealthData()
      mockGetHealth.mockResolvedValueOnce(mockHealthData)

      const store = useHealthStore.getState()
      store.startHealthMonitoring()

      // Wait for initial check (fails)
      await vi.waitFor(() => {
        return useHealthStore.getState().error !== null
      })
      expect(useHealthStore.getState().error).toBe('Network error')
      expect(mockGetHealth).toHaveBeenCalledTimes(1)

      // Reset mock call count for clarity
      mockGetHealth.mockClear()
      mockGetHealth.mockResolvedValue(mockHealthData)

      // Next check (succeeds)
      vi.advanceTimersByTime(30000)
      await vi.runOnlyPendingTimersAsync()
      expect(useHealthStore.getState().error).toBeNull()
      expect(useHealthStore.getState().health).toEqual(mockHealthData)
    })

    it('maintains monitoring state across errors', async () => {
      mockGetHealth.mockRejectedValue(new Error('Persistent error'))

      const store = useHealthStore.getState()
      store.startHealthMonitoring()

      // Wait for initial check
      await vi.waitFor(() => {
        return useHealthStore.getState().error !== null
      })
      expect(mockGetHealth).toHaveBeenCalledTimes(1)

      // Clear mock to track only periodic calls
      mockGetHealth.mockClear()
      
      // Advance time for periodic checks
      vi.advanceTimersByTime(30000)
      
      // Wait for the periodic check
      await vi.waitFor(() => {
        return mockGetHealth.mock.calls.length >= 1
      })
      expect(mockGetHealth).toHaveBeenCalledTimes(1)
      
      vi.advanceTimersByTime(30000)
      
      // Wait for the second periodic check
      await vi.waitFor(() => {
        return mockGetHealth.mock.calls.length >= 2
      })
      expect(mockGetHealth).toHaveBeenCalledTimes(2)

      // Should still be monitoring (interval should exist)
      expect(useHealthStore.getState().checkInterval).not.toBeNull()
    })
  })

  describe('State Management', () => {
    it('preserves health data between checks', async () => {
      const initialHealthData = createMockHealthData()

      const updatedHealthData = createMockHealthData({
        status: HealthStatus.DEGRADED,
        timestamp: '2024-01-01T12:30:00Z',
        version: '1.0.0',
        database: {},
        services: {}
      })

      // First check
      mockGetHealth.mockResolvedValueOnce(initialHealthData)
      const store = useHealthStore.getState()
      await store.checkHealth()
      expect(useHealthStore.getState().health).toEqual(initialHealthData)

      // Second check with updated data
      mockGetHealth.mockResolvedValueOnce(updatedHealthData)
      await store.checkHealth()
      expect(useHealthStore.getState().health).toEqual(updatedHealthData)
    })

    it('updates lastChecked timestamp on each check', async () => {
      const mockHealthData = createMockHealthData()

      mockGetHealth.mockResolvedValue(mockHealthData)

      const store = useHealthStore.getState()
      
      // First check
      await store.checkHealth()
      const firstCheck = useHealthStore.getState().lastChecked

      // Wait a bit
      vi.advanceTimersByTime(1000)

      // Second check
      await store.checkHealth()
      const secondCheck = useHealthStore.getState().lastChecked

      expect(firstCheck).toBeInstanceOf(Date)
      expect(secondCheck).toBeInstanceOf(Date)
      expect(secondCheck!.getTime()).toBeGreaterThan(firstCheck!.getTime())
    })
  })
}) 