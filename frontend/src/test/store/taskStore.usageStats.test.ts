import { describe, it, expect, beforeEach, vi } from 'vitest'
import useTaskStore from '../../store/taskStore'
import type { TaskState } from '../../store/taskStore'
import { mockGenerationWithUsageStats, mockGenerationWithoutUsageStats, mockRankingWithUsageStats } from '../mocks/usageStatsMocks'

// Mock axios
vi.mock('axios', () => ({
  default: {
    create: () => ({
      get: vi.fn(),
      post: vi.fn(),
      put: vi.fn(),
      delete: vi.fn(),
    }),
  },
}))

describe('TaskStore - Usage Statistics', () => {
  beforeEach(() => {
    // Reset store state before each test
    useTaskStore.setState({
      selectedTaskId: null,
      tasks: {},
      taskHistory: [],
      availableModels: [],
      activeHistoryPollIntervalId: null,
      isLoadingModels: false,
      isLoadingHistory: false,
      pageError: null,
      errorLoadingHistory: null
    })
    vi.clearAllMocks()
  })

  describe('Usage Statistics Calculation', () => {
    it('should calculate aggregated generation usage statistics correctly', () => {
      const taskWithGenerations: TaskState = {
        id: 1,
        prompt: 'Test prompt',
        system_prompt: 'Test system prompt',
        requestedModels: ['gpt-4', 'claude-3-sonnet'],
        outputs: [
          mockGenerationWithUsageStats,
          {
            ...mockGenerationWithUsageStats,
            id: 2,
            prompt_tokens: 200,
            completion_tokens: 300,
            total_tokens: 500,
            reasoning_tokens: 100,
            cached_tokens: 50,
            cost_credits: 0.005000,
            generation_id: 'gen-789'
          }
        ],
        currentEvaluationId: null,
        currentReport: null,
        evaluations: [],
        cacheTimestamp: Date.now(),
        isGenerating: false,
        isStreaming: false,
        isEvaluating: false,
        errorMessage: null,
        sseEventSource: null,
        modelsDoneStreamingThisTask: new Set<string>(),
        taskStatusFromBackend: 'COMPLETED',
        aggregatedReportsByAlgorithm: null,
        isAggregating: false,
        aggregationError: null,
        chunkBuffer: {},
        bufferFlushTimer: null,
        lastFlushTime: 0
      }

      useTaskStore.setState((state) => ({
        ...state,
        tasks: { ...state.tasks, 1: taskWithGenerations }
      }))

      // In a real application, you'd have a selector or computed value
      // For this test, let's simulate the aggregation logic
      const generations = taskWithGenerations.outputs.filter(
        output => output.output_text && !output.error_message && output.generation_id
      )

      const aggregatedUsage = generations.reduce((acc, output) => ({
        prompt_tokens: acc.prompt_tokens + (output.prompt_tokens || 0),
        completion_tokens: acc.completion_tokens + (output.completion_tokens || 0),
        total_tokens: acc.total_tokens + (output.total_tokens || 0),
        reasoning_tokens: acc.reasoning_tokens + (output.reasoning_tokens || 0),
        cached_tokens: acc.cached_tokens + (output.cached_tokens || 0),
        cost_credits: acc.cost_credits + (output.cost_credits || 0),
        count: acc.count + 1
      }), {
        prompt_tokens: 0,
        completion_tokens: 0,
        total_tokens: 0,
        reasoning_tokens: 0,
        cached_tokens: 0,
        cost_credits: 0,
        count: 0
      })

      expect(aggregatedUsage.prompt_tokens).toBe(300) // 100 + 200
      expect(aggregatedUsage.completion_tokens).toBe(450) // 150 + 300
      expect(aggregatedUsage.total_tokens).toBe(750) // 250 + 500
      expect(aggregatedUsage.reasoning_tokens).toBe(150) // 50 + 100
      expect(aggregatedUsage.cached_tokens).toBe(75) // 25 + 50
      expect(aggregatedUsage.cost_credits).toBe(0.007500) // 0.002500 + 0.005000
      expect(aggregatedUsage.count).toBe(2)
    })

    it('should exclude generations without usage statistics from aggregation', () => {
      const taskWithMixedGenerations: TaskState = {
        id: 1,
        prompt: 'Test prompt',
        system_prompt: 'Test system prompt',
        requestedModels: ['gpt-4', 'legacy-model', 'claude-3-sonnet'],
        outputs: [
          mockGenerationWithUsageStats,
          mockGenerationWithoutUsageStats, // This should be excluded
          {
            ...mockGenerationWithUsageStats,
            id: 3,
            generation_id: 'gen-456'
          }
        ],
        currentEvaluationId: null,
        currentReport: null,
        evaluations: [],
        cacheTimestamp: Date.now(),
        isGenerating: false,
        isStreaming: false,
        isEvaluating: false,
        errorMessage: null,
        sseEventSource: null,
        modelsDoneStreamingThisTask: new Set<string>(),
        taskStatusFromBackend: 'COMPLETED',
        aggregatedReportsByAlgorithm: null,
        isAggregating: false,
        aggregationError: null,
        chunkBuffer: {},
        bufferFlushTimer: null,
        lastFlushTime: 0
      }

      useTaskStore.setState((state) => ({
        ...state,
        tasks: { ...state.tasks, 1: taskWithMixedGenerations }
      }))

      // Simulate filtering logic
      const generationsWithUsage = taskWithMixedGenerations.outputs.filter(
        output => output.output_text && !output.error_message && output.generation_id
      )

      expect(generationsWithUsage).toHaveLength(2)
      expect(generationsWithUsage.some(g => g.id === 2)).toBe(false) // excluded without generation_id
    })

    it('should handle empty generations list', () => {
      const taskWithoutGenerations: TaskState = {
        id: 1,
        prompt: 'Test prompt',
        system_prompt: 'Test system prompt',
        requestedModels: [],
        outputs: [],
        currentEvaluationId: null,
        currentReport: null,
        evaluations: [],
        cacheTimestamp: Date.now(),
        isGenerating: false,
        isStreaming: false,
        isEvaluating: false,
        errorMessage: null,
        sseEventSource: null,
        modelsDoneStreamingThisTask: new Set<string>(),
        taskStatusFromBackend: 'PENDING',
        aggregatedReportsByAlgorithm: null,
        isAggregating: false,
        aggregationError: null,
        chunkBuffer: {},
        bufferFlushTimer: null,
        lastFlushTime: 0
      }

      useTaskStore.setState((state) => ({
        ...state,
        tasks: { ...state.tasks, 1: taskWithoutGenerations }
      }))

      const { tasks } = useTaskStore.getState()
      expect(tasks[1].outputs).toHaveLength(0)
    })
  })

  describe('Evaluation Usage Statistics', () => {
    it('should aggregate evaluation usage statistics correctly', () => {
      const evaluation = {
        id: 1,
        task_id: 1,
        status: 'EVALUATION_DONE' as const,
        evaluation_used_blind_ids: true,
        evaluation_prompt: undefined,
        created_at: '2024-01-01T12:00:00Z',
        updated_at: '2024-01-01T12:15:00Z',
        rankings: [
          mockRankingWithUsageStats,
          {
            ...mockRankingWithUsageStats,
            id: 2,
            prompt_tokens: 300,
            completion_tokens: 150,
            total_tokens: 450,
            reasoning_tokens: 125,
            cached_tokens: 75,
            cost_credits: 0.004500,
            generation_id: 'eval-789'
          }
        ]
      }

      const taskWithEvaluations: TaskState = {
        id: 1,
        prompt: 'Test prompt',
        system_prompt: 'Test system prompt',
        requestedModels: [],
        outputs: [],
        currentEvaluationId: 1,
        currentReport: null,
        evaluations: [evaluation],
        cacheTimestamp: Date.now(),
        isGenerating: false,
        isStreaming: false,
        isEvaluating: false,
        errorMessage: null,
        sseEventSource: null,
        modelsDoneStreamingThisTask: new Set<string>(),
        taskStatusFromBackend: 'EVALUATION_DONE',
        aggregatedReportsByAlgorithm: null,
        isAggregating: false,
        aggregationError: null,
        chunkBuffer: {},
        bufferFlushTimer: null,
        lastFlushTime: 0
      }

      useTaskStore.setState((state) => ({
        ...state,
        tasks: { ...state.tasks, 1: taskWithEvaluations }
      }))

      // Simulate evaluation usage aggregation
      const allRankings = (taskWithEvaluations.evaluations || []).flatMap(ev => ev.rankings || [])
      const rankingsWithUsage = allRankings.filter(r => !r.error_message && r.generation_id)

      const evaluationUsage = rankingsWithUsage.reduce((acc, ranking) => ({
        prompt_tokens: acc.prompt_tokens + (ranking.prompt_tokens || 0),
        completion_tokens: acc.completion_tokens + (ranking.completion_tokens || 0),
        total_tokens: acc.total_tokens + (ranking.total_tokens || 0),
        reasoning_tokens: acc.reasoning_tokens + (ranking.reasoning_tokens || 0),
        cached_tokens: acc.cached_tokens + (ranking.cached_tokens || 0),
        cost_credits: acc.cost_credits + (ranking.cost_credits || 0),
        count: acc.count + 1
      }), {
        prompt_tokens: 0,
        completion_tokens: 0,
        total_tokens: 0,
        reasoning_tokens: 0,
        cached_tokens: 0,
        cost_credits: 0,
        count: 0
      })

      expect(evaluationUsage.prompt_tokens).toBe(500) // 200 + 300
      expect(evaluationUsage.completion_tokens).toBe(250) // 100 + 150
      expect(evaluationUsage.total_tokens).toBe(750) // 300 + 450
      expect(evaluationUsage.reasoning_tokens).toBe(200) // 75 + 125
      expect(evaluationUsage.cached_tokens).toBe(125) // 50 + 75
      expect(evaluationUsage.cost_credits).toBe(0.007500) // 0.003000 + 0.004500
      expect(evaluationUsage.count).toBe(2)
    })
  })

  describe('Task State Updates with Usage Statistics', () => {
    it('should preserve usage statistics when updating task outputs', () => {
      // Set initial task
      const initialTask: TaskState = {
        id: 1,
        prompt: 'Test prompt',
        system_prompt: 'Test system prompt',
        requestedModels: ['gpt-4'],
        outputs: [mockGenerationWithUsageStats],
        currentEvaluationId: null,
        currentReport: null,
        evaluations: [],
        cacheTimestamp: Date.now(),
        isGenerating: false,
        isStreaming: false,
        isEvaluating: false,
        errorMessage: null,
        sseEventSource: null,
        modelsDoneStreamingThisTask: new Set<string>(),
        taskStatusFromBackend: 'COMPLETED',
        aggregatedReportsByAlgorithm: null,
        isAggregating: false,
        aggregationError: null,
        chunkBuffer: {},
        bufferFlushTimer: null,
        lastFlushTime: 0
      }
      
      useTaskStore.setState((state) => ({
        ...state,
        tasks: { ...state.tasks, 1: initialTask }
      }))

      // Update output with additional text but preserve usage stats
      useTaskStore.setState((state) => ({
        ...state,
        tasks: {
          ...state.tasks,
          1: {
            ...state.tasks[1],
            outputs: state.tasks[1].outputs.map(output =>
              output.blind_id === 'blind-123' 
                ? { ...output, output_text: 'Updated output text' }
                : output
            )
          }
        }
      }))

      const { tasks } = useTaskStore.getState()
      const updatedGeneration = tasks[1].outputs.find(o => o.blind_id === 'blind-123')

      expect(updatedGeneration?.output_text).toBe('Updated output text')
      expect(updatedGeneration?.prompt_tokens).toBe(100)
      expect(updatedGeneration?.completion_tokens).toBe(150)
      expect(updatedGeneration?.total_tokens).toBe(250)
      expect(updatedGeneration?.cost_credits).toBe(0.002500)
      expect(updatedGeneration?.generation_id).toBe('gen-123456')
    })

    it('should handle streaming updates without losing usage statistics', () => {
      const streamingTask: TaskState = {
        id: 1,
        prompt: 'Test prompt',
        system_prompt: 'Test system prompt',
        requestedModels: ['gpt-4'],
        outputs: [mockGenerationWithUsageStats],
        currentEvaluationId: null,
        currentReport: null,
        evaluations: [],
        cacheTimestamp: Date.now(),
        isGenerating: true,
        isStreaming: true,
        isEvaluating: false,
        errorMessage: null,
        sseEventSource: null,
        modelsDoneStreamingThisTask: new Set<string>(),
        taskStatusFromBackend: 'GENERATING',
        aggregatedReportsByAlgorithm: null,
        isAggregating: false,
        aggregationError: null,
        chunkBuffer: {},
        bufferFlushTimer: null,
        lastFlushTime: 0
      }

      useTaskStore.setState((state) => ({
        ...state,
        tasks: { ...state.tasks, 1: streamingTask }
      }))

      // Simulate streaming update
      useTaskStore.setState((state) => ({
        ...state,
        tasks: {
          ...state.tasks,
          1: {
            ...state.tasks[1],
            outputs: state.tasks[1].outputs.map(output =>
              output.blind_id === 'blind-123' 
                ? { 
                    ...output, 
                    output_text: 'Partial output...',
                    // Usage stats might not be available yet during streaming
                    prompt_tokens: null,
                    completion_tokens: null,
                    total_tokens: null,
                    cost_credits: null,
                    generation_id: null
                  }
                : output
            )
          }
        }
      }))

      const { tasks } = useTaskStore.getState()
      const generation = tasks[1].outputs.find(o => o.blind_id === 'blind-123')

      expect(generation?.output_text).toBe('Partial output...')
      expect(generation?.prompt_tokens).toBeNull()
      expect(generation?.generation_id).toBeNull()
    })
  })

  describe('Error Handling', () => {
    it('should handle generations with error messages correctly', () => {
      const generationWithError = {
        ...mockGenerationWithUsageStats,
        id: 2,
        output_text: null,
        error_message: 'API rate limit exceeded',
        prompt_tokens: null,
        completion_tokens: null,
        total_tokens: null,
        cost_credits: null,
        generation_id: null
      }

      const taskWithErrors: TaskState = {
        id: 1,
        prompt: 'Test prompt',
        system_prompt: 'Test system prompt',
        requestedModels: ['gpt-4', 'claude-3-sonnet'],
        outputs: [mockGenerationWithUsageStats, generationWithError],
        currentEvaluationId: null,
        currentReport: null,
        evaluations: [],
        cacheTimestamp: Date.now(),
        isGenerating: false,
        isStreaming: false,
        isEvaluating: false,
        errorMessage: null,
        sseEventSource: null,
        modelsDoneStreamingThisTask: new Set<string>(),
        taskStatusFromBackend: 'COMPLETED',
        aggregatedReportsByAlgorithm: null,
        isAggregating: false,
        aggregationError: null,
        chunkBuffer: {},
        bufferFlushTimer: null,
        lastFlushTime: 0
      }

      useTaskStore.setState((state) => ({
        ...state,
        tasks: { ...state.tasks, 1: taskWithErrors }
      }))

      // Simulate filtering logic to exclude errored generations
      const validGenerations = taskWithErrors.outputs.filter(
        output => output.output_text && !output.error_message
      )

      expect(validGenerations).toHaveLength(1)
      expect(validGenerations[0].id).toBe(1)
    })

    it('should handle malformed usage statistics gracefully', () => {
      const generationWithMalformedStats = {
        ...mockGenerationWithUsageStats,
        prompt_tokens: -1, // Invalid negative value
        completion_tokens: undefined, // Undefined value
        total_tokens: NaN, // Invalid number
        cost_credits: 'invalid' as any, // Wrong type
      }

      const taskWithMalformedStats: TaskState = {
        id: 1,
        prompt: 'Test prompt',
        system_prompt: 'Test system prompt',
        requestedModels: ['gpt-4'],
        outputs: [generationWithMalformedStats],
        currentEvaluationId: null,
        currentReport: null,
        evaluations: [],
        cacheTimestamp: Date.now(),
        isGenerating: false,
        isStreaming: false,
        isEvaluating: false,
        errorMessage: null,
        sseEventSource: null,
        modelsDoneStreamingThisTask: new Set<string>(),
        taskStatusFromBackend: 'COMPLETED',
        aggregatedReportsByAlgorithm: null,
        isAggregating: false,
        aggregationError: null,
        chunkBuffer: {},
        bufferFlushTimer: null,
        lastFlushTime: 0
      }

      expect(() => {
        useTaskStore.setState((state) => ({
          ...state,
          tasks: { ...state.tasks, 1: taskWithMalformedStats }
        }))
      }).not.toThrow()

      const { tasks } = useTaskStore.getState()
      expect(tasks[1]).toBeDefined()
    })
  })

  describe('Performance Considerations', () => {
    it('should handle large numbers of generations efficiently', () => {
      // Create 100 generations with usage stats
      const manyGenerations = Array.from({ length: 100 }, (_, index) => ({
        ...mockGenerationWithUsageStats,
        id: index + 1,
        blind_id: `blind-${index + 1}`,
        generation_id: `gen-${index + 1}`
      }))

      const taskWithManyGenerations: TaskState = {
        id: 1,
        prompt: 'Test prompt',
        system_prompt: 'Test system prompt',
        requestedModels: Array.from({ length: 100 }, (_, i) => `model-${i}`),
        outputs: manyGenerations,
        currentEvaluationId: null,
        currentReport: null,
        evaluations: [],
        cacheTimestamp: Date.now(),
        isGenerating: false,
        isStreaming: false,
        isEvaluating: false,
        errorMessage: null,
        sseEventSource: null,
        modelsDoneStreamingThisTask: new Set<string>(),
        taskStatusFromBackend: 'COMPLETED',
        aggregatedReportsByAlgorithm: null,
        isAggregating: false,
        aggregationError: null,
        chunkBuffer: {},
        bufferFlushTimer: null,
        lastFlushTime: 0
      }

      const startTime = performance.now()
      useTaskStore.setState((state) => ({
        ...state,
        tasks: { ...state.tasks, 1: taskWithManyGenerations }
      }))
      const endTime = performance.now()

      // Should complete within reasonable time (less than 100ms)
      expect(endTime - startTime).toBeLessThan(100)

      const { tasks } = useTaskStore.getState()
      expect(tasks[1].outputs).toHaveLength(100)
    })
  })
})