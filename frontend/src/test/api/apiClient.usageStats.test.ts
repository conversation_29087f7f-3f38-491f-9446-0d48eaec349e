import { describe, it, expect, beforeEach, vi } from 'vitest'
import axios from 'axios'
import { mockGenerationWithUsageStats, mockRankingWithUsageStats } from '../mocks/usageStatsMocks'

// Mock axios
vi.mock('axios', () => ({
  default: {
    create: vi.fn(() => ({
      get: vi.fn(),
      post: vi.fn(),
      put: vi.fn(),
      delete: vi.fn(),
    })),
  },
}))

const mockedAxios = vi.mocked(axios, true)
const mockedApiClient = {
  get: vi.fn(),
  post: vi.fn(),
  put: vi.fn(),
  delete: vi.fn(),
}

// Setup the mock to return our mocked client
mockedAxios.create.mockReturnValue(mockedApiClient as any)

describe('API Client - Usage Statistics', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('Generation Usage Statistics', () => {
    it('should fetch generation with usage statistics', async () => {
      const mockResponse = {
        data: mockGenerationWithUsageStats
      }
      
      mockedApiClient.get.mockResolvedValueOnce(mockResponse)

      const result = await mockedApiClient.get('/api/v1/generations/1')

      expect(mockedApiClient.get).toHaveBeenCalledWith('/api/v1/generations/1')
      expect(result.data).toEqual(mockGenerationWithUsageStats)
      expect(result.data.prompt_tokens).toBe(100)
      expect(result.data.completion_tokens).toBe(150)
      expect(result.data.total_tokens).toBe(250)
      expect(result.data.reasoning_tokens).toBe(50)
      expect(result.data.cached_tokens).toBe(25)
      expect(result.data.cost_credits).toBe(0.002500)
      expect(result.data.generation_id).toBe('gen-123456')
    })

    it('should handle generation without usage statistics', async () => {
      const generationWithoutStats = {
        ...mockGenerationWithUsageStats,
        prompt_tokens: null,
        completion_tokens: null,
        total_tokens: null,
        reasoning_tokens: null,
        cached_tokens: null,
        cost_credits: null,
        generation_id: null
      }

      const mockResponse = {
        data: generationWithoutStats
      }
      
      mockedApiClient.get.mockResolvedValueOnce(mockResponse)

      const result = await mockedApiClient.get('/api/v1/generations/2')

      expect(result.data.prompt_tokens).toBeNull()
      expect(result.data.completion_tokens).toBeNull()
      expect(result.data.total_tokens).toBeNull()
      expect(result.data.reasoning_tokens).toBeNull()
      expect(result.data.cached_tokens).toBeNull()
      expect(result.data.cost_credits).toBeNull()
      expect(result.data.generation_id).toBeNull()
    })

    it('should handle API errors gracefully for generation stats', async () => {
      const errorMessage = 'Network Error'
      mockedApiClient.get.mockRejectedValueOnce(new Error(errorMessage))

      await expect(mockedApiClient.get('/api/v1/generations/999')).rejects.toThrow(errorMessage)
    })
  })

  describe('Ranking Usage Statistics', () => {
    it('should fetch ranking with usage statistics', async () => {
      const mockResponse = {
        data: mockRankingWithUsageStats
      }
      
      mockedApiClient.get.mockResolvedValueOnce(mockResponse)

      const result = await mockedApiClient.get('/api/v1/rankings/1')

      expect(mockedApiClient.get).toHaveBeenCalledWith('/api/v1/rankings/1')
      expect(result.data).toEqual(mockRankingWithUsageStats)
      expect(result.data.prompt_tokens).toBe(200)
      expect(result.data.completion_tokens).toBe(100)
      expect(result.data.total_tokens).toBe(300)
      expect(result.data.reasoning_tokens).toBe(75)
      expect(result.data.cached_tokens).toBe(50)
      expect(result.data.cost_credits).toBe(0.003000)
      expect(result.data.generation_id).toBe('eval-123456')
    })

    it('should handle ranking without usage statistics', async () => {
      const rankingWithoutStats = {
        ...mockRankingWithUsageStats,
        prompt_tokens: null,
        completion_tokens: null,
        total_tokens: null,
        reasoning_tokens: null,
        cached_tokens: null,
        cost_credits: null,
        generation_id: null
      }

      const mockResponse = {
        data: rankingWithoutStats
      }
      
      mockedApiClient.get.mockResolvedValueOnce(mockResponse)

      const result = await mockedApiClient.get('/api/v1/rankings/2')

      expect(result.data.prompt_tokens).toBeNull()
      expect(result.data.completion_tokens).toBeNull()
      expect(result.data.total_tokens).toBeNull()
      expect(result.data.reasoning_tokens).toBeNull()
      expect(result.data.cached_tokens).toBeNull()
      expect(result.data.cost_credits).toBeNull()
      expect(result.data.generation_id).toBeNull()
    })

    it('should handle API errors gracefully for ranking stats', async () => {
      const errorMessage = 'Server Error'
      mockedApiClient.get.mockRejectedValueOnce(new Error(errorMessage))

      await expect(mockedApiClient.get('/api/v1/rankings/999')).rejects.toThrow(errorMessage)
    })
  })

  describe('Usage Statistics Aggregation', () => {
    it('should fetch aggregated usage statistics for a task', async () => {
      const mockAggregatedStats = {
        generation_usage: {
          total_prompt_tokens: 500,
          total_completion_tokens: 750,
          total_tokens: 1250,
          total_reasoning_tokens: 200,
          total_cached_tokens: 100,
          total_cost_credits: 0.012500,
          model_count: 3
        },
        evaluation_usage: {
          total_prompt_tokens: 800,
          total_completion_tokens: 400,
          total_tokens: 1200,
          total_reasoning_tokens: 300,
          total_cached_tokens: 200,
          total_cost_credits: 0.015000,
          evaluator_count: 2
        }
      }

      const mockResponse = {
        data: mockAggregatedStats
      }
      
      mockedApiClient.get.mockResolvedValueOnce(mockResponse)

      const result = await mockedApiClient.get('/api/v1/tasks/1/usage-stats')

      expect(mockedApiClient.get).toHaveBeenCalledWith('/api/v1/tasks/1/usage-stats')
      expect(result.data).toEqual(mockAggregatedStats)
      expect(result.data.generation_usage.total_tokens).toBe(1250)
      expect(result.data.evaluation_usage.total_tokens).toBe(1200)
    })

    it('should handle empty usage statistics', async () => {
      const mockEmptyStats = {
        generation_usage: null,
        evaluation_usage: null
      }

      const mockResponse = {
        data: mockEmptyStats
      }
      
      mockedApiClient.get.mockResolvedValueOnce(mockResponse)

      const result = await mockedApiClient.get('/api/v1/tasks/2/usage-stats')

      expect(result.data.generation_usage).toBeNull()
      expect(result.data.evaluation_usage).toBeNull()
    })
  })

  describe('Usage Statistics Validation', () => {
    it('should validate usage statistics data types', async () => {
      const mockResponse = {
        data: mockGenerationWithUsageStats
      }
      
      mockedApiClient.get.mockResolvedValueOnce(mockResponse)

      const result = await mockedApiClient.get('/api/v1/generations/1')

      // Validate that numeric fields are numbers
      expect(typeof result.data.prompt_tokens).toBe('number')
      expect(typeof result.data.completion_tokens).toBe('number')
      expect(typeof result.data.total_tokens).toBe('number')
      expect(typeof result.data.reasoning_tokens).toBe('number')
      expect(typeof result.data.cached_tokens).toBe('number')
      expect(typeof result.data.cost_credits).toBe('number')
      expect(typeof result.data.generation_id).toBe('string')
    })

    it('should handle edge cases for cost calculation', async () => {
      const generationWithZeroCost = {
        ...mockGenerationWithUsageStats,
        cost_credits: 0.000000
      }

      const mockResponse = {
        data: generationWithZeroCost
      }
      
      mockedApiClient.get.mockResolvedValueOnce(mockResponse)

      const result = await mockedApiClient.get('/api/v1/generations/free')

      expect(result.data.cost_credits).toBe(0.000000)
    })

    it('should handle very large token counts', async () => {
      const generationWithLargeTokens = {
        ...mockGenerationWithUsageStats,
        prompt_tokens: 999999,
        completion_tokens: 999999,
        total_tokens: 1999998
      }

      const mockResponse = {
        data: generationWithLargeTokens
      }
      
      mockedApiClient.get.mockResolvedValueOnce(mockResponse)

      const result = await mockedApiClient.get('/api/v1/generations/large')

      expect(result.data.prompt_tokens).toBe(999999)
      expect(result.data.completion_tokens).toBe(999999)
      expect(result.data.total_tokens).toBe(1999998)
    })
  })
})