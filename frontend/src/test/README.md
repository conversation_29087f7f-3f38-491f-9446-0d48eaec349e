# Usage Statistics Test Suite

This directory contains comprehensive tests for the usage statistics functionality in the LLM Evaluation Platform frontend.

## Test Structure

```
src/test/
├── setup.ts                    # Global test setup and mocks
├── mocks/
│   └── usageStatsMocks.ts     # Mock data for usage statistics
├── api/
│   └── apiClient.usageStats.test.ts  # API client tests
├── components/
│   ├── UsageStatsCard.test.tsx      # Usage stats card component tests
│   └── CombinedUsageSummary.test.tsx # Combined usage summary tests
├── store/
│   └── taskStore.usageStats.test.ts  # Zustand store tests
└── integration/
    └── usageStats.integration.test.tsx # Full integration tests
```

## Running Tests

### Run all tests
```bash
npm test
```

### Run specific test file
```bash
npm test -- src/test/components/UsageStatsCard.test.tsx
```

### Run tests with coverage
```bash
npm run test:coverage
```

### Run tests in watch mode
```bash
npm test -- --watch
```

## Test Coverage

The test suite covers:

1. **API Client Layer**
   - Fetching tasks with usage statistics
   - Handling missing usage data
   - Error scenarios

2. **Component Testing**
   - UsageStatsCard rendering with various data states
   - CombinedUsageSummary aggregation logic
   - UI interactions and display formatting

3. **State Management**
   - Zustand store updates with usage statistics
   - Aggregation calculations
   - State persistence

4. **Integration Testing**
   - Full workflow from API to UI
   - Real-time updates with SSE
   - Model selection and filtering
   - Copy functionality with usage data

## Mock Data

The `usageStatsMocks.ts` file provides standardized mock data for:
- Generations with complete usage statistics
- Generations without usage statistics
- Rankings with usage statistics
- Various edge cases (zero cost, large tokens, etc.)

## Key Test Patterns

### Testing Async Operations
```typescript
await waitFor(() => {
  expect(screen.getByText('100')).toBeInTheDocument()
})
```

### Testing Aggregations
```typescript
const result = calculateGenerationUsageStats(generations)
expect(result.totalTokens).toBe(750)
```

### Testing Missing Data
```typescript
render(<UsageStatsCard {...props} promptTokens={null} />)
expect(screen.queryByText('Prompt')).not.toBeInTheDocument()
```

## Common Issues and Solutions

1. **Multiple elements with same text**: Use `getAllByText` and check array length
2. **Async state updates**: Use `waitFor` with appropriate timeout
3. **Mock data consistency**: Use centralized mocks from `usageStatsMocks.ts`
4. **SSE testing**: Mock EventSource in `setup.ts`

## Future Improvements

- Add visual regression tests for charts
- Performance benchmarks for large datasets
- E2E tests with real backend
- Accessibility tests for usage statistics displays