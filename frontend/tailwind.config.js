/** @type {import('tailwindcss').Config} */
module.exports = {
  content: ["./src/**/*.{js,jsx,ts,tsx}"],
  darkMode: 'class', // Enable dark mode using class strategy
  theme: {
    extend: {
      fontFamily: {
        sans: ['Inter', 'system-ui', '-apple-system', 'BlinkMacSystemFont', 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', 'sans-serif'],
      },
      colors: {
        // Glassmorphism-enhanced color palette
        // Light Theme Colors
        light: {
          primary: '#111827',      // Default text, headings (Almost Black)
          secondary: '#4B5563',  // Subdued text, icons (Gray-600)
          accent: '#5e72e4',      // Softer accent color (Blue-Indigo blend)
          'accent-hover': '#4c63d2', // Accent hover
          background: '#F0F4F8', // Softer page background with gray-blue tint
          component: '#FFFFFF',   // Component background (White)
          'component-subtle': '#F7F9FC', // Subtle component background
          'glass': 'rgba(255, 255, 255, 0.7)', // Glass background
          'glass-subtle': 'rgba(255, 255, 255, 0.5)', // Subtle glass
          'glass-border': 'rgba(255, 255, 255, 0.3)', // Glass border
          border: '#E2E8F0',     // Softer border
          'border-strong': '#CBD5E1', // Stronger border
          error: '#EF4444',       // Error text (Red-500)
          'error-bg': '#FEF2F2', // Error background (Red-50)
        },
        // Dark Theme Colors
        dark: {
          primary: '#F3F4F6',      // Default text, headings (Neutral-100)
          secondary: '#94A3B8',  // Softer subdued text (Slate-400)
          accent: '#7c8ff3',      // Softer accent color (Blue-Indigo blend)
          'accent-hover': '#6b7fe8', // Accent hover
          background: '#0F172A', // Deeper background (Slate-900)
          component: '#1E293B',   // Component background (Slate-800)
          'component-subtle': '#2D3748', // Darker subtle component background for better hover
          'glass': 'rgba(30, 41, 59, 0.7)', // Glass background
          'glass-subtle': 'rgba(30, 41, 59, 0.3)', // More subtle glass for hover
          'glass-border': 'rgba(148, 163, 184, 0.2)', // Glass border
          border: '#334155',     // Default border (Slate-700)
          'border-strong': '#475569', // Stronger border (Slate-600)
          error: '#F87171',       // Error text (Red-400)
          'error-bg': '#450a0a', // Error background (Red-900/50 -> approximation)
        },
        // Original neutral palette (can still be used for specific cases if needed)
        neutral: {
          50: '#F9FAFB',
          100: '#F3F4F6',
          200: '#E5E7EB',
          300: '#D1D5DB',
          400: '#9CA3AF',
          500: '#6B7280',
          600: '#4B5563',
          700: '#374151',
          800: '#1F2937',
          900: '#111827',
        },
        // Specific indigo colors for direct use if needed
        indigo: {
          50: '#eef2ff',
          100: '#e0e7ff',
          200: '#c7d2fe',
          300: '#a5b4fc',
          400: '#818cf8',
          500: '#6366f1',
          600: '#4f46e5',
          700: '#4338ca',
          800: '#3730a3',
          900: '#312e81',
        },
      },
      backdropBlur: {
        xs: '4px',
        sm: '8px',
        md: '12px',
        lg: '16px',
        xl: '24px',
        '2xl': '40px',
      },
      boxShadow: {
        'glass': '0 8px 32px 0 rgba(31, 38, 135, 0.37)',
        'glass-dark': '0 8px 32px 0 rgba(0, 0, 0, 0.37)',
        'glow': '0 0 20px rgba(94, 114, 228, 0.15)',
        'glow-dark': '0 0 20px rgba(124, 143, 243, 0.15)',
      },
      backgroundImage: {
        'gradient-radial': 'radial-gradient(var(--tw-gradient-stops))',
        'gradient-mesh': 'linear-gradient(to right bottom, var(--tw-gradient-stops))',
      },
      // Typography plugin setup
      typography: (theme) => ({
        DEFAULT: {
          css: {
            '--tw-prose-body': theme('colors.light.primary'),
            '--tw-prose-headings': theme('colors.light.primary'),
            '--tw-prose-lead': theme('colors.light.secondary'),
            '--tw-prose-links': theme('colors.light.accent'),
            '--tw-prose-bold': theme('colors.light.primary'),
            '--tw-prose-counters': theme('colors.light.secondary'),
            '--tw-prose-bullets': theme('colors.light.border-strong'),
            '--tw-prose-hr': theme('colors.light.border'),
            '--tw-prose-quotes': theme('colors.light.primary'),
            '--tw-prose-quote-borders': theme('colors.light.border'),
            '--tw-prose-captions': theme('colors.light.secondary'),
            '--tw-prose-code': theme('colors.light.accent'),
            '--tw-prose-pre-code': theme('colors.neutral.200'), // Light theme code block text
            '--tw-prose-pre-bg': theme('colors.light.component'),   // Light theme code block background, matches page bg
            '--tw-prose-th-borders': theme('colors.light.border-strong'),
            '--tw-prose-td-borders': theme('colors.light.border'),
            'code::before': { content: 'none' },
            'code::after': { content: 'none' },
          },
        },
        dark: {
          css: {
            '--tw-prose-body': theme('colors.dark.primary'),
            '--tw-prose-headings': theme('colors.dark.primary'),
            '--tw-prose-lead': theme('colors.dark.secondary'),
            '--tw-prose-links': theme('colors.dark.accent'),
            '--tw-prose-bold': theme('colors.dark.primary'),
            '--tw-prose-counters': theme('colors.dark.secondary'),
            '--tw-prose-bullets': theme('colors.dark.border-strong'),
            '--tw-prose-hr': theme('colors.dark.border'),
            '--tw-prose-quotes': theme('colors.dark.primary'),
            '--tw-prose-quote-borders': theme('colors.dark.border'),
            '--tw-prose-captions': theme('colors.dark.secondary'),
            '--tw-prose-code': theme('colors.dark.accent'), // Inline code
            '--tw-prose-pre-code': theme('colors.neutral.200'),      // Dark theme code block text (often light on dark bg)
            '--tw-prose-pre-bg': theme('colors.neutral.800'), // Dark theme code block background (can be shared or specific)
            '--tw-prose-th-borders': theme('colors.dark.border-strong'),
            '--tw-prose-td-borders': theme('colors.dark.border'),
            'code::before': { content: 'none' },
            'code::after': { content: 'none' },
          },
        },
      }),
    },
  },
  plugins: [
    require('@tailwindcss/typography'),
  ],
}; 