# Create multiple PostgreSQL instances for each AI tool
# Based on the document requirements: augment, roo, cline, cursor, copilot, windsurf

---
# Augment Code PostgreSQL Instance
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: postgresql-augment-pvc
  namespace: llm-eval
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 10Gi
  storageClassName: local-path

---
apiVersion: v1
kind: Secret
metadata:
  name: postgresql-augment-secret
  namespace: llm-eval
type: Opaque
data:
  # postgres / postgres123 / llm_eval_augment (base64 encoded)
  POSTGRES_USER: cG9zdGdyZXM=
  POSTGRES_PASSWORD: cG9zdGdyZXMxMjM=
  POSTGRES_DB: bGxtX2V2YWxfYXVnbWVudA==

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: postgresql-augment
  namespace: llm-eval
  labels:
    app: postgresql-augment
spec:
  replicas: 1
  selector:
    matchLabels:
      app: postgresql-augment
  template:
    metadata:
      labels:
        app: postgresql-augment
    spec:
      containers:
      - name: postgresql
        image: postgres:15-alpine
        ports:
        - containerPort: 5432
        env:
        - name: POSTGRES_USER
          valueFrom:
            secretKeyRef:
              name: postgresql-augment-secret
              key: POSTGRES_USER
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              name: postgresql-augment-secret
              key: POSTGRES_PASSWORD
        - name: POSTGRES_DB
          valueFrom:
            secretKeyRef:
              name: postgresql-augment-secret
              key: POSTGRES_DB
        - name: PGDATA
          value: /var/lib/postgresql/data/pgdata
        volumeMounts:
        - name: postgresql-storage
          mountPath: /var/lib/postgresql/data
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          exec:
            command:
            - pg_isready
            - -U
            - postgres
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          exec:
            command:
            - pg_isready
            - -U
            - postgres
          initialDelaySeconds: 5
          periodSeconds: 5
      volumes:
      - name: postgresql-storage
        persistentVolumeClaim:
          claimName: postgresql-augment-pvc

---
apiVersion: v1
kind: Service
metadata:
  name: postgresql-augment-service
  namespace: llm-eval
  labels:
    app: postgresql-augment
spec:
  selector:
    app: postgresql-augment
  ports:
  - port: 5432
    targetPort: 5432
    protocol: TCP
  type: ClusterIP

---
# Roo Code PostgreSQL Instance
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: postgresql-roo-pvc
  namespace: llm-eval
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 10Gi
  storageClassName: local-path

---
apiVersion: v1
kind: Secret
metadata:
  name: postgresql-roo-secret
  namespace: llm-eval
type: Opaque
data:
  # postgres / postgres123 / llm_eval_roo (base64 encoded)
  POSTGRES_USER: cG9zdGdyZXM=
  POSTGRES_PASSWORD: cG9zdGdyZXMxMjM=
  POSTGRES_DB: bGxtX2V2YWxfcm9v

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: postgresql-roo
  namespace: llm-eval
  labels:
    app: postgresql-roo
spec:
  replicas: 1
  selector:
    matchLabels:
      app: postgresql-roo
  template:
    metadata:
      labels:
        app: postgresql-roo
    spec:
      containers:
      - name: postgresql
        image: postgres:15-alpine
        ports:
        - containerPort: 5432
        env:
        - name: POSTGRES_USER
          valueFrom:
            secretKeyRef:
              name: postgresql-roo-secret
              key: POSTGRES_USER
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              name: postgresql-roo-secret
              key: POSTGRES_PASSWORD
        - name: POSTGRES_DB
          valueFrom:
            secretKeyRef:
              name: postgresql-roo-secret
              key: POSTGRES_DB
        - name: PGDATA
          value: /var/lib/postgresql/data/pgdata
        volumeMounts:
        - name: postgresql-storage
          mountPath: /var/lib/postgresql/data
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          exec:
            command:
            - pg_isready
            - -U
            - postgres
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          exec:
            command:
            - pg_isready
            - -U
            - postgres
          initialDelaySeconds: 5
          periodSeconds: 5
      volumes:
      - name: postgresql-storage
        persistentVolumeClaim:
          claimName: postgresql-roo-pvc

---
apiVersion: v1
kind: Service
metadata:
  name: postgresql-roo-service
  namespace: llm-eval
  labels:
    app: postgresql-roo
spec:
  selector:
    app: postgresql-roo
  ports:
  - port: 5432
    targetPort: 5432
    protocol: TCP
  type: ClusterIP

---
# Cline PostgreSQL Instance
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: postgresql-cline-pvc
  namespace: llm-eval
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 10Gi
  storageClassName: local-path

---
apiVersion: v1
kind: Secret
metadata:
  name: postgresql-cline-secret
  namespace: llm-eval
type: Opaque
data:
  # postgres / postgres123 / llm_eval_cline (base64 encoded)
  POSTGRES_USER: cG9zdGdyZXM=
  POSTGRES_PASSWORD: cG9zdGdyZXMxMjM=
  POSTGRES_DB: bGxtX2V2YWxfY2xpbmU=

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: postgresql-cline
  namespace: llm-eval
  labels:
    app: postgresql-cline
spec:
  replicas: 1
  selector:
    matchLabels:
      app: postgresql-cline
  template:
    metadata:
      labels:
        app: postgresql-cline
    spec:
      containers:
      - name: postgresql
        image: postgres:15-alpine
        ports:
        - containerPort: 5432
        env:
        - name: POSTGRES_USER
          valueFrom:
            secretKeyRef:
              name: postgresql-cline-secret
              key: POSTGRES_USER
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              name: postgresql-cline-secret
              key: POSTGRES_PASSWORD
        - name: POSTGRES_DB
          valueFrom:
            secretKeyRef:
              name: postgresql-cline-secret
              key: POSTGRES_DB
        - name: PGDATA
          value: /var/lib/postgresql/data/pgdata
        volumeMounts:
        - name: postgresql-storage
          mountPath: /var/lib/postgresql/data
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          exec:
            command:
            - pg_isready
            - -U
            - postgres
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          exec:
            command:
            - pg_isready
            - -U
            - postgres
          initialDelaySeconds: 5
          periodSeconds: 5
      volumes:
      - name: postgresql-storage
        persistentVolumeClaim:
          claimName: postgresql-cline-pvc

---
apiVersion: v1
kind: Service
metadata:
  name: postgresql-cline-service
  namespace: llm-eval
  labels:
    app: postgresql-cline
spec:
  selector:
    app: postgresql-cline
  ports:
  - port: 5432
    targetPort: 5432
    protocol: TCP
  type: ClusterIP

---
# Cursor PostgreSQL Instance
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: postgresql-cursor-pvc
  namespace: llm-eval
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 10Gi
  storageClassName: local-path

---
apiVersion: v1
kind: Secret
metadata:
  name: postgresql-cursor-secret
  namespace: llm-eval
type: Opaque
data:
  # postgres / postgres123 / llm_eval_cursor (base64 encoded)
  POSTGRES_USER: cG9zdGdyZXM=
  POSTGRES_PASSWORD: cG9zdGdyZXMxMjM=
  POSTGRES_DB: bGxtX2V2YWxfY3Vyc29y

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: postgresql-cursor
  namespace: llm-eval
  labels:
    app: postgresql-cursor
spec:
  replicas: 1
  selector:
    matchLabels:
      app: postgresql-cursor
  template:
    metadata:
      labels:
        app: postgresql-cursor
    spec:
      containers:
      - name: postgresql
        image: postgres:15-alpine
        ports:
        - containerPort: 5432
        env:
        - name: POSTGRES_USER
          valueFrom:
            secretKeyRef:
              name: postgresql-cursor-secret
              key: POSTGRES_USER
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              name: postgresql-cursor-secret
              key: POSTGRES_PASSWORD
        - name: POSTGRES_DB
          valueFrom:
            secretKeyRef:
              name: postgresql-cursor-secret
              key: POSTGRES_DB
        - name: PGDATA
          value: /var/lib/postgresql/data/pgdata
        volumeMounts:
        - name: postgresql-storage
          mountPath: /var/lib/postgresql/data
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          exec:
            command:
            - pg_isready
            - -U
            - postgres
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          exec:
            command:
            - pg_isready
            - -U
            - postgres
          initialDelaySeconds: 5
          periodSeconds: 5
      volumes:
      - name: postgresql-storage
        persistentVolumeClaim:
          claimName: postgresql-cursor-pvc

---
apiVersion: v1
kind: Service
metadata:
  name: postgresql-cursor-service
  namespace: llm-eval
  labels:
    app: postgresql-cursor
spec:
  selector:
    app: postgresql-cursor
  ports:
  - port: 5432
    targetPort: 5432
    protocol: TCP
  type: ClusterIP

---
# GitHub Copilot PostgreSQL Instance
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: postgresql-copilot-pvc
  namespace: llm-eval
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 10Gi
  storageClassName: local-path

---
apiVersion: v1
kind: Secret
metadata:
  name: postgresql-copilot-secret
  namespace: llm-eval
type: Opaque
data:
  # postgres / postgres123 / llm_eval_copilot (base64 encoded)
  POSTGRES_USER: cG9zdGdyZXM=
  POSTGRES_PASSWORD: cG9zdGdyZXMxMjM=
  POSTGRES_DB: bGxtX2V2YWxfY29waWxvdA==

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: postgresql-copilot
  namespace: llm-eval
  labels:
    app: postgresql-copilot
spec:
  replicas: 1
  selector:
    matchLabels:
      app: postgresql-copilot
  template:
    metadata:
      labels:
        app: postgresql-copilot
    spec:
      containers:
      - name: postgresql
        image: postgres:15-alpine
        ports:
        - containerPort: 5432
        env:
        - name: POSTGRES_USER
          valueFrom:
            secretKeyRef:
              name: postgresql-copilot-secret
              key: POSTGRES_USER
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              name: postgresql-copilot-secret
              key: POSTGRES_PASSWORD
        - name: POSTGRES_DB
          valueFrom:
            secretKeyRef:
              name: postgresql-copilot-secret
              key: POSTGRES_DB
        - name: PGDATA
          value: /var/lib/postgresql/data/pgdata
        volumeMounts:
        - name: postgresql-storage
          mountPath: /var/lib/postgresql/data
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          exec:
            command:
            - pg_isready
            - -U
            - postgres
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          exec:
            command:
            - pg_isready
            - -U
            - postgres
          initialDelaySeconds: 5
          periodSeconds: 5
      volumes:
      - name: postgresql-storage
        persistentVolumeClaim:
          claimName: postgresql-copilot-pvc

---
apiVersion: v1
kind: Service
metadata:
  name: postgresql-copilot-service
  namespace: llm-eval
  labels:
    app: postgresql-copilot
spec:
  selector:
    app: postgresql-copilot
  ports:
  - port: 5432
    targetPort: 5432
    protocol: TCP
  type: ClusterIP

---
# Windsurf PostgreSQL Instance
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: postgresql-windsurf-pvc
  namespace: llm-eval
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 10Gi
  storageClassName: local-path

---
apiVersion: v1
kind: Secret
metadata:
  name: postgresql-windsurf-secret
  namespace: llm-eval
type: Opaque
data:
  # postgres / postgres123 / llm_eval_windsurf (base64 encoded)
  POSTGRES_USER: cG9zdGdyZXM=
  POSTGRES_PASSWORD: cG9zdGdyZXMxMjM=
  POSTGRES_DB: bGxtX2V2YWxfd2luZHN1cmY=

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: postgresql-windsurf
  namespace: llm-eval
  labels:
    app: postgresql-windsurf
spec:
  replicas: 1
  selector:
    matchLabels:
      app: postgresql-windsurf
  template:
    metadata:
      labels:
        app: postgresql-windsurf
    spec:
      containers:
      - name: postgresql
        image: postgres:15-alpine
        ports:
        - containerPort: 5432
        env:
        - name: POSTGRES_USER
          valueFrom:
            secretKeyRef:
              name: postgresql-windsurf-secret
              key: POSTGRES_USER
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              name: postgresql-windsurf-secret
              key: POSTGRES_PASSWORD
        - name: POSTGRES_DB
          valueFrom:
            secretKeyRef:
              name: postgresql-windsurf-secret
              key: POSTGRES_DB
        - name: PGDATA
          value: /var/lib/postgresql/data/pgdata
        volumeMounts:
        - name: postgresql-storage
          mountPath: /var/lib/postgresql/data
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          exec:
            command:
            - pg_isready
            - -U
            - postgres
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          exec:
            command:
            - pg_isready
            - -U
            - postgres
          initialDelaySeconds: 5
          periodSeconds: 5
      volumes:
      - name: postgresql-storage
        persistentVolumeClaim:
          claimName: postgresql-windsurf-pvc

---
apiVersion: v1
kind: Service
metadata:
  name: postgresql-windsurf-service
  namespace: llm-eval
  labels:
    app: postgresql-windsurf
spec:
  selector:
    app: postgresql-windsurf
  ports:
  - port: 5432
    targetPort: 5432
    protocol: TCP
  type: ClusterIP

---
# Cursor Max PostgreSQL Instance
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: postgresql-cursor-max-pvc
  namespace: llm-eval
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 10Gi
  storageClassName: local-path

---
apiVersion: v1
kind: Secret
metadata:
  name: postgresql-cursor-max-secret
  namespace: llm-eval
type: Opaque
data:
  # postgres / postgres123 / llm_eval_cursor_max (base64 encoded)
  POSTGRES_USER: cG9zdGdyZXM=
  POSTGRES_PASSWORD: cG9zdGdyZXMxMjM=
  POSTGRES_DB: bGxtX2V2YWxfY3Vyc29yX21heA==

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: postgresql-cursor-max
  namespace: llm-eval
  labels:
    app: postgresql-cursor-max
spec:
  replicas: 1
  selector:
    matchLabels:
      app: postgresql-cursor-max
  template:
    metadata:
      labels:
        app: postgresql-cursor-max
    spec:
      containers:
      - name: postgresql
        image: postgres:15-alpine
        ports:
        - containerPort: 5432
        env:
        - name: POSTGRES_USER
          valueFrom:
            secretKeyRef:
              name: postgresql-cursor-max-secret
              key: POSTGRES_USER
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              name: postgresql-cursor-max-secret
              key: POSTGRES_PASSWORD
        - name: POSTGRES_DB
          valueFrom:
            secretKeyRef:
              name: postgresql-cursor-max-secret
              key: POSTGRES_DB
        - name: PGDATA
          value: /var/lib/postgresql/data/pgdata
        volumeMounts:
        - name: postgresql-storage
          mountPath: /var/lib/postgresql/data
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          exec:
            command:
            - pg_isready
            - -U
            - postgres
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          exec:
            command:
            - pg_isready
            - -U
            - postgres
          initialDelaySeconds: 5
          periodSeconds: 5
      volumes:
      - name: postgresql-storage
        persistentVolumeClaim:
          claimName: postgresql-cursor-max-pvc

---
apiVersion: v1
kind: Service
metadata:
  name: postgresql-cursor-max-service
  namespace: llm-eval
  labels:
    app: postgresql-cursor-max
spec:
  selector:
    app: postgresql-cursor-max
  ports:
  - port: 5432
    targetPort: 5432
    protocol: TCP
  type: ClusterIP

---
# Cursor Gemini PostgreSQL Instance
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: postgresql-cursor-gemini-pvc
  namespace: llm-eval
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 10Gi
  storageClassName: local-path

---
apiVersion: v1
kind: Secret
metadata:
  name: postgresql-cursor-gemini-secret
  namespace: llm-eval
type: Opaque
data:
  # postgres / postgres123 / llm_eval_cursor_gemini (base64 encoded)
  POSTGRES_USER: cG9zdGdyZXM=
  POSTGRES_PASSWORD: cG9zdGdyZXMxMjM=
  POSTGRES_DB: bGxtX2V2YWxfY3Vyc29yX2dlbWluaQ==

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: postgresql-cursor-gemini
  namespace: llm-eval
  labels:
    app: postgresql-cursor-gemini
spec:
  replicas: 1
  selector:
    matchLabels:
      app: postgresql-cursor-gemini
  template:
    metadata:
      labels:
        app: postgresql-cursor-gemini
    spec:
      containers:
      - name: postgresql
        image: postgres:15-alpine
        ports:
        - containerPort: 5432
        env:
        - name: POSTGRES_USER
          valueFrom:
            secretKeyRef:
              name: postgresql-cursor-gemini-secret
              key: POSTGRES_USER
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              name: postgresql-cursor-gemini-secret
              key: POSTGRES_PASSWORD
        - name: POSTGRES_DB
          valueFrom:
            secretKeyRef:
              name: postgresql-cursor-gemini-secret
              key: POSTGRES_DB
        - name: PGDATA
          value: /var/lib/postgresql/data/pgdata
        volumeMounts:
        - name: postgresql-storage
          mountPath: /var/lib/postgresql/data
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          exec:
            command:
            - pg_isready
            - -U
            - postgres
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          exec:
            command:
            - pg_isready
            - -U
            - postgres
          initialDelaySeconds: 5
          periodSeconds: 5
      volumes:
      - name: postgresql-storage
        persistentVolumeClaim:
          claimName: postgresql-cursor-gemini-pvc

---
apiVersion: v1
kind: Service
metadata:
  name: postgresql-cursor-gemini-service
  namespace: llm-eval
  labels:
    app: postgresql-cursor-gemini
spec:
  selector:
    app: postgresql-cursor-gemini
  ports:
  - port: 5432
    targetPort: 5432
    protocol: TCP
  type: ClusterIP

---
# Cursor Gemini Max PostgreSQL Instance
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: postgresql-cursor-gemini-max-pvc
  namespace: llm-eval
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 10Gi
  storageClassName: local-path

---
apiVersion: v1
kind: Secret
metadata:
  name: postgresql-cursor-gemini-max-secret
  namespace: llm-eval
type: Opaque
data:
  # postgres / postgres123 / llm_eval_cursor_gemini_max (base64 encoded)
  POSTGRES_USER: cG9zdGdyZXM=
  POSTGRES_PASSWORD: cG9zdGdyZXMxMjM=
  POSTGRES_DB: bGxtX2V2YWxfY3Vyc29yX2dlbWluaV9tYXg=

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: postgresql-cursor-gemini-max
  namespace: llm-eval
  labels:
    app: postgresql-cursor-gemini-max
spec:
  replicas: 1
  selector:
    matchLabels:
      app: postgresql-cursor-gemini-max
  template:
    metadata:
      labels:
        app: postgresql-cursor-gemini-max
    spec:
      containers:
      - name: postgresql
        image: postgres:15-alpine
        ports:
        - containerPort: 5432
        env:
        - name: POSTGRES_USER
          valueFrom:
            secretKeyRef:
              name: postgresql-cursor-gemini-max-secret
              key: POSTGRES_USER
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              name: postgresql-cursor-gemini-max-secret
              key: POSTGRES_PASSWORD
        - name: POSTGRES_DB
          valueFrom:
            secretKeyRef:
              name: postgresql-cursor-gemini-max-secret
              key: POSTGRES_DB
        - name: PGDATA
          value: /var/lib/postgresql/data/pgdata
        volumeMounts:
        - name: postgresql-storage
          mountPath: /var/lib/postgresql/data
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          exec:
            command:
            - pg_isready
            - -U
            - postgres
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          exec:
            command:
            - pg_isready
            - -U
            - postgres
          initialDelaySeconds: 5
          periodSeconds: 5
      volumes:
      - name: postgresql-storage
        persistentVolumeClaim:
          claimName: postgresql-cursor-gemini-max-pvc

---
apiVersion: v1
kind: Service
metadata:
  name: postgresql-cursor-gemini-max-service
  namespace: llm-eval
  labels:
    app: postgresql-cursor-gemini-max
spec:
  selector:
    app: postgresql-cursor-gemini-max
  ports:
  - port: 5432
    targetPort: 5432
    protocol: TCP
  type: ClusterIP

---
# Codex PostgreSQL Instance
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: postgresql-codex-pvc
  namespace: llm-eval
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 10Gi
  storageClassName: local-path

---
apiVersion: v1
kind: Secret
metadata:
  name: postgresql-codex-secret
  namespace: llm-eval
type: Opaque
data:
  # postgres / postgres123 / llm_eval_codex (base64 encoded)
  POSTGRES_USER: cG9zdGdyZXM=
  POSTGRES_PASSWORD: cG9zdGdyZXMxMjM=
  POSTGRES_DB: bGxtX2V2YWxfY29kZXg=

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: postgresql-codex
  namespace: llm-eval
  labels:
    app: postgresql-codex
spec:
  replicas: 1
  selector:
    matchLabels:
      app: postgresql-codex
  template:
    metadata:
      labels:
        app: postgresql-codex
    spec:
      containers:
      - name: postgresql
        image: postgres:15-alpine
        ports:
        - containerPort: 5432
        env:
        - name: POSTGRES_USER
          valueFrom:
            secretKeyRef:
              name: postgresql-codex-secret
              key: POSTGRES_USER
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              name: postgresql-codex-secret
              key: POSTGRES_PASSWORD
        - name: POSTGRES_DB
          valueFrom:
            secretKeyRef:
              name: postgresql-codex-secret
              key: POSTGRES_DB
        - name: PGDATA
          value: /var/lib/postgresql/data/pgdata
        volumeMounts:
        - name: postgresql-storage
          mountPath: /var/lib/postgresql/data
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          exec:
            command:
            - pg_isready
            - -U
            - postgres
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          exec:
            command:
            - pg_isready
            - -U
            - postgres
          initialDelaySeconds: 5
          periodSeconds: 5
      volumes:
      - name: postgresql-storage
        persistentVolumeClaim:
          claimName: postgresql-codex-pvc

---
apiVersion: v1
kind: Service
metadata:
  name: postgresql-codex-service
  namespace: llm-eval
  labels:
    app: postgresql-codex
spec:
  selector:
    app: postgresql-codex
  ports:
  - port: 5432
    targetPort: 5432
    protocol: TCP
  type: ClusterIP

---
# Claudecode PostgreSQL Instance
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: postgresql-claudecode-pvc
  namespace: llm-eval
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 10Gi
  storageClassName: local-path

---
apiVersion: v1
kind: Secret
metadata:
  name: postgresql-claudecode-secret
  namespace: llm-eval
type: Opaque
data:
  # postgres / postgres123 / llm_eval_claudecode (base64 encoded)
  POSTGRES_USER: cG9zdGdyZXM=
  POSTGRES_PASSWORD: cG9zdGdyZXMxMjM=
  POSTGRES_DB: bGxtX2V2YWxfY2xhdWRlY29kZQ==

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: postgresql-claudecode
  namespace: llm-eval
  labels:
    app: postgresql-claudecode
spec:
  replicas: 1
  selector:
    matchLabels:
      app: postgresql-claudecode
  template:
    metadata:
      labels:
        app: postgresql-claudecode
    spec:
      containers:
      - name: postgresql
        image: postgres:15-alpine
        ports:
        - containerPort: 5432
        env:
        - name: POSTGRES_USER
          valueFrom:
            secretKeyRef:
              name: postgresql-claudecode-secret
              key: POSTGRES_USER
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              name: postgresql-claudecode-secret
              key: POSTGRES_PASSWORD
        - name: POSTGRES_DB
          valueFrom:
            secretKeyRef:
              name: postgresql-claudecode-secret
              key: POSTGRES_DB
        - name: PGDATA
          value: /var/lib/postgresql/data/pgdata
        volumeMounts:
        - name: postgresql-storage
          mountPath: /var/lib/postgresql/data
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          exec:
            command:
            - pg_isready
            - -U
            - postgres
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          exec:
            command:
            - pg_isready
            - -U
            - postgres
          initialDelaySeconds: 5
          periodSeconds: 5
      volumes:
      - name: postgresql-storage
        persistentVolumeClaim:
          claimName: postgresql-claudecode-pvc

---
apiVersion: v1
kind: Service
metadata:
  name: postgresql-claudecode-service
  namespace: llm-eval
  labels:
    app: postgresql-claudecode
spec:
  selector:
    app: postgresql-claudecode
  ports:
  - port: 5432
    targetPort: 5432
    protocol: TCP
  type: ClusterIP 