#!/bin/bash

# Setup new cursor variant database instances
# For AI tool testing environment - only new instances
# Note: Port forwarding should be started separately

set -e

# Define new AI tools that need setup
NEW_AI_TOOLS=("codex" "claudecode")

# Baseline version (from documentation)
BASELINE_VERSION="28b5937d928a"

# Namespace
NAMESPACE="llm-eval"

# Port mapping
declare -A AI_TOOLS_PORTS=(
    ["cursor-max"]="5439"
    ["cursor-gemini"]="5440"
    ["cursor-gemini-max"]="5441"
    ["codex"]="5442"
    ["claudecode"]="5443"
)

echo "🚀 Starting new database instances setup..."
echo "📊 New AI Tools: ${NEW_AI_TOOLS[*]}"
echo "📍 Target baseline version: $BASELINE_VERSION"
echo ""

# Check if port forwarding is running
echo "🔍 Checking port forwarding status..."
if pgrep -f "kubectl.*port-forward.*postgresql-.*-service" > /dev/null; then
    echo "  ✅ Port forwarding is already running"
else
    echo "  ⚠️  Port forwarding not detected. Please run: ./scripts/start-port-forwarding.sh"
    exit 1
fi

echo ""

# Setup database instances
for tool in "${NEW_AI_TOOLS[@]}"; do
    echo "🔧 Processing $tool database..."
    
    # Build database connection URL - Use sync format for Alembic
    DB_URL="postgresql://postgres:postgres123@postgresql-${tool}-service:5432/llm_eval_${tool//-/_}"
    
    echo "  📡 Testing connection: postgresql-${tool}-service"
    
    # Test database connection
    kubectl run psql-test-${tool} --rm -it --image=postgres:15-alpine --restart=Never \
        --namespace=$NAMESPACE \
        -- psql "$DB_URL" -c "SELECT 1;" >/dev/null 2>&1 || {
        echo "  ❌ $tool database connection failed"
        exit 1
    }
    
    echo "  ✅ Connection successful"
    
    # Run full migration to latest version (establish complete schema first)
    echo "  📤 Running migration to latest version..."
    kubectl exec -n $NAMESPACE deployment/backend -- \
        bash -c "cd /app && POSTGRES_DB=temp DATABASE_URL='$DB_URL' alembic upgrade head"
    
    # Check current version
    CURRENT_VERSION=$(kubectl exec -n $NAMESPACE deployment/backend -- \
        bash -c "cd /app && POSTGRES_DB=temp DATABASE_URL='$DB_URL' alembic current" | grep -E '^[a-f0-9]+' | head -n1)
    
    echo "  📋 Current version: $CURRENT_VERSION"
    
    # Downgrade to baseline version
    echo "  📉 Downgrading to baseline version: $BASELINE_VERSION"
    kubectl exec -n $NAMESPACE deployment/backend -- \
        bash -c "cd /app && POSTGRES_DB=temp DATABASE_URL='$DB_URL' alembic downgrade $BASELINE_VERSION"
    
    # Verify downgrade version
    FINAL_VERSION=$(kubectl exec -n $NAMESPACE deployment/backend -- \
        bash -c "cd /app && POSTGRES_DB=temp DATABASE_URL='$DB_URL' alembic current" | grep -E '^[a-f0-9]+' | head -n1)
    
    echo "  ✅ $tool database setup completed (version: $FINAL_VERSION)"
    echo ""
done

echo "🎉 New database instances setup completed!"
echo ""
echo "📝 New database connection information:"
for tool in "${NEW_AI_TOOLS[@]}"; do
    PORT=${AI_TOOLS_PORTS[$tool]}
    if [ -n "$PORT" ]; then
        echo "  $tool: localhost:$PORT -> postgresql://postgres:postgres123@postgresql-${tool}-service:5432/llm_eval_${tool//-/_}"
    else
        echo "  $tool: Port not defined in script. Connection URL: postgresql://postgres:postgres123@postgresql-${tool}-service:5432/llm_eval_${tool//-/_}"
    fi
done 