# Scripts Directory

This directory contains utility scripts for deployment, testing, database management, and development workflows.

## Script Categories

### 🚀 Deployment Scripts

#### `deploy-local.sh`
**Purpose**: Deploy the LLM Evaluation Platform to local k3s cluster

**Usage**:
```bash
./scripts/deploy-local.sh
```

**Features**:
- Checks prerequisites (kubectl, docker, k3s)
- Builds Docker images for backend and frontend
- Deploys PostgreSQL, backend, and frontend services
- Waits for services to be ready
- Displays access URLs and useful kubectl commands

#### `cleanup.sh`
**Purpose**: Clean up k3s deployment and optionally remove Docker images

**Usage**:
```bash
./scripts/cleanup.sh
```

**Features**:
- Shows current resources before deletion
- Confirms before deleting namespace
- Optionally removes local Docker images
- Color-coded output for clarity

### 🧪 Testing Scripts

#### `run-comprehensive-tests.py`
**Purpose**: Run the complete test suite (386 tests)

**Usage**:
```bash
./scripts/run-comprehensive-tests.py
# or
python3 scripts/run-comprehensive-tests.py
```

**Coverage**:
- Frontend: 342 tests (components, stores, API client, integration)
- Backend: 44 tests (API endpoints, CRUD operations)
- Generates coverage reports for both frontend and backend

#### `run-usage-stats-tests.sh`
**Purpose**: Run focused tests for usage statistics functionality

**Usage**:
```bash
./scripts/run-usage-stats-tests.sh
```

**Coverage**:
- Usage statistics components (UsageStatsCard, CombinedUsageSummary)
- Usage statistics API client methods
- Usage statistics store functionality
- Backend CRUD and API tests for usage stats

### 🗄️ Database Management Scripts

#### `setup-multi-databases.sh`
**Purpose**: Set up all database instances for AI tool testing

**Usage**:
```bash
./scripts/setup-multi-databases.sh
```

**Databases Created**:
- augment (port 5433)
- roo (port 5434)
- cline (port 5435)
- cursor (port 5436)
- copilot (port 5437)
- windsurf (port 5438)
- cursor-max (port 5439)
- cursor-gemini (port 5440)
- cursor-gemini-max (port 5441)
- codex (port 5442)
- claudecode (port 5443)

**Features**:
- Runs Alembic migrations to latest version
- Downgrades to baseline version (28b5937d928a)
- Tests connections before migrations

#### `setup-new-databases.sh`
**Purpose**: Set up only new database instances (codex and claudecode)

**Usage**:
```bash
./scripts/setup-new-databases.sh
```

**Prerequisites**: Port forwarding must be running

#### `start-port-forwarding.sh`
**Purpose**: Start port forwarding for all PostgreSQL instances

**Usage**:
```bash
./scripts/start-port-forwarding.sh
```

**Features**:
- Forwards ports 5433-5443 to k8s PostgreSQL services
- Kills existing port forwards before starting
- Runs in background for persistent access
- Displays connection strings for all databases

### 🐳 Docker Registry Scripts

#### `manage-local-registry.sh`
**Purpose**: Manage local Docker registry for development

**Usage**:
```bash
./scripts/manage-local-registry.sh [command]

Commands:
  start    - Start the registry
  stop     - Stop the registry
  restart  - Restart the registry
  status   - Show registry status
  list     - List images in registry
  clean    - Remove registry and data
  help     - Show help message
```

**Features**:
- Runs registry on port 5000
- Persistent volume for registry data
- API connectivity testing
- Image listing and cleanup

#### `test-local-registry.sh`
**Purpose**: Test local Docker registry functionality

**Usage**:
```bash
./scripts/test-local-registry.sh
```

**Tests**:
- Registry API accessibility
- Image build, push, and pull
- Container run from pulled image
- Cleanup of test artifacts

### 🔧 Configuration Scripts

#### `configure-ai-tool-branch.sh`
**Purpose**: Configure branch for specific AI tool testing

**Usage**:
```bash
./scripts/configure-ai-tool-branch.sh <tool-name>

# Example:
./scripts/configure-ai-tool-branch.sh augment
```

**Supported Tools**: augment, roo, cline, cursor, copilot, windsurf

**Creates**:
- Tool-specific Kubernetes configuration
- Customized CI/CD workflow
- Branch-specific README
- Results recording template

## Common Workflows

### 1. Full Local Deployment
```bash
# Deploy everything
./scripts/deploy-local.sh

# Start port forwarding for database access
./scripts/start-port-forwarding.sh

# Set up all databases
./scripts/setup-multi-databases.sh
```

### 2. Running Tests Before Commit
```bash
# Run all tests
./scripts/run-comprehensive-tests.py

# Or run specific test suite
./scripts/run-usage-stats-tests.sh
```

### 3. Cleanup After Development
```bash
# Stop port forwarding (Ctrl+C in terminal)

# Clean up k3s deployment
./scripts/cleanup.sh
```

### 4. AI Tool Branch Setup
```bash
# Create branch for testing specific tool
git checkout -b test-augment

# Configure branch
./scripts/configure-ai-tool-branch.sh augment

# Follow instructions in generated README
```

## Prerequisites

Most scripts require:
- Docker installed and running
- k3s installed (for deployment scripts)
- kubectl configured
- Python 3.x (for Python scripts)
- Node.js and npm (for frontend tests)
- Poetry (for backend tests)

## Script Maintenance

When adding new scripts:
1. Use consistent naming (kebab-case)
2. Add proper headers with description
3. Include usage instructions
4. Use color coding for output
5. Add error handling
6. Update this README

## Related Documentation

- [Comprehensive Test Suite](../docs/testing/comprehensive-test-suite.md)
- [CI/CD Documentation](../docs/cicd/README.md)
- [Main Project README](../README.md)