#!/usr/bin/env python3
"""
Comprehensive Test Suite Runner Script
This script runs all tests for the LLM Evaluation Platform
"""

import subprocess
import sys
import os
import shutil
from pathlib import Path
from typing import List, Tuple
import json
import re
import argparse
import warnings

# Colors for output
class Colors:
    RED = '\033[0;31m'
    GREEN = '\033[0;32m'
    YELLOW = '\033[1;33m'
    BLUE = '\033[0;34m'
    PURPLE = '\033[0;35m'
    CYAN = '\033[0;36m'
    NC = '\033[0m'  # No Color

def print_colored(text: str, color: str) -> None:
    """Print text with color"""
    print(f"{color}{text}{Colors.NC}")

def print_section(title: str) -> None:
    """Print section header"""
    print_colored(f"📋 {title}", Colors.YELLOW)
    print_colored("=" * 60, Colors.YELLOW)

def print_subsection(title: str) -> None:
    """Print subsection header"""
    print_colored(f"🔬 {title}", Colors.CYAN)
    print_colored("-" * 40, Colors.CYAN)

def command_exists(command: str) -> bool:
    """Check if command exists"""
    return shutil.which(command) is not None

def run_test(test_name: str, test_command: List[str], cwd: str = None) -> bool:
    """
    Run a test command and return True if successful, False otherwise
    """
    print_colored(f"Running: {test_name}", Colors.BLUE)
    
    try:
        result = subprocess.run(
            test_command,
            cwd=cwd,
            capture_output=False,  # Let output go to terminal
            text=True,
            timeout=300  # 5 minute timeout per test
        )
        
        if result.returncode == 0:
            print_colored(f"✅ {test_name} - PASSED", Colors.GREEN)
            return True
        else:
            print_colored(f"❌ {test_name} - FAILED (Exit Code: {result.returncode})", Colors.RED)
            return False
            
    except subprocess.TimeoutExpired:
        print_colored(f"❌ {test_name} - TIMEOUT (5 minutes)", Colors.RED)
        return False
    except Exception as e:
        print_colored(f"❌ {test_name} - ERROR: {str(e)}", Colors.RED)
        return False

def main():
    """Main test runner function"""
    
    # -----------------------
    # CLI argument parsing
    # -----------------------
    parser = argparse.ArgumentParser(
        description="Run the full frontend & backend test suites (with coverage)."
    )
    parser.add_argument(
        "--show-warnings",
        action="store_true",
        help="Display warning messages emitted by Python/Node during test execution."
    )
    args = parser.parse_args()

    # Suppress warnings unless user explicitly asks for them
    if not args.show_warnings:
        warnings.filterwarnings("ignore")
        # Propagate to child processes
        os.environ.setdefault("PYTHONWARNINGS", "ignore")
        os.environ.setdefault("NODE_OPTIONS", "--no-warnings")
    
    # Initialize counters
    total_tests = 0
    passed_tests = 0
    failed_tests = 0
    
    print_colored("🧪 LLM Evaluation Platform - Comprehensive Test Suite", Colors.BLUE)
    print_colored("=" * 51, Colors.BLUE)
    print()
    
    # Check prerequisites
    print_section("Checking Prerequisites")
    
    if not command_exists("npm"):
        print_colored("❌ npm is not installed", Colors.RED)
        sys.exit(1)
    
    if not command_exists("python"):
        print_colored("❌ Python is not installed", Colors.RED)
        sys.exit(1)
    
    print_colored("✅ Prerequisites check passed", Colors.GREEN)
    print()
    
    # Get project root directory
    script_dir = Path(__file__).parent
    project_root = script_dir.parent
    frontend_dir = project_root / "frontend"
    backend_dir = project_root / "backend"
    
    # Frontend Tests
    print_section("Frontend Test Suite")
    
    os.chdir(frontend_dir)
    
    print("Installing frontend dependencies...")
    subprocess.run(["npm", "install"], check=False)
    print()
    
    # Component Tests
    print_subsection("Component Tests")
    
    # Core UI Components
    frontend_tests = [
        ("LoadingSpinner Component", ["npm", "test", "--", "src/test/components/LoadingSpinner.test.tsx", "--run"]),
        ("ThemeToggle Component", ["npm", "test", "--", "src/test/components/ThemeToggle.test.tsx", "--run"]),
        ("SystemStatus Component", ["npm", "test", "--", "src/test/components/SystemStatus.test.tsx", "--run"]),
        
        # Form Components
        ("ModelSelector Component", ["npm", "test", "--", "src/test/components/ModelSelector.test.tsx", "--run"]),
        ("PromptInput Component", ["npm", "test", "--", "src/test/components/PromptInput.test.tsx", "--run"]),
        ("EvaluateArea Component", ["npm", "test", "--", "src/test/components/EvaluateArea.test.tsx", "--run"]),
        
        # Display Components
        ("OutputDisplay Component", ["npm", "test", "--", "src/test/components/OutputDisplay.test.tsx", "--run"]),
        ("ReportViewer Component", ["npm", "test", "--", "src/test/components/ReportViewer.test.tsx", "--run"]),
        
        # Navigation Components
        ("EvaluationSidebar Component", ["npm", "test", "--", "src/test/components/EvaluationSidebar.test.tsx", "--run"]),
        ("DeleteConfirmModal Component", ["npm", "test", "--", "src/test/components/DeleteConfirmModal.test.tsx", "--run"]),
    ]
    
    for test_name, test_command in frontend_tests:
        total_tests += 1
        if run_test(test_name, test_command, str(frontend_dir)):
            passed_tests += 1
        else:
            failed_tests += 1
    
    # Usage Statistics Components
    print_subsection("Usage Statistics Components")
    
    usage_stats_tests = [
        ("UsageStatsCard Component", ["npm", "test", "--", "src/test/components/UsageStatsCard.test.tsx", "--run"]),
        ("CombinedUsageSummary Component", ["npm", "test", "--", "src/test/components/CombinedUsageSummary.test.tsx", "--run"]),
    ]
    
    for test_name, test_command in usage_stats_tests:
        total_tests += 1
        if run_test(test_name, test_command, str(frontend_dir)):
            passed_tests += 1
        else:
            failed_tests += 1
    
    # API Tests
    print_subsection("API Client Tests")
    
    api_tests = [
        ("Usage Stats API Client", ["npm", "test", "--", "src/test/api/apiClient.usageStats.test.ts", "--run"]),
    ]
    
    for test_name, test_command in api_tests:
        total_tests += 1
        if run_test(test_name, test_command, str(frontend_dir)):
            passed_tests += 1
        else:
            failed_tests += 1
    
    # Store Tests
    print_subsection("State Management Tests")
    
    store_tests = [
        ("Health Store", ["npm", "test", "--", "src/test/store/healthStore.test.ts", "--run"]),
        ("Task Store (Usage Stats)", ["npm", "test", "--", "src/test/store/taskStore.usageStats.test.ts", "--run"]),
    ]
    
    for test_name, test_command in store_tests:
        total_tests += 1
        if run_test(test_name, test_command, str(frontend_dir)):
            passed_tests += 1
        else:
            failed_tests += 1
    
    # Integration Tests
    print_subsection("Integration Tests")
    
    integration_tests = [
        ("Model Selection Integration", ["npm", "test", "--", "src/test/integration/modelSelection.integration.test.tsx", "--run"]),
        ("Usage Stats Integration", ["npm", "test", "--", "src/test/integration/usageStats.integration.test.tsx", "--run"]),
    ]
    
    for test_name, test_command in integration_tests:
        total_tests += 1
        if run_test(test_name, test_command, str(frontend_dir)):
            passed_tests += 1
        else:
            failed_tests += 1
    
    # Coverage Report
    print()
    print_subsection("Test Coverage Analysis")
    
    print_colored("📊 Generating comprehensive coverage report...", Colors.BLUE)
    subprocess.run(["npm", "run", "test:coverage", "--", "--run"], cwd=frontend_dir, check=False)
    
    print_colored("✅ Frontend test suite completed", Colors.GREEN)
    
    # Backend Tests
    os.chdir(backend_dir)
    
    print_section("Backend Test Suite")
    
    print("Installing backend dependencies...")
    subprocess.run(["poetry", "install"], check=False)
    print()
    
    # API Tests
    print_subsection("API Endpoint Tests")
    
    backend_api_tests = [
        ("Health API", ["poetry", "run", "pytest", "tests/test_api_health.py", "-v"]),
        ("Usage Stats API", ["poetry", "run", "pytest", "tests/test_api_usage_stats.py", "-v"]),
    ]
    
    for test_name, test_command in backend_api_tests:
        total_tests += 1
        if run_test(test_name, test_command, str(backend_dir)):
            passed_tests += 1
        else:
            failed_tests += 1
    
    # CRUD Tests
    print_subsection("Database CRUD Tests")
    
    backend_crud_tests = [
        ("Task CRUD Operations", ["poetry", "run", "pytest", "tests/test_crud_task.py", "-v"]),
        ("Generation CRUD Operations", ["poetry", "run", "pytest", "tests/test_crud_generation.py", "-v"]),
        ("Usage Stats CRUD Operations", ["poetry", "run", "pytest", "tests/test_crud_usage_stats.py", "-v"]),
    ]
    
    for test_name, test_command in backend_crud_tests:
        total_tests += 1
        if run_test(test_name, test_command, str(backend_dir)):
            passed_tests += 1
        else:
            failed_tests += 1
    
    # Backend Coverage Report
    print()
    print_subsection("Backend Coverage Analysis")
    
    print_colored("📊 Generating backend coverage report...", Colors.BLUE)
    subprocess.run([
        "poetry", "run", "pytest", 
        "--cov=app", "--cov-report=html", "--cov-report=term", 
        "tests/", "-v"
    ], cwd=backend_dir, check=False)
    
    print_colored("✅ Backend test suite completed", Colors.GREEN)
    
    # ------------------------------------------------------------------
    # Helper functions to extract coverage percentages for summary output
    # ------------------------------------------------------------------
    def get_frontend_coverage(frontend_root: Path) -> float | None:
        """Search for coverage report files and extract line coverage percentage.
        
        First checks for coverage-summary.json, then falls back to parsing HTML report.
        """
        # First try JSON summary files
        candidate_names = ["coverage-summary.json", "coverage-final.json"]
        
        for name in candidate_names:
            for summary_file in frontend_root.rglob(name):
                try:
                    with open(summary_file, "r", encoding="utf-8") as sf:
                        data = json.load(sf)
                        
                        # coverage-summary.json format: { total: { lines: { pct: 53.27 } } }
                        if "total" in data:
                            total = data["total"].get("lines", {})
                            pct = total.get("pct")
                            if pct is not None:
                                return float(pct)
                except Exception:
                    continue
        
        # Fall back to parsing HTML report
        html_file = frontend_root / "coverage" / "index.html"
        if html_file.exists():
            try:
                with open(html_file, "r", encoding="utf-8") as hf:
                    content = hf.read()
                    # Look for the first percentage value which is the overall coverage
                    match = re.search(r'<span class="strong">(\d+\.?\d*)%\s*</span>', content)
                    if match:
                        return float(match.group(1))
            except Exception:
                pass
                
        return None

    def get_backend_coverage(htmlcov_dir: Path) -> float | None:
        """Return backend total line coverage percentage if available by parsing html index."""
        index_file = htmlcov_dir / "index.html"
        if index_file.exists():
            try:
                with open(index_file, "r", encoding="utf-8") as idx:
                    content = idx.read()
                    # Find first percentage value like 92%
                    match = re.search(r"(\d+)%", content)
                    if match:
                        return float(match.group(1))
            except Exception:
                return None
        return None
    
    # Summary
    os.chdir(project_root)
    
    print_section("Test Execution Summary")
    
    print_colored("📊 Test Results:", Colors.PURPLE)
    print(f"  Total Test Suites: {total_tests}")
    print_colored(f"  Passed: {passed_tests}", Colors.GREEN)
    print_colored(f"  Failed: {failed_tests}", Colors.RED)
    
    if failed_tests == 0:
        print_colored("🎉 All test suites passed successfully!", Colors.GREEN)
        exit_code = 0
    else:
        print_colored("⚠️  Some test suites failed. Please review the output above.", Colors.RED)
        exit_code = 1
    
    print()
    print_colored("📁 Coverage Reports Generated:", Colors.BLUE)
    print("  Frontend: frontend/coverage/index.html")
    print("  Backend: backend/htmlcov/index.html")
    
    print()
    print_colored("🔍 Test Files Covered:", Colors.BLUE)
    print("  Frontend Components:")
    print("    ├── LoadingSpinner, ThemeToggle, SystemStatus")
    print("    ├── ModelSelector, PromptInput, EvaluateArea")
    print("    ├── OutputDisplay, ReportViewer")
    print("    ├── EvaluationSidebar, DeleteConfirmModal")
    print("    └── UsageStatsCard, CombinedUsageSummary")
    print()
    print("  Frontend Infrastructure:")
    print("    ├── API Client Tests")
    print("    ├── Store Tests (Health, Task)")
    print("    └── Integration Tests")
    print()
    print("  Backend:")
    print("    ├── API Endpoints (Health, Usage Stats)")
    print("    └── CRUD Operations (Task, Generation, Usage Stats)")
    
    print()
    print_colored("🚀 Test Suite Features:", Colors.BLUE)
    print("  ✅ Comprehensive component testing (300+ tests)")
    print("  ✅ API integration testing")
    print("  ✅ State management testing")
    print("  ✅ Error handling and edge cases")
    print("  ✅ Accessibility compliance")
    print("  ✅ Theme compatibility")
    print("  ✅ Performance and loading states")
    print("  ✅ User interaction simulation")
    
    print()
    print_colored("📈 Coverage Metrics:", Colors.BLUE)
    frontend_cov = get_frontend_coverage(frontend_dir)
    backend_cov = get_backend_coverage(backend_dir / "htmlcov")

    if frontend_cov is not None:
        print(f"  • Frontend line coverage: {frontend_cov:.1f}%")
    else:
        print("  • Frontend line coverage: N/A (report not found)")

    if backend_cov is not None:
        print(f"  • Backend line coverage: {backend_cov:.1f}%")
    else:
        print("  • Backend line coverage: N/A (report not found)")
    
    print()
    if failed_tests == 0:
        print_colored("🎯 Ready for production deployment! 🚀", Colors.GREEN)
    else:
        print_colored("🔧 Please fix failing tests before deployment.", Colors.YELLOW)
    
    print()
    print_colored("Happy testing! 🧪✨", Colors.CYAN)
    
    sys.exit(exit_code)

if __name__ == "__main__":
    main() 