#!/bin/bash

# Usage Statistics Test Runner Script
# This script runs comprehensive tests for usage statistics functionality

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🧪 Usage Statistics Test Suite Runner${NC}"
echo -e "${BLUE}=====================================${NC}"
echo ""

# Function to print section headers
print_section() {
    echo -e "${YELLOW}📋 $1${NC}"
    echo -e "${YELLOW}$(printf '=%.0s' {1..50})${NC}"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check prerequisites
print_section "Checking Prerequisites"

if ! command_exists npm; then
    echo -e "${RED}❌ npm is not installed${NC}"
    exit 1
fi

if ! command_exists python; then
    echo -e "${RED}❌ Python is not installed${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Prerequisites check passed${NC}"
echo ""

# Frontend Tests
print_section "Frontend Usage Statistics Tests"

cd frontend

echo "Installing frontend dependencies..."
npm install

echo ""
echo "Running frontend usage statistics tests..."

# Run specific usage statistics tests
echo -e "${BLUE}🔬 Running API Client Tests${NC}"
npm test -- src/test/api/apiClient.usageStats.test.ts --run

echo -e "${BLUE}🔬 Running Component Tests${NC}"
npm test -- src/test/components/UsageStatsCard.test.tsx --run
npm test -- src/test/components/CombinedUsageSummary.test.tsx --run

echo -e "${BLUE}🔬 Running Store Tests${NC}"
npm test -- src/test/store/taskStore.usageStats.test.ts --run

echo -e "${BLUE}🔬 Running Integration Tests${NC}"
npm test -- src/test/integration/usageStats.integration.test.tsx --run

echo ""
echo -e "${BLUE}📊 Generating Coverage Report${NC}"
npm run test:coverage -- src/test --run

echo -e "${GREEN}✅ Frontend tests completed${NC}"

cd ..

# Backend Tests
print_section "Backend Usage Statistics Tests"

cd backend

echo "Installing backend dependencies..."
poetry install

echo ""
echo "Running backend usage statistics tests..."

echo -e "${BLUE}🔬 Running CRUD Tests${NC}"
poetry run pytest tests/test_crud_usage_stats.py -v

echo -e "${BLUE}🔬 Running API Tests${NC}"
poetry run pytest tests/test_api_usage_stats.py -v

echo ""
echo -e "${BLUE}📊 Generating Coverage Report${NC}"
poetry run pytest --cov=app.crud --cov=app.api --cov-report=html --cov-report=term tests/test_*usage_stats*.py

echo -e "${GREEN}✅ Backend tests completed${NC}"

cd ..

# Summary
print_section "Test Summary"

echo -e "${GREEN}🎉 All usage statistics tests completed successfully!${NC}"
echo ""
echo -e "${BLUE}📁 Test Coverage Reports:${NC}"
echo "  Frontend: frontend/coverage/index.html"
echo "  Backend: backend/htmlcov/index.html"
echo ""
echo -e "${BLUE}🔍 Test Files Created:${NC}"
echo "  Frontend:"
echo "    ├── src/test/setup.ts"
echo "    ├── src/test/mocks/usageStatsMocks.ts"
echo "    ├── src/test/api/apiClient.usageStats.test.ts"
echo "    ├── src/test/components/UsageStatsCard.test.tsx"
echo "    ├── src/test/components/CombinedUsageSummary.test.tsx"
echo "    ├── src/test/store/taskStore.usageStats.test.ts"
echo "    ├── src/test/integration/usageStats.integration.test.tsx"
echo "    └── src/test/README.md"
echo ""
echo "  Backend:"
echo "    ├── tests/conftest.py"
echo "    ├── tests/test_crud_usage_stats.py"
echo "    ├── tests/test_api_usage_stats.py"
echo "    └── pytest.ini"
echo ""
echo -e "${BLUE}🚀 Next Steps:${NC}"
echo "1. Review test coverage reports"
echo "2. Run tests in CI/CD pipeline"
echo "3. Add performance benchmarks"
echo "4. Monitor test execution in production builds"
echo ""
echo -e "${GREEN}Happy testing! 🧪✨${NC}"