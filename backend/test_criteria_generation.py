#!/usr/bin/env python3
"""
Test script to analyze criteria generation issues:
1. Component count problems
2. Weight distribution issues
3. Model behavior analysis
"""

import asyncio
import json
import logging
from typing import List, Dict, Any
import httpx
import os
import sys

# Add the app directory to Python path
sys.path.append('/root/projects/report/llm-eval-platform/backend')

from app.services.criteria_generator import CriteriaGenerator, GeneratedCriterion
from app.services.llm_adapter import LLMService

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class CriteriaAnalyzer:
    """Analyze criteria generation patterns and issues."""
    
    def __init__(self):
        self.llm_service = LLMService()
        self.criteria_generator = CriteriaGenerator(self.llm_service)
    
    async def test_criteria_generation(self, test_cases: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Test criteria generation with different parameters."""
        results = []
        
        for i, test_case in enumerate(test_cases):
            logger.info(f"Running test case {i+1}/{len(test_cases)}: {test_case['name']}")
            
            try:
                criteria, usage_stats = await self.criteria_generator.generate_criteria(
                    task_prompt=test_case['task_prompt'],
                    evaluation_type=test_case['evaluation_type'],
                    num_criteria=test_case['num_criteria'],
                    model_name=test_case['model_name']
                )
                
                # Analyze the results
                analysis = self.analyze_criteria(criteria, test_case)
                results.append({
                    'test_case': test_case,
                    'criteria': [c.model_dump() for c in criteria],
                    'analysis': analysis
                })
                
                logger.info(f"Test case {i+1} completed: {analysis['summary']}")
                
            except Exception as e:
                logger.error(f"Test case {i+1} failed: {str(e)}")
                results.append({
                    'test_case': test_case,
                    'error': str(e),
                    'analysis': {'summary': f'Failed: {str(e)}'}
                })
        
        return {
            'test_results': results,
            'overall_analysis': self.generate_overall_analysis(results)
        }
    
    def analyze_criteria(self, criteria: List[GeneratedCriterion], test_case: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze generated criteria for common issues."""
        analysis = {
            'criteria_count': len(criteria),
            'expected_count': test_case['num_criteria'],
            'count_match': len(criteria) == test_case['num_criteria'],
            'weight_issues': [],
            'component_issues': [],
            'criteria_details': []
        }
        
        total_weight = 0
        for i, criterion in enumerate(criteria):
            criterion_analysis = {
                'name': criterion.name,
                'weight': criterion.weight,
                'component_count': len(criterion.components),
                'component_weight_sum': sum(comp.weight for comp in criterion.components),
                'component_weights': [comp.weight for comp in criterion.components]
            }
            
            # Check component weight distribution
            comp_weight_sum = criterion_analysis['component_weight_sum']
            if abs(comp_weight_sum - 1.0) > 0.01:  # Allow small floating point errors
                analysis['component_issues'].append(
                    f"Criterion '{criterion.name}': component weights sum to {comp_weight_sum:.3f}, not 1.0"
                )
            
            # Check component count
            if len(criterion.components) < 2:
                analysis['component_issues'].append(
                    f"Criterion '{criterion.name}': only {len(criterion.components)} component(s), expected multiple"
                )
            
            total_weight += criterion.weight
            analysis['criteria_details'].append(criterion_analysis)
        
        # Check overall weight distribution
        analysis['total_weight'] = total_weight
        if test_case['num_criteria'] > 1:
            expected_avg_weight = 1.0 / test_case['num_criteria']
            if abs(total_weight - 1.0) > 0.1:  # More tolerance for overall weights
                analysis['weight_issues'].append(
                    f"Total weight {total_weight:.3f} doesn't sum to 1.0"
                )
        
        # Generate summary
        issues = len(analysis['weight_issues']) + len(analysis['component_issues'])
        if not analysis['count_match']:
            issues += 1
        
        analysis['summary'] = f"{analysis['criteria_count']} criteria, {issues} issues"
        analysis['has_issues'] = issues > 0
        
        return analysis
    
    def generate_overall_analysis(self, results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Generate overall analysis across all test cases."""
        successful_tests = [r for r in results if 'error' not in r]
        failed_tests = [r for r in results if 'error' in r]
        
        if not successful_tests:
            return {'summary': 'All tests failed', 'recommendations': ['Fix basic connectivity/setup issues']}
        
        # Aggregate statistics
        total_criteria = sum(len(r['criteria']) for r in successful_tests)
        total_components = sum(
            sum(len(c.get('components', [])) for c in r['criteria'])
            for r in successful_tests
        )
        
        # Count common issues
        weight_issues = sum(len(r['analysis'].get('weight_issues', [])) for r in successful_tests)
        component_issues = sum(len(r['analysis'].get('component_issues', [])) for r in successful_tests)
        count_mismatches = sum(1 for r in successful_tests if not r['analysis'].get('count_match', True))
        
        # Analyze patterns
        avg_components_per_criterion = total_components / total_criteria if total_criteria > 0 else 0
        
        recommendations = []
        
        if avg_components_per_criterion < 2:
            recommendations.append("Improve prompts to generate multiple components per criterion")
        
        if weight_issues > len(successful_tests) * 0.5:
            recommendations.append("Add weight normalization/validation logic")
        
        if component_issues > len(successful_tests) * 0.5:
            recommendations.append("Improve component weight distribution in prompts")
        
        if count_mismatches > 0:
            recommendations.append("Fix criteria count validation")
        
        return {
            'summary': f'{len(successful_tests)}/{len(results)} tests passed',
            'total_criteria': total_criteria,
            'avg_components_per_criterion': avg_components_per_criterion,
            'weight_issues': weight_issues,
            'component_issues': component_issues,
            'count_mismatches': count_mismatches,
            'recommendations': recommendations,
            'failed_tests': len(failed_tests)
        }
    
    async def test_raw_llm_response(self, task_prompt: str, model_name: str) -> Dict[str, Any]:
        """Test raw LLM response to understand generation behavior."""
        logger.info(f"Testing raw LLM response with {model_name}")
        
        # Use the same prompt as criteria generator
        system_prompt = """You are an expert at evaluating AI-generated code projects. Given a task prompt, generate comprehensive evaluation criteria.

The criteria should cover:
1. Functional correctness and completeness
2. Code quality and best practices
3. Architecture and design decisions
4. Documentation and readability
5. Testing and error handling
6. Performance and efficiency

Each criterion should be:
- Objectively measurable
- Clearly defined with scoring rubrics
- Weighted by importance
- Include specific evaluation methods

IMPORTANT REQUIREMENTS:
1. Generate EXACTLY 3-5 components per criterion
2. Component weights within each criterion MUST sum to 1.0
3. Overall criterion weights should be balanced
4. Each component needs detailed scoring rubric

IMPORTANT: For scoring_rubric, use the following standardized format:
- "0-30": Description for poor/failing performance
- "31-60": Description for below average performance  
- "61-80": Description for good/acceptable performance
- "81-100": Description for excellent/exceptional performance

Output the criteria in the specified JSON format."""
        
        user_prompt = f"""Task Prompt:
{task_prompt}

Task Context:
- Evaluation Type: 0-1 (from scratch)

Please generate exactly 3 evaluation criteria for this task.

CRITICAL: Each criterion must have 3-5 components, and component weights must sum to 1.0.

Example of expected component structure:
"components": [
    {{
        "name": "Component 1",
        "description": "What this measures",
        "weight": 0.4,
        "evaluation_method": "How to evaluate",
        "scoring_rubric": {{
            "0-30": "Poor performance description",
            "31-60": "Below average performance",
            "61-80": "Good performance",
            "81-100": "Excellent performance"
        }}
    }},
    {{
        "name": "Component 2", 
        "weight": 0.6,
        ...
    }}
]

Ensure weights sum to 1.0: 0.4 + 0.6 = 1.0"""
        
        try:
            # Test without tool calling first
            response = await self.llm_service.generate(
                prompt=user_prompt,
                model=model_name,
                system_prompt=system_prompt,
                temperature=0.3
            )
            
            return {
                'model': model_name,
                'response_type': 'raw',
                'content': response.get('content', ''),
                'raw_content': response.get('raw_content', ''),
                'usage': response.get('usage', {}),
                'success': True
            }
            
        except Exception as e:
            logger.error(f"Raw LLM test failed: {str(e)}")
            return {
                'model': model_name,
                'response_type': 'raw',
                'error': str(e),
                'success': False
            }

async def main():
    """Main test execution."""
    analyzer = CriteriaAnalyzer()
    
    # Define test cases
    test_cases = [
        {
            'name': 'Basic Code Generation - Claude Haiku',
            'task_prompt': 'Build a simple todo application with React that allows adding, removing, and marking tasks as complete',
            'evaluation_type': '0-1',
            'num_criteria': 3,
            'model_name': 'anthropic/claude-3.5-haiku'
        },
        {
            'name': 'Basic Code Generation - Claude Sonnet',
            'task_prompt': 'Build a simple todo application with React that allows adding, removing, and marking tasks as complete',
            'evaluation_type': '0-1',
            'num_criteria': 3,
            'model_name': 'anthropic/claude-3.5-sonnet'
        },
        {
            'name': 'Complex Algorithm - Claude Haiku',
            'task_prompt': 'Implement a binary search tree with insertion, deletion, and traversal methods in Python',
            'evaluation_type': '0-1',
            'num_criteria': 4,
            'model_name': 'anthropic/claude-3.5-haiku'
        },
        {
            'name': 'Code Improvement - Claude Haiku',
            'task_prompt': 'Optimize the existing sorting algorithm implementation for better performance',
            'evaluation_type': '90-100',
            'num_criteria': 3,
            'model_name': 'anthropic/claude-3.5-haiku'
        }
    ]
    
    print("🧪 Starting Criteria Generation Analysis")
    print("=" * 60)
    
    # Test criteria generation
    logger.info("Testing criteria generation with different parameters...")
    results = await analyzer.test_criteria_generation(test_cases)
    
    # Test raw LLM response
    logger.info("Testing raw LLM response...")
    raw_response = await analyzer.test_raw_llm_response(
        "Build a simple calculator web app with basic arithmetic operations",
        "anthropic/claude-3.5-haiku"
    )
    
    # Print detailed results
    print("\n📊 DETAILED RESULTS")
    print("=" * 60)
    
    for i, result in enumerate(results['test_results']):
        print(f"\n🔍 Test Case {i+1}: {result['test_case']['name']}")
        print("-" * 40)
        
        if 'error' in result:
            print(f"❌ FAILED: {result['error']}")
            continue
        
        analysis = result['analysis']
        print(f"✅ Status: {analysis['summary']}")
        print(f"📈 Criteria Count: {analysis['criteria_count']}/{analysis['expected_count']}")
        print(f"⚖️  Total Weight: {analysis.get('total_weight', 0):.3f}")
        
        if analysis.get('weight_issues'):
            print("⚠️  Weight Issues:")
            for issue in analysis['weight_issues']:
                print(f"   - {issue}")
        
        if analysis.get('component_issues'):
            print("⚠️  Component Issues:")
            for issue in analysis['component_issues']:
                print(f"   - {issue}")
        
        print("\n📋 Criteria Details:")
        for j, detail in enumerate(analysis['criteria_details']):
            print(f"   {j+1}. {detail['name']}")
            print(f"      Weight: {detail['weight']:.3f}")
            print(f"      Components: {detail['component_count']} (weights: {[f'{w:.2f}' for w in detail['component_weights']]})")
            print(f"      Component Sum: {detail['component_weight_sum']:.3f}")
    
    # Overall analysis
    print(f"\n🎯 OVERALL ANALYSIS")
    print("=" * 60)
    overall = results['overall_analysis']
    print(f"Success Rate: {overall['summary']}")
    print(f"Average Components per Criterion: {overall.get('avg_components_per_criterion', 0):.2f}")
    print(f"Weight Issues: {overall.get('weight_issues', 0)}")
    print(f"Component Issues: {overall.get('component_issues', 0)}")
    
    if overall.get('recommendations'):
        print("\n💡 RECOMMENDATIONS:")
        for rec in overall['recommendations']:
            print(f"   - {rec}")
    
    # Raw response analysis
    print(f"\n🔬 RAW LLM RESPONSE ANALYSIS")
    print("=" * 60)
    if raw_response['success']:
        print(f"Model: {raw_response['model']}")
        print(f"Content Type: {type(raw_response.get('content', ''))}")
        print(f"Content Length: {len(str(raw_response.get('content', '')))}")
        print(f"Raw Content Preview: {str(raw_response.get('raw_content', ''))[:200]}...")
        
        # Try to parse the content
        try:
            if isinstance(raw_response.get('content'), str):
                parsed = json.loads(raw_response['content'])
                print(f"✅ JSON Parseable: Yes")
                print(f"Structure: {type(parsed)}")
                if isinstance(parsed, dict) and 'criteria' in parsed:
                    print(f"Criteria Count: {len(parsed['criteria'])}")
                elif isinstance(parsed, list):
                    print(f"Criteria Count: {len(parsed)}")
            else:
                print(f"✅ Content Type: {type(raw_response.get('content'))}")
        except json.JSONDecodeError as e:
            print(f"❌ JSON Parse Error: {str(e)}")
    else:
        print(f"❌ Raw response failed: {raw_response.get('error', 'Unknown error')}")
    
    # Save detailed results to file
    output_file = "/root/projects/report/llm-eval-platform/backend/criteria_analysis_results.json"
    with open(output_file, 'w') as f:
        json.dump({
            'test_results': results,
            'raw_response': raw_response,
            'timestamp': str(asyncio.get_event_loop().time())
        }, f, indent=2, default=str)
    
    print(f"\n💾 Detailed results saved to: {output_file}")
    print("\n🏁 Analysis Complete!")

if __name__ == "__main__":
    asyncio.run(main()) 