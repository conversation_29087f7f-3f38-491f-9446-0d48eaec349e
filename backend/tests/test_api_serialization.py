"""Test API serialization to ensure proper response models are used."""
import pytest
import pytest_asyncio
from httpx import AsyncClient
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_sessionmaker
from sqlalchemy.pool import StaticPool

from app.main import app
from app.db.base import Base, get_db


@pytest_asyncio.fixture
async def async_engine():
    """Create a test database engine."""
    # Use in-memory SQLite for tests
    engine = create_async_engine(
        "sqlite+aiosqlite:///:memory:",
        connect_args={"check_same_thread": False},
        poolclass=StaticPool,
    )
    
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)
    
    yield engine
    
    await engine.dispose()


@pytest_asyncio.fixture
async def async_session(async_engine):
    """Create a test database session."""
    async_session_maker = async_sessionmaker(
        async_engine,
        class_=AsyncSession,
        expire_on_commit=False,
        autocommit=False,
        autoflush=False,
    )
    
    async with async_session_maker() as session:
        yield session


@pytest_asyncio.fixture
async def client(async_session):
    """Create test client with overridden database."""
    async def override_get_db():
        yield async_session
    
    app.dependency_overrides[get_db] = override_get_db
    
    async with AsyncClient(app=app, base_url="http://test") as ac:
        yield ac
    
    app.dependency_overrides.clear()


class TestAPISerialization:
    """Test that API endpoints properly serialize responses."""
    
    @pytest.mark.asyncio
    async def test_create_task_serialization(self, client):
        """Test that create task endpoint returns proper JSON."""
        task_data = {
            "name": "Test Task",
            "description": "Test description",
            "evaluation_type": "0-1",
            "prompt": "Test prompt"
        }
        
        response = await client.post("/api/v1/agent-evaluations/tasks", json=task_data)
        
        # Should not raise serialization error
        assert response.status_code == 200
        
        # Check response is proper JSON
        data = response.json()
        assert isinstance(data, dict)
        assert "id" in data
        assert "name" in data
        assert data["name"] == task_data["name"]
        
        # Ensure it's not returning SQLAlchemy internals
        assert "_sa_instance_state" not in data
    
    @pytest.mark.asyncio
    async def test_list_tasks_serialization(self, client):
        """Test that list tasks endpoint returns proper JSON array."""
        response = await client.get("/api/v1/agent-evaluations/tasks")
        
        # Should not raise serialization error
        assert response.status_code == 200
        
        # Check response is proper JSON array
        data = response.json()
        assert isinstance(data, list)
    
    @pytest.mark.asyncio
    async def test_get_task_serialization(self, client):
        """Test that get single task endpoint returns proper JSON."""
        # First create a task
        task_data = {
            "name": "Test Task",
            "description": "Test description",
            "evaluation_type": "0-1",
            "prompt": "Test prompt"
        }
        
        create_response = await client.post("/api/v1/agent-evaluations/tasks", json=task_data)
        assert create_response.status_code == 200
        task_id = create_response.json()["id"]
        
        # Get the task
        response = await client.get(f"/api/v1/agent-evaluations/tasks/{task_id}")
        
        # Should not raise serialization error
        assert response.status_code == 200
        
        # Check response is proper JSON
        data = response.json()
        assert isinstance(data, dict)
        assert data["id"] == task_id
        
        # Ensure it's not returning SQLAlchemy internals
        assert "_sa_instance_state" not in data