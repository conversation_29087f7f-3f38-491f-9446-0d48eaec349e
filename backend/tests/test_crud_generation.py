import pytest
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select

from app.crud import crud_generation
from app.schemas import task as task_schema
from app.db import models
from app.crud import crud_task


@pytest.mark.asyncio
class TestGenerationCRUD:
    """Test cases for generation CRUD operations."""

    async def test_create_generation_basic(self, db_session: AsyncSession):
        """Test creating a basic generation."""
        # First create a task
        task_data = task_schema.TaskCreate(prompt="Test prompt")
        task = await crud_task.create_task(db_session, task_data)
        
        # Create generation data using Pydantic model
        generation_data = task_schema.GenerationCreate(
            task_id=task.id,
            model_id_used='openai/gpt-4',
            blind_id='blind-123',
            output_text='Generated response',
            reasoning_text='Reasoning process',
            error_message=None
        )
        
        created_generation = await crud_generation.create_generation(db_session, generation_data)
        
        assert created_generation.id is not None
        assert created_generation.task_id == task.id
        assert created_generation.model_id_used == 'openai/gpt-4'
        assert created_generation.blind_id == 'blind-123'
        assert created_generation.output_text == 'Generated response'
        assert created_generation.reasoning_text == 'Reasoning process'
        assert created_generation.error_message is None
        assert created_generation.created_at is not None

    async def test_create_generation_with_usage_stats(self, db_session: AsyncSession):
        """Test creating a generation with usage statistics."""
        # Create a task
        task_data = task_schema.TaskCreate(prompt="Test prompt")
        task = await crud_task.create_task(db_session, task_data)
        
        # Create generation with usage stats using Pydantic model
        generation_data = task_schema.GenerationCreate(
            task_id=task.id,
            model_id_used='openai/gpt-4',
            blind_id='blind-456',
            output_text='Generated response with stats',
            prompt_tokens=100,
            completion_tokens=150,
            total_tokens=250,
            reasoning_tokens=50,
            cached_tokens=25,
            cost_credits=0.002500,
            generation_id='gen-123456'
        )
        
        created_generation = await crud_generation.create_generation(db_session, generation_data)
        
        assert created_generation.prompt_tokens == 100
        assert created_generation.completion_tokens == 150
        assert created_generation.total_tokens == 250
        assert created_generation.reasoning_tokens == 50
        assert created_generation.cached_tokens == 25
        assert created_generation.cost_credits == 0.002500
        assert created_generation.generation_id == 'gen-123456'

    async def test_create_generation_with_error(self, db_session: AsyncSession):
        """Test creating a generation with error message."""
        # Create a task
        task_data = task_schema.TaskCreate(prompt="Test prompt")
        task = await crud_task.create_task(db_session, task_data)
        
        # Create generation with error using Pydantic model
        generation_data = task_schema.GenerationCreate(
            task_id=task.id,
            model_id_used='openai/gpt-4',
            blind_id='blind-error',
            output_text=None,
            error_message='Rate limit exceeded'
        )
        
        created_generation = await crud_generation.create_generation(db_session, generation_data)
        
        assert created_generation.output_text is None
        assert created_generation.error_message == 'Rate limit exceeded'

    async def test_get_generation_by_id(self, db_session: AsyncSession):
        """Test retrieving a generation by ID."""
        # Create task and generation
        task_data = task_schema.TaskCreate(prompt="Test prompt")
        task = await crud_task.create_task(db_session, task_data)
        
        generation_data = task_schema.GenerationCreate(
            task_id=task.id,
            model_id_used='openai/gpt-4',
            blind_id='blind-123',
            output_text='Test output'
        )
        
        created_generation = await crud_generation.create_generation(db_session, generation_data)
        
        # Retrieve the generation
        retrieved_generation = await crud_generation.get_generation(db_session, created_generation.id)
        
        assert retrieved_generation is not None
        assert retrieved_generation.id == created_generation.id
        assert retrieved_generation.output_text == 'Test output'

    async def test_get_generation_nonexistent(self, db_session: AsyncSession):
        """Test retrieving a non-existent generation."""
        retrieved_generation = await crud_generation.get_generation(db_session, 99999)
        assert retrieved_generation is None

    async def test_get_generations_by_task_id(self, db_session: AsyncSession):
        """Test retrieving all generations for a task."""
        # Create task
        task_data = task_schema.TaskCreate(prompt="Test prompt")
        task = await crud_task.create_task(db_session, task_data)
        
        # Create multiple generations
        generation_data_list = [
            task_schema.GenerationCreate(
                task_id=task.id,
                model_id_used=f'model-{i}',
                blind_id=f'blind-{i}',
                output_text=f'Output {i}'
            )
            for i in range(3)
        ]
        
        created_generations = []
        for gen_data in generation_data_list:
            gen = await crud_generation.create_generation(db_session, gen_data)
            created_generations.append(gen)
        
        # Retrieve generations by task ID
        retrieved_generations = await crud_generation.get_generations_by_task_id(db_session, task.id)
        
        assert len(retrieved_generations) == 3
        retrieved_outputs = [gen.output_text for gen in retrieved_generations]
        assert 'Output 0' in retrieved_outputs
        assert 'Output 1' in retrieved_outputs
        assert 'Output 2' in retrieved_outputs

    async def test_update_generation_output(self, db_session: AsyncSession):
        """Test updating generation output text."""
        # Create task and generation
        task_data = task_schema.TaskCreate(prompt="Test prompt")
        task = await crud_task.create_task(db_session, task_data)
        
        generation_data = task_schema.GenerationCreate(
            task_id=task.id,
            model_id_used='openai/gpt-4',
            blind_id='blind-123',
            output_text='Original output'
        )
        
        created_generation = await crud_generation.create_generation(db_session, generation_data)
        
        # Update the generation
        updated_generation = await crud_generation.update_generation_output(
            db_session, 
            created_generation.id, 
            'Updated output text'
        )
        
        assert updated_generation is not None
        assert updated_generation.output_text == 'Updated output text'
        assert updated_generation.id == created_generation.id

    async def test_update_generation_usage_stats(self, db_session: AsyncSession):
        """Test updating generation usage statistics."""
        # Create task and generation
        task_data = task_schema.TaskCreate(prompt="Test prompt")
        task = await crud_task.create_task(db_session, task_data)
        
        generation_data = task_schema.GenerationCreate(
            task_id=task.id,
            model_id_used='openai/gpt-4',
            blind_id='blind-123',
            output_text='Test output'
        )
        
        created_generation = await crud_generation.create_generation(db_session, generation_data)
        
        # Update usage stats
        usage_stats = {
            'prompt_tokens': 200,
            'completion_tokens': 300,
            'total_tokens': 500,
            'cost_credits': 0.005000
        }
        
        updated_generation = await crud_generation.update_generation_usage_stats(
            db_session, 
            created_generation.id, 
            usage_stats
        )
        
        assert updated_generation is not None
        assert updated_generation.prompt_tokens == 200
        assert updated_generation.completion_tokens == 300
        assert updated_generation.total_tokens == 500
        assert updated_generation.cost_credits == 0.005000

    async def test_delete_generation(self, db_session: AsyncSession):
        """Test deleting a generation."""
        # Create task and generation
        task_data = task_schema.TaskCreate(prompt="Test prompt")
        task = await crud_task.create_task(db_session, task_data)
        
        generation_data = task_schema.GenerationCreate(
            task_id=task.id,
            model_id_used='openai/gpt-4',
            blind_id='blind-123',
            output_text='Test output'
        )
        
        created_generation = await crud_generation.create_generation(db_session, generation_data)
        generation_id = created_generation.id
        
        # Delete the generation
        result = await crud_generation.delete_generation(db_session, generation_id)
        
        assert result is True
        
        # Verify deletion
        deleted_generation = await crud_generation.get_generation(db_session, generation_id)
        assert deleted_generation is None

    async def test_delete_generation_nonexistent(self, db_session: AsyncSession):
        """Test deleting a non-existent generation."""
        result = await crud_generation.delete_generation(db_session, 99999)
        assert result is False

    async def test_get_generations_with_usage_stats(self, db_session: AsyncSession):
        """Test retrieving generations with usage statistics."""
        # Create task
        task_data = task_schema.TaskCreate(prompt="Test prompt")
        task = await crud_task.create_task(db_session, task_data)
        
        # Create generation with usage stats
        generation_data = task_schema.GenerationCreate(
            task_id=task.id,
            model_id_used='openai/gpt-4',
            blind_id='blind-stats',
            output_text='Output with stats',
            prompt_tokens=150,
            completion_tokens=200,
            total_tokens=350,
            cost_credits=0.003500
        )
        
        created_generation = await crud_generation.create_generation(db_session, generation_data)
        
        # Retrieve and verify usage stats
        retrieved_generation = await crud_generation.get_generation(db_session, created_generation.id)
        
        assert retrieved_generation.prompt_tokens == 150
        assert retrieved_generation.completion_tokens == 200
        assert retrieved_generation.total_tokens == 350
        assert retrieved_generation.cost_credits == 0.003500

    async def test_generation_with_long_output(self, db_session: AsyncSession):
        """Test creating generation with very long output text."""
        # Create task
        task_data = task_schema.TaskCreate(prompt="Test prompt")
        task = await crud_task.create_task(db_session, task_data)
        
        # Create generation with long output
        long_output = "A" * 50000  # Very long output
        generation_data = task_schema.GenerationCreate(
            task_id=task.id,
            model_id_used='openai/gpt-4',
            blind_id='blind-long',
            output_text=long_output
        )
        
        created_generation = await crud_generation.create_generation(db_session, generation_data)
        
        assert created_generation.output_text == long_output
        assert len(created_generation.output_text) == 50000

    async def test_generation_with_special_characters(self, db_session: AsyncSession):
        """Test creating generation with special characters."""
        # Create task
        task_data = task_schema.TaskCreate(prompt="Test prompt")
        task = await crud_task.create_task(db_session, task_data)
        
        # Create generation with special characters
        special_output = "Output with émojis 🚀 and special chars: @#$%^&*()[]{}|\\:;\"'<>,.?/~`"
        generation_data = task_schema.GenerationCreate(
            task_id=task.id,
            model_id_used='openai/gpt-4',
            blind_id='blind-special',
            output_text=special_output,
            reasoning_text='Reasoning with ñáéíóú characters'
        )
        
        created_generation = await crud_generation.create_generation(db_session, generation_data)
        
        assert created_generation.output_text == special_output
        assert created_generation.reasoning_text == 'Reasoning with ñáéíóú characters' 