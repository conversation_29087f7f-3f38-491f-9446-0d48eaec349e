"""API endpoint tests for agent evaluation system - without authentication."""
import pytest
import json
from unittest.mock import Mock, AsyncMock, patch
from fastapi.testclient import TestClient
from uuid import uuid4

from app.main import app


class TestAgentEvaluationAPI:
    """Test agent evaluation API endpoints."""
    
    @pytest.fixture
    def client(self):
        """Create test client."""
        return TestClient(app)
    
    def test_create_evaluation_task(self, client):
        """Test creating an evaluation task."""
        task_data = {
            "name": "Test Task",
            "description": "Test description",
            "evaluation_type": "0-1",
            "prompt": "Create a test application"
        }
        
        response = client.post("/api/v1/agent-evaluations/tasks", json=task_data)
        
        assert response.status_code == 200
        data = response.json()
        assert data["name"] == task_data["name"]
        assert data["description"] == task_data["description"]
        assert data["evaluation_type"] == task_data["evaluation_type"]
        assert data["prompt"] == task_data["prompt"]
        assert "id" in data
        assert "created_at" in data
        assert "status" in data
    
    def test_list_evaluation_tasks(self, client):
        """Test listing evaluation tasks."""
        response = client.get("/api/v1/agent-evaluations/tasks")
        
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)
    
    def test_get_evaluation_task(self, client):
        """Test getting a specific evaluation task."""
        # First create a task
        task_data = {
            "name": "Test Task",
            "description": "Test description",
            "evaluation_type": "0-1",
            "prompt": "Create a test application"
        }
        
        create_response = client.post("/api/v1/agent-evaluations/tasks", json=task_data)
        assert create_response.status_code == 200
        created_task = create_response.json()
        
        # Then get it
        response = client.get(f"/api/v1/agent-evaluations/tasks/{created_task['id']}")
        
        assert response.status_code == 200
        data = response.json()
        assert data["id"] == created_task["id"]
        assert data["name"] == task_data["name"]
    
    def test_import_folder_submission(self, client):
        """Test importing a folder submission."""
        # First create a task
        task_data = {
            "name": "Test Task",
            "description": "Test description",
            "evaluation_type": "0-1",
            "prompt": "Create a test application"
        }
        
        create_response = client.post("/api/v1/agent-evaluations/tasks", json=task_data)
        assert create_response.status_code == 200
        task_id = create_response.json()["id"]
        
        # Import folder submission
        submission_data = {
            "folder_path": "/test/path",
            "agent_name": "TestAgent",
            "agent_version": "1.0.0"
        }
        
        response = client.post(
            f"/api/v1/agent-evaluations/tasks/{task_id}/import-folder",
            json=submission_data
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "submission_id" in data
        assert data["status"] == "importing"
    
    def test_estimate_tokens_folder(self, client):
        """Test token estimation for folder."""
        response = client.get(
            "/api/v1/agent-evaluations/estimate-tokens",
            params={"folder_path": "/test/path"}
        )
        
        # This will likely fail without a real folder, but we can test the endpoint exists
        assert response.status_code in [200, 400]
    
    def test_generate_criteria(self, client):
        """Test generating evaluation criteria."""
        # First create a task
        task_data = {
            "name": "Test Task",
            "description": "Test description",
            "evaluation_type": "0-1",
            "prompt": "Create a test application"
        }
        
        create_response = client.post("/api/v1/agent-evaluations/tasks", json=task_data)
        assert create_response.status_code == 200
        task_id = create_response.json()["id"]
        
        # Generate criteria
        response = client.post(
            f"/api/v1/agent-evaluations/generate-criteria",
            json={"task_id": task_id}
        )
        
        # This might fail without OpenRouter API key, but we can test the endpoint exists
        assert response.status_code in [200, 500]
    
    def test_evaluation_stream_endpoint(self, client):
        """Test evaluation stream endpoint exists."""
        task_id = str(uuid4())
        
        # The stream endpoint should return 404 for non-existent task
        response = client.get(f"/api/v1/agent-evaluations/stream/{task_id}")
        
        assert response.status_code == 404