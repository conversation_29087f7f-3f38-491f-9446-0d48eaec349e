import pytest
from sqlalchemy.ext.asyncio import AsyncSession
from app.crud.crud_generation import create_generation, get_generations_for_task, get_generation_by_id, update_generation_usage
from app.crud.crud_ranking import create_ranking, get_ranking_by_id
from app.crud.crud_task import create_task
from app.crud.crud_evaluation import create_evaluation
from app.db.models import Task, Generation, Evaluation, Ranking
from app.schemas.task import TaskCreate, GenerationCreate, RankingCreate


class TestGenerationUsageStatistics:
    """Test usage statistics functionality for generations."""

    @pytest.mark.asyncio
    async def test_create_generation_with_usage_stats(
        self, 
        db_session: AsyncSession, 
        sample_task_data, 
        sample_generation_data
    ):
        """Test creating a generation with usage statistics."""
        # Create task first
        task_create = TaskCreate(**sample_task_data)
        task = await create_task(db_session, task_in=task_create)
        
        # Update generation data with correct task_id
        generation_data = {**sample_generation_data, "task_id": task.id}
        generation_create = GenerationCreate(**generation_data)
        
        # Create generation with usage stats
        generation = await create_generation(db_session, generation_in=generation_create)
        
        assert generation.id is not None
        assert generation.task_id == task.id
        assert generation.prompt_tokens == 100
        assert generation.completion_tokens == 150
        assert generation.total_tokens == 250
        assert generation.reasoning_tokens == 50
        assert generation.cached_tokens == 25
        assert generation.cost_credits == 0.002500
        assert generation.generation_id == "gen-123456"

    @pytest.mark.asyncio
    async def test_create_generation_without_usage_stats(
        self, 
        db_session: AsyncSession, 
        sample_task_data, 
        sample_generation_without_usage
    ):
        """Test creating a generation without usage statistics."""
        # Create task first
        task_create = TaskCreate(**sample_task_data)
        task = await create_task(db_session, task_in=task_create)
        
        # Update generation data with correct task_id
        generation_data = {**sample_generation_without_usage, "task_id": task.id}
        generation_create = GenerationCreate(**generation_data)
        
        # Create generation without usage stats
        generation = await create_generation(db_session, generation_in=generation_create)
        
        assert generation.id is not None
        assert generation.task_id == task.id
        assert generation.prompt_tokens is None
        assert generation.completion_tokens is None
        assert generation.total_tokens is None
        assert generation.reasoning_tokens is None
        assert generation.cached_tokens is None
        assert generation.cost_credits is None
        assert generation.generation_id is None

    @pytest.mark.asyncio
    async def test_update_generation_with_usage_stats(
        self, 
        db_session: AsyncSession, 
        sample_task_data, 
        sample_generation_without_usage
    ):
        """Test updating a generation to add usage statistics."""
        # Create task first
        task_create = TaskCreate(**sample_task_data)
        task = await create_task(db_session, task_in=task_create)
        
        # Create generation without usage stats
        generation_data = {**sample_generation_without_usage, "task_id": task.id}
        generation_create = GenerationCreate(**generation_data)
        generation = await create_generation(db_session, generation_in=generation_create)
        
        # Update with usage statistics
        usage_update = {
            "prompt_tokens": 200,
            "completion_tokens": 300,
            "total_tokens": 500,
            "reasoning_tokens": 100,
            "cached_tokens": 50,
            "cost_credits": 0.005000,
            "generation_id": "gen-updated-123"
        }
        
        # Use the update function directly with generation id
        success = await update_generation_usage(
            db_session, generation_id=generation.id, usage_data=usage_update
        )
        assert success == True
        
        # Fetch the updated generation to verify
        updated_generation = await get_generation_by_id(db_session, generation_id=generation.id)
        
        assert updated_generation.prompt_tokens == 200
        assert updated_generation.completion_tokens == 300
        assert updated_generation.total_tokens == 500
        assert updated_generation.reasoning_tokens == 100
        assert updated_generation.cached_tokens == 50
        assert updated_generation.cost_credits == 0.005000
        assert updated_generation.generation_id == "gen-updated-123"

    @pytest.mark.asyncio
    async def test_get_generations_by_task_with_usage_filter(
        self, 
        db_session: AsyncSession, 
        sample_task_data, 
        sample_generation_data,
        sample_generation_without_usage
    ):
        """Test filtering generations by usage statistics availability."""
        # Create task
        task_create = TaskCreate(**sample_task_data)
        task = await create_task(db_session, task_in=task_create)
        
        # Create generation with usage stats
        gen_with_usage = {**sample_generation_data, "task_id": task.id}
        gen_create_with = GenerationCreate(**gen_with_usage)
        await create_generation(db_session, generation_in=gen_create_with)
        
        # Create generation without usage stats
        gen_without_usage = {**sample_generation_without_usage, "task_id": task.id}
        gen_create_without = GenerationCreate(**gen_without_usage)
        await create_generation(db_session, generation_in=gen_create_without)
        
        # Get all generations for task
        all_generations = await get_generations_for_task(db_session, task_id=task.id)
        
        # Filter generations with usage statistics
        generations_with_usage = [
            gen for gen in all_generations 
            if gen.generation_id is not None
        ]
        
        # Filter generations without usage statistics
        generations_without_usage = [
            gen for gen in all_generations 
            if gen.generation_id is None
        ]
        
        assert len(all_generations) == 2
        assert len(generations_with_usage) == 1
        assert len(generations_without_usage) == 1
        assert generations_with_usage[0].generation_id == "gen-123456"

    @pytest.mark.asyncio
    async def test_usage_statistics_data_types(
        self, 
        db_session: AsyncSession, 
        sample_task_data, 
        sample_generation_data
    ):
        """Test that usage statistics maintain correct data types."""
        # Create task
        task_create = TaskCreate(**sample_task_data)
        task = await create_task(db_session, task_in=task_create)
        
        # Create generation with usage stats
        generation_data = {**sample_generation_data, "task_id": task.id}
        generation_create = GenerationCreate(**generation_data)
        generation = await create_generation(db_session, generation_in=generation_create)
        
        # Verify data types
        assert isinstance(generation.prompt_tokens, int)
        assert isinstance(generation.completion_tokens, int)
        assert isinstance(generation.total_tokens, int)
        assert isinstance(generation.reasoning_tokens, int)
        assert isinstance(generation.cached_tokens, int)
        assert isinstance(generation.cost_credits, float)
        assert isinstance(generation.generation_id, str)


class TestRankingUsageStatistics:
    """Test usage statistics functionality for rankings."""

    @pytest.mark.asyncio
    async def test_create_ranking_with_usage_stats(
        self, 
        db_session: AsyncSession, 
        sample_task_data, 
        sample_evaluation_data,
        sample_ranking_data
    ):
        """Test creating a ranking with usage statistics."""
        # Create task and evaluation first
        task_create = TaskCreate(**sample_task_data)
        task = await create_task(db_session, task_in=task_create)
        # create_evaluation doesn't use schemas, it takes direct parameters
        evaluation = await create_evaluation(
            db_session, 
            task_id=task.id,
            evaluation_used_blind_ids=True,
            evaluation_prompt=None
        )
        
        # Create ranking with usage stats
        # Remove evaluation_id from sample data as it's not part of RankingCreate schema
        ranking_data = {k: v for k, v in sample_ranking_data.items() if k != "evaluation_id"}
        ranking_create = RankingCreate(**ranking_data)
        
        # Manually create the ranking with evaluation_id
        from app.db.models import Ranking
        db_ranking = Ranking(**ranking_create.model_dump(), evaluation_id=evaluation.id)
        db_session.add(db_ranking)
        await db_session.commit()
        await db_session.refresh(db_ranking)
        ranking = db_ranking
        
        assert ranking.id is not None
        assert ranking.evaluation_id == evaluation.id
        assert ranking.prompt_tokens == 200
        assert ranking.completion_tokens == 100
        assert ranking.total_tokens == 300
        assert ranking.reasoning_tokens == 75
        assert ranking.cached_tokens == 50
        assert ranking.cost_credits == 0.003000
        assert ranking.generation_id == "eval-123456"

    @pytest.mark.asyncio
    async def test_create_ranking_without_usage_stats(
        self, 
        db_session: AsyncSession, 
        sample_task_data, 
        sample_evaluation_data,
        sample_ranking_without_usage
    ):
        """Test creating a ranking without usage statistics."""
        # Create task and evaluation first
        task_create = TaskCreate(**sample_task_data)
        task = await create_task(db_session, task_in=task_create)
        # create_evaluation doesn't use schemas, it takes direct parameters
        evaluation = await create_evaluation(
            db_session, 
            task_id=task.id,
            evaluation_used_blind_ids=True,
            evaluation_prompt=None
        )
        
        # Create ranking without usage stats
        # Remove evaluation_id from sample data as it's not part of RankingCreate schema
        ranking_data = {k: v for k, v in sample_ranking_without_usage.items() if k != "evaluation_id"}
        ranking_create = RankingCreate(**ranking_data)
        
        # Manually create the ranking with evaluation_id
        from app.db.models import Ranking
        db_ranking = Ranking(**ranking_create.model_dump(), evaluation_id=evaluation.id)
        db_session.add(db_ranking)
        await db_session.commit()
        await db_session.refresh(db_ranking)
        ranking = db_ranking
        
        assert ranking.id is not None
        assert ranking.evaluation_id == evaluation.id
        assert ranking.prompt_tokens is None
        assert ranking.completion_tokens is None
        assert ranking.total_tokens is None
        assert ranking.reasoning_tokens is None
        assert ranking.cached_tokens is None
        assert ranking.cost_credits is None
        assert ranking.generation_id is None

    @pytest.mark.asyncio
    async def test_aggregate_ranking_usage_statistics(
        self, 
        db_session: AsyncSession, 
        sample_task_data, 
        sample_evaluation_data,
        sample_ranking_data,
        sample_ranking_without_usage
    ):
        """Test aggregating usage statistics across multiple rankings."""
        # Create task and evaluation
        task_create = TaskCreate(**sample_task_data)
        task = await create_task(db_session, task_in=task_create)
        # create_evaluation doesn't use schemas, it takes direct parameters
        evaluation = await create_evaluation(
            db_session, 
            task_id=task.id,
            evaluation_used_blind_ids=True,
            evaluation_prompt=None
        )
        
        # Use bulk_create_rankings as it's the proper way to create rankings
        from app.crud.crud_ranking import bulk_create_rankings
        
        # Prepare ranking data for bulk creation
        rankings_data = [
            # First ranking with usage stats
            {
                **{k: v for k, v in sample_ranking_data.items() if k != "evaluation_id"},
            },
            # Second ranking with different usage stats
            {
                "evaluator_model_id": "anthropic/claude-3-opus",
                "ranked_list_json": [1, 2, 3],
                "reasoning_text": "Based on quality and accuracy...",
                "prompt_tokens": 300,
                "completion_tokens": 150,
                "total_tokens": 450,
                "reasoning_tokens": 100,
                "cached_tokens": 75,
                "cost_credits": 0.004500,
                "generation_id": "eval-789456"
            },
            # Third ranking without usage stats
            {
                **{k: v for k, v in sample_ranking_without_usage.items() if k != "evaluation_id"},
            }
        ]
        
        # Create all rankings at once
        await bulk_create_rankings(db_session, evaluation_id=evaluation.id, rankings_data=rankings_data)
        
        # Get all rankings for evaluation using direct query
        from sqlalchemy import select
        result = await db_session.execute(
            select(Ranking).filter(Ranking.evaluation_id == evaluation.id)
        )
        all_rankings = result.scalars().all()
        
        # Filter rankings with usage statistics
        rankings_with_usage = [
            ranking for ranking in all_rankings 
            if ranking.generation_id is not None
        ]
        
        # Calculate aggregated usage statistics
        total_prompt_tokens = sum(r.prompt_tokens or 0 for r in rankings_with_usage)
        total_completion_tokens = sum(r.completion_tokens or 0 for r in rankings_with_usage)
        total_tokens = sum(r.total_tokens or 0 for r in rankings_with_usage)
        total_reasoning_tokens = sum(r.reasoning_tokens or 0 for r in rankings_with_usage)
        total_cached_tokens = sum(r.cached_tokens or 0 for r in rankings_with_usage)
        total_cost_credits = sum(r.cost_credits or 0 for r in rankings_with_usage)
        
        assert len(all_rankings) == 3
        assert len(rankings_with_usage) == 2
        assert total_prompt_tokens == 500  # 200 + 300
        assert total_completion_tokens == 250  # 100 + 150
        assert total_tokens == 750  # 300 + 450
        assert total_reasoning_tokens == 175  # 75 + 100
        assert total_cached_tokens == 125  # 50 + 75
        assert total_cost_credits == 0.007500  # 0.003000 + 0.004500


class TestUsageStatisticsEdgeCases:
    """Test edge cases and error conditions for usage statistics."""

    @pytest.mark.asyncio
    async def test_zero_cost_generation(
        self, 
        db_session: AsyncSession, 
        sample_task_data, 
        sample_generation_data
    ):
        """Test generation with zero cost (free tier models)."""
        # Create task
        task_create = TaskCreate(**sample_task_data)
        task = await create_task(db_session, task_in=task_create)
        
        # Create generation with zero cost
        generation_data = {
            **sample_generation_data,
            "task_id": task.id,
            "cost_credits": 0.0
        }
        generation_create = GenerationCreate(**generation_data)
        generation = await create_generation(db_session, generation_in=generation_create)
        
        assert generation.cost_credits == 0.0
        assert generation.prompt_tokens == 100
        assert generation.total_tokens == 250

    @pytest.mark.asyncio
    async def test_large_token_counts(
        self, 
        db_session: AsyncSession, 
        sample_task_data, 
        sample_generation_data
    ):
        """Test generation with very large token counts."""
        # Create task
        task_create = TaskCreate(**sample_task_data)
        task = await create_task(db_session, task_in=task_create)
        
        # Create generation with large token counts
        generation_data = {
            **sample_generation_data,
            "task_id": task.id,
            "prompt_tokens": 999999,
            "completion_tokens": 999999,
            "total_tokens": 1999998,
            "cost_credits": 999.999999
        }
        generation_create = GenerationCreate(**generation_data)
        generation = await create_generation(db_session, generation_in=generation_create)
        
        assert generation.prompt_tokens == 999999
        assert generation.completion_tokens == 999999
        assert generation.total_tokens == 1999998
        assert generation.cost_credits == 999.999999

    @pytest.mark.asyncio
    async def test_precision_of_cost_credits(
        self, 
        db_session: AsyncSession, 
        sample_task_data, 
        sample_generation_data
    ):
        """Test precision handling of cost credits."""
        # Create task
        task_create = TaskCreate(**sample_task_data)
        task = await create_task(db_session, task_in=task_create)
        
        # Create generation with high precision cost
        generation_data = {
            **sample_generation_data,
            "task_id": task.id,
            "cost_credits": 0.000001  # Very small cost
        }
        generation_create = GenerationCreate(**generation_data)
        generation = await create_generation(db_session, generation_in=generation_create)
        
        assert generation.cost_credits == 0.000001

    @pytest.mark.asyncio
    async def test_partial_usage_statistics(
        self, 
        db_session: AsyncSession, 
        sample_task_data, 
        sample_generation_data
    ):
        """Test generation with only partial usage statistics."""
        # Create task
        task_create = TaskCreate(**sample_task_data)
        task = await create_task(db_session, task_in=task_create)
        
        # Create generation with only some usage stats
        generation_data = {
            **sample_generation_data,
            "task_id": task.id,
            "prompt_tokens": 100,
            "completion_tokens": 150,
            "total_tokens": 250,
            "reasoning_tokens": None,  # Not available
            "cached_tokens": None,     # Not available
            "cost_credits": 0.002500,
            "generation_id": "gen-partial-123"
        }
        generation_create = GenerationCreate(**generation_data)
        generation = await create_generation(db_session, generation_in=generation_create)
        
        assert generation.prompt_tokens == 100
        assert generation.completion_tokens == 150
        assert generation.total_tokens == 250
        assert generation.reasoning_tokens is None
        assert generation.cached_tokens is None
        assert generation.cost_credits == 0.002500
        assert generation.generation_id == "gen-partial-123"