"""Tests for the blob storage service."""
import pytest
import tempfile
import shutil
from pathlib import Path
from app.services.blob_storage import BlobStorageService


class TestBlobStorageService:
    """Test cases for BlobStorageService."""
    
    @pytest.fixture
    def storage_service(self):
        """Create a BlobStorageService instance with temporary storage."""
        # Use temporary directory for tests
        temp_dir = tempfile.mkdtemp()
        service = BlobStorageService(storage_root=temp_dir)
        yield service
        # Cleanup
        shutil.rmtree(temp_dir, ignore_errors=True)
    
    @pytest.mark.asyncio
    async def test_initialization(self, storage_service):
        """Test that BlobStorageService initializes correctly."""
        assert storage_service.storage_root.exists()
        assert storage_service.storage_root.is_dir()
    
    @pytest.mark.asyncio
    async def test_store_blob(self, storage_service):
        """Test storing a blob."""
        content = b"Hello, this is test content for blob storage!"
        
        result = await storage_service.store_blob(content)
        
        assert 'storage_key' in result
        assert 'content_hash' in result
        assert result['uncompressed_size'] == len(content)
        assert result['compressed_size'] > 0  # Should have a size
        assert result['compression_type'] == 'zlib'
        # Note: For small content, compression might not reduce size
        
        # Verify file exists
        storage_path = storage_service._generate_storage_path(result['storage_key'])
        assert storage_path.exists()
    
    @pytest.mark.asyncio
    async def test_retrieve_blob(self, storage_service):
        """Test retrieving a stored blob."""
        original_content = b"This is the content to store and retrieve"
        
        # Store blob
        store_result = await storage_service.store_blob(original_content)
        storage_key = store_result['storage_key']
        
        # Retrieve blob
        retrieved_content = await storage_service.retrieve_blob(storage_key)
        
        assert retrieved_content == original_content
    
    @pytest.mark.asyncio
    async def test_store_duplicate_blob(self, storage_service):
        """Test storing the same content twice."""
        content = b"Duplicate content test"
        
        # Store once
        result1 = await storage_service.store_blob(content)
        
        # Store again
        result2 = await storage_service.store_blob(content)
        
        # Should have same hash
        assert result1['storage_key'] == result2['storage_key']
        assert result2.get('already_existed') is True
    
    @pytest.mark.asyncio
    async def test_delete_blob(self, storage_service):
        """Test deleting a blob."""
        content = b"Content to be deleted"
        
        # Store blob
        result = await storage_service.store_blob(content)
        storage_key = result['storage_key']
        
        # Verify it exists
        assert await storage_service.blob_exists(storage_key)
        
        # Delete blob
        deleted = await storage_service.delete_blob(storage_key)
        assert deleted is True
        
        # Verify it's gone
        assert not await storage_service.blob_exists(storage_key)
    
    @pytest.mark.asyncio
    async def test_retrieve_nonexistent_blob(self, storage_service):
        """Test retrieving a blob that doesn't exist."""
        fake_key = "nonexistent_hash_key"
        
        with pytest.raises(FileNotFoundError):
            await storage_service.retrieve_blob(fake_key)
    
    @pytest.mark.asyncio
    async def test_large_content(self, storage_service):
        """Test storing and retrieving large content."""
        # Create 1MB of content
        large_content = b"x" * (1024 * 1024)
        
        # Store
        result = await storage_service.store_blob(large_content)
        
        assert result['uncompressed_size'] == len(large_content)
        # Compression should work well on repetitive content
        assert result['compressed_size'] < len(large_content) * 0.1
        
        # Retrieve
        retrieved = await storage_service.retrieve_blob(result['storage_key'])
        assert retrieved == large_content
    
    @pytest.mark.asyncio
    async def test_blob_info(self, storage_service):
        """Test getting blob information."""
        content = b"Info test content"
        
        # Store blob
        result = await storage_service.store_blob(content)
        storage_key = result['storage_key']
        
        # Get info
        info = await storage_service.get_blob_info(storage_key)
        
        assert info is not None
        assert info['storage_key'] == storage_key
        assert 'compressed_size' in info
        assert 'created_at' in info
    
    @pytest.mark.asyncio
    async def test_storage_stats(self, storage_service):
        """Test getting storage statistics."""
        # Store some blobs
        contents = [
            b"Content 1",
            b"Content 2 with more data",
            b"Content 3 with even more data than before"
        ]
        
        for content in contents:
            await storage_service.store_blob(content)
        
        # Get stats
        stats = storage_service.get_storage_stats()
        
        assert stats['total_blobs'] == 3
        assert stats['total_size_bytes'] > 0
        assert stats['total_size_mb'] >= 0
    
    @pytest.mark.asyncio
    async def test_cleanup_orphaned_blobs(self, storage_service):
        """Test cleaning up orphaned blobs."""
        # Store some blobs
        content1 = b"Keep this content"
        content2 = b"Delete this content"
        content3 = b"Also delete this"
        
        result1 = await storage_service.store_blob(content1)
        result2 = await storage_service.store_blob(content2)
        result3 = await storage_service.store_blob(content3)
        
        # Define active keys (only keep first one)
        active_keys = {result1['storage_key']}
        
        # Run cleanup
        cleanup_stats = await storage_service.cleanup_orphaned_blobs(active_keys)
        
        assert cleanup_stats['deleted_blobs'] == 2
        assert cleanup_stats['reclaimed_bytes'] > 0
        
        # Verify correct blobs remain
        assert await storage_service.blob_exists(result1['storage_key'])
        assert not await storage_service.blob_exists(result2['storage_key'])
        assert not await storage_service.blob_exists(result3['storage_key'])
    
    @pytest.mark.asyncio
    async def test_hierarchical_storage(self, storage_service):
        """Test that blobs are stored in hierarchical directory structure."""
        content = b"Hierarchical test"
        
        result = await storage_service.store_blob(content)
        storage_key = result['storage_key']
        
        # Check directory structure
        storage_path = storage_service._generate_storage_path(storage_key)
        
        # Should have two levels of directories
        assert storage_path.parent.parent != storage_service.storage_root
        assert len(storage_path.parent.name) == 2  # Second level dir
        assert len(storage_path.parent.parent.name) == 2  # First level dir