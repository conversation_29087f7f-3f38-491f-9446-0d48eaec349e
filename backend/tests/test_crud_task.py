import pytest
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import delete

from app.crud import crud_task
from app.schemas import task as task_schema
from app.db import models


@pytest.mark.asyncio
class TestTaskCRUD:
    """Test cases for task CRUD operations."""

    async def test_create_task_basic(self, db_session: AsyncSession):
        """Test creating a basic task."""
        task_data = task_schema.TaskCreate(
            prompt="Test prompt",
            system_prompt="Test system prompt"
        )
        
        created_task = await crud_task.create_task(db_session, task_data)
        
        assert created_task.id is not None
        assert created_task.prompt == "Test prompt"
        assert created_task.system_prompt == "Test system prompt"
        assert created_task.status == task_schema.TaskStatusEnum.PENDING
        assert created_task.created_at is not None

    async def test_create_task_without_system_prompt(self, db_session: AsyncSession):
        """Test creating a task without system prompt."""
        task_data = task_schema.TaskCreate(
            prompt="Test prompt without system prompt"
        )
        
        created_task = await crud_task.create_task(db_session, task_data)
        
        assert created_task.id is not None
        assert created_task.prompt == "Test prompt without system prompt"
        assert created_task.system_prompt is None
        assert created_task.status == task_schema.TaskStatusEnum.PENDING

    async def test_get_task_existing(self, db_session: AsyncSession):
        """Test retrieving an existing task."""
        # Create a task first
        task_data = task_schema.TaskCreate(prompt="Test prompt")
        created_task = await crud_task.create_task(db_session, task_data)
        
        # Retrieve the task
        retrieved_task = await crud_task.get_task(db_session, created_task.id)
        
        assert retrieved_task is not None
        assert retrieved_task.id == created_task.id
        assert retrieved_task.prompt == created_task.prompt
        assert retrieved_task.status == created_task.status

    async def test_get_task_nonexistent(self, db_session: AsyncSession):
        """Test retrieving a non-existent task."""
        retrieved_task = await crud_task.get_task(db_session, 99999)
        assert retrieved_task is None

    async def test_get_task_with_generations(self, db_session: AsyncSession):
        """Test retrieving a task with its generations."""
        # Create a task
        task_data = task_schema.TaskCreate(prompt="Test prompt")
        created_task = await crud_task.create_task(db_session, task_data)
        
        # Create a generation for the task
        generation = models.Generation(
            task_id=created_task.id,
            model_id_used="test/model",
            blind_id="blind-123",
            output_text="Test output",
            error_message=None
        )
        db_session.add(generation)
        await db_session.commit()
        
        # Retrieve task with generations
        task_with_generations = await crud_task.get_task_with_generations(db_session, created_task.id)
        
        assert task_with_generations is not None
        assert len(task_with_generations.generations) == 1
        assert task_with_generations.generations[0].output_text == "Test output"

    async def test_get_task_with_evaluations(self, db_session: AsyncSession):
        """Test retrieving a task with its evaluations and rankings."""
        # Create a task
        task_data = task_schema.TaskCreate(prompt="Test prompt")
        created_task = await crud_task.create_task(db_session, task_data)
        
        # Create an evaluation for the task
        evaluation = models.Evaluation(
            task_id=created_task.id,
            status=task_schema.TaskStatusEnum.COMPLETED,
            evaluation_used_blind_ids=True
        )
        db_session.add(evaluation)
        await db_session.commit()
        await db_session.refresh(evaluation)
        
        # Create a ranking for the evaluation
        ranking = models.Ranking(
            evaluation_id=evaluation.id,
            evaluator_model_id="evaluator/model",
            ranked_list_json=[1, 2, 3],
            reasoning_text="Test reasoning"
        )
        db_session.add(ranking)
        await db_session.commit()
        
        # Retrieve task with evaluations
        task_with_evaluations = await crud_task.get_task_with_evaluations(db_session, created_task.id)
        
        assert task_with_evaluations is not None
        assert len(task_with_evaluations.evaluations) == 1
        assert len(task_with_evaluations.evaluations[0].rankings) == 1
        assert task_with_evaluations.evaluations[0].rankings[0].reasoning_text == "Test reasoning"

    async def test_get_task_with_details(self, db_session: AsyncSession):
        """Test retrieving a task with both generations and evaluations."""
        # Create a task
        task_data = task_schema.TaskCreate(prompt="Test prompt")
        created_task = await crud_task.create_task(db_session, task_data)
        
        # Create a generation
        generation = models.Generation(
            task_id=created_task.id,
            model_id_used="test/model",
            blind_id="blind-123",
            output_text="Test output",
            error_message=None
        )
        db_session.add(generation)
        
        # Create an evaluation
        evaluation = models.Evaluation(
            task_id=created_task.id,
            status=task_schema.TaskStatusEnum.COMPLETED,
            evaluation_used_blind_ids=True
        )
        db_session.add(evaluation)
        await db_session.commit()
        await db_session.refresh(evaluation)
        
        # Create a ranking
        ranking = models.Ranking(
            evaluation_id=evaluation.id,
            evaluator_model_id="evaluator/model",
            ranked_list_json=[1, 2, 3],
            reasoning_text="Test reasoning"
        )
        db_session.add(ranking)
        await db_session.commit()
        
        # Retrieve task with full details
        task_with_details = await crud_task.get_task_with_details(db_session, created_task.id)
        
        assert task_with_details is not None
        assert len(task_with_details.generations) == 1
        assert len(task_with_details.evaluations) == 1
        assert len(task_with_details.evaluations[0].rankings) == 1

    async def test_get_tasks_for_history(self, db_session: AsyncSession):
        """Test retrieving tasks for history display."""
        # Create multiple tasks
        tasks_data = [
            task_schema.TaskCreate(prompt=f"Test prompt {i}")
            for i in range(5)
        ]
        
        created_tasks = []
        for task_data in tasks_data:
            task = await crud_task.create_task(db_session, task_data)
            created_tasks.append(task)
        
        # Retrieve tasks for history
        history_tasks = await crud_task.get_tasks_for_history(db_session, skip=0, limit=10)
        
        assert len(history_tasks) == 5
        # Should be ordered by created_at desc (newest first)
        # Check that we have the expected fields
        for task_row in history_tasks:
            assert hasattr(task_row, 'id')
            assert hasattr(task_row, 'prompt')
            assert hasattr(task_row, 'status')
            assert hasattr(task_row, 'created_at')

    async def test_get_tasks_for_history_pagination(self, db_session: AsyncSession):
        """Test pagination in get_tasks_for_history."""
        # Create multiple tasks
        for i in range(10):
            task_data = task_schema.TaskCreate(prompt=f"Test prompt {i}")
            await crud_task.create_task(db_session, task_data)
        
        # Test first page
        first_page = await crud_task.get_tasks_for_history(db_session, skip=0, limit=5)
        assert len(first_page) == 5
        
        # Test second page
        second_page = await crud_task.get_tasks_for_history(db_session, skip=5, limit=5)
        assert len(second_page) == 5
        
        # Ensure no overlap
        first_page_ids = [task.id for task in first_page]
        second_page_ids = [task.id for task in second_page]
        assert len(set(first_page_ids) & set(second_page_ids)) == 0

    async def test_update_task_status(self, db_session: AsyncSession):
        """Test updating task status."""
        # Create a task
        task_data = task_schema.TaskCreate(prompt="Test prompt")
        created_task = await crud_task.create_task(db_session, task_data)
        
        assert created_task.status == task_schema.TaskStatusEnum.PENDING
        
        # Update status
        updated_task = await crud_task.update_task_status(
            db_session, 
            created_task.id, 
            task_schema.TaskStatusEnum.COMPLETED
        )
        
        assert updated_task is not None
        assert updated_task.status == task_schema.TaskStatusEnum.COMPLETED
        assert updated_task.id == created_task.id

    async def test_update_task_status_nonexistent(self, db_session: AsyncSession):
        """Test updating status of non-existent task."""
        updated_task = await crud_task.update_task_status(
            db_session, 
            99999, 
            task_schema.TaskStatusEnum.COMPLETED
        )
        
        assert updated_task is None

    async def test_delete_task_recursive_basic(self, db_session: AsyncSession):
        """Test deleting a task without related data."""
        # Create a task
        task_data = task_schema.TaskCreate(prompt="Test prompt")
        created_task = await crud_task.create_task(db_session, task_data)
        task_id = created_task.id
        
        # Delete the task
        result = await crud_task.delete_task_recursive(db_session, task_id)
        
        assert result is True
        
        # Verify task is deleted
        deleted_task = await crud_task.get_task(db_session, task_id)
        assert deleted_task is None

    async def test_delete_task_recursive_with_generations(self, db_session: AsyncSession):
        """Test deleting a task with generations."""
        # Create a task
        task_data = task_schema.TaskCreate(prompt="Test prompt")
        created_task = await crud_task.create_task(db_session, task_data)
        task_id = created_task.id
        
        # Create generations
        generations = []
        for i in range(3):
            generation = models.Generation(
                task_id=task_id,
                model_id_used=f"test/model-{i}",
                blind_id=f"blind-{i}",
                output_text=f"Test output {i}",
                error_message=None
            )
            db_session.add(generation)
            generations.append(generation)
        
        await db_session.commit()
        generation_ids = [gen.id for gen in generations]
        
        # Delete the task
        result = await crud_task.delete_task_recursive(db_session, task_id)
        
        assert result is True
        
        # Verify task and generations are deleted
        deleted_task = await crud_task.get_task(db_session, task_id)
        assert deleted_task is None
        
        # Check that generations are deleted
        for gen_id in generation_ids:
            result = await db_session.execute(
                select(models.Generation).where(models.Generation.id == gen_id)
            )
            assert result.scalar_one_or_none() is None

    async def test_delete_task_recursive_with_evaluations_and_rankings(self, db_session: AsyncSession):
        """Test deleting a task with evaluations and rankings."""
        # Create a task
        task_data = task_schema.TaskCreate(prompt="Test prompt")
        created_task = await crud_task.create_task(db_session, task_data)
        task_id = created_task.id
        
        # Create an evaluation
        evaluation = models.Evaluation(
            task_id=task_id,
            status=task_schema.TaskStatusEnum.COMPLETED,
            evaluation_used_blind_ids=True
        )
        db_session.add(evaluation)
        await db_session.commit()
        await db_session.refresh(evaluation)
        evaluation_id = evaluation.id
        
        # Create rankings
        rankings = []
        for i in range(2):
            ranking = models.Ranking(
                evaluation_id=evaluation_id,
                evaluator_model_id=f"evaluator/model-{i}",
                ranked_list_json=[1, 2, 3],
                reasoning_text=f"Test reasoning {i}"
            )
            db_session.add(ranking)
            rankings.append(ranking)
        
        await db_session.commit()
        ranking_ids = [rank.id for rank in rankings]
        
        # Delete the task
        result = await crud_task.delete_task_recursive(db_session, task_id)
        
        assert result is True
        
        # Verify everything is deleted
        deleted_task = await crud_task.get_task(db_session, task_id)
        assert deleted_task is None
        
        # Check evaluation is deleted
        result = await db_session.execute(
            select(models.Evaluation).where(models.Evaluation.id == evaluation_id)
        )
        assert result.scalar_one_or_none() is None
        
        # Check rankings are deleted
        for rank_id in ranking_ids:
            result = await db_session.execute(
                select(models.Ranking).where(models.Ranking.id == rank_id)
            )
            assert result.scalar_one_or_none() is None

    async def test_delete_task_recursive_complete_scenario(self, db_session: AsyncSession):
        """Test deleting a task with all types of related data."""
        # Create a task
        task_data = task_schema.TaskCreate(prompt="Test prompt")
        created_task = await crud_task.create_task(db_session, task_data)
        task_id = created_task.id
        
        # Create generations
        generation = models.Generation(
            task_id=task_id,
            model_id_used="test/model",
            blind_id="blind-123",
            output_text="Test output",
            error_message=None
        )
        db_session.add(generation)
        
        # Create evaluation
        evaluation = models.Evaluation(
            task_id=task_id,
            status=task_schema.TaskStatusEnum.COMPLETED,
            evaluation_used_blind_ids=True
        )
        db_session.add(evaluation)
        await db_session.commit()
        await db_session.refresh(evaluation)
        
        # Create ranking
        ranking = models.Ranking(
            evaluation_id=evaluation.id,
            evaluator_model_id="evaluator/model",
            ranked_list_json=[1, 2, 3],
            reasoning_text="Test reasoning"
        )
        db_session.add(ranking)
        await db_session.commit()
        
        generation_id = generation.id
        evaluation_id = evaluation.id
        ranking_id = ranking.id
        
        # Delete the task
        result = await crud_task.delete_task_recursive(db_session, task_id)
        
        assert result is True
        
        # Verify all data is deleted
        assert await crud_task.get_task(db_session, task_id) is None
        
        # Check generation
        result = await db_session.execute(
            select(models.Generation).where(models.Generation.id == generation_id)
        )
        assert result.scalar_one_or_none() is None
        
        # Check evaluation
        result = await db_session.execute(
            select(models.Evaluation).where(models.Evaluation.id == evaluation_id)
        )
        assert result.scalar_one_or_none() is None
        
        # Check ranking
        result = await db_session.execute(
            select(models.Ranking).where(models.Ranking.id == ranking_id)
        )
        assert result.scalar_one_or_none() is None

    async def test_delete_task_recursive_nonexistent(self, db_session: AsyncSession):
        """Test deleting a non-existent task."""
        result = await crud_task.delete_task_recursive(db_session, 99999)
        assert result is False

    async def test_task_creation_with_long_prompt(self, db_session: AsyncSession):
        """Test creating a task with a very long prompt."""
        long_prompt = "A" * 10000  # Very long prompt
        task_data = task_schema.TaskCreate(prompt=long_prompt)
        
        created_task = await crud_task.create_task(db_session, task_data)
        
        assert created_task.id is not None
        assert created_task.prompt == long_prompt
        assert len(created_task.prompt) == 10000

    async def test_task_creation_with_special_characters(self, db_session: AsyncSession):
        """Test creating a task with special characters in prompt."""
        special_prompt = "Test with émojis 🚀 and special chars: @#$%^&*()[]{}|\\:;\"'<>,.?/~`"
        task_data = task_schema.TaskCreate(
            prompt=special_prompt,
            system_prompt="System with special chars: ñáéíóú"
        )
        
        created_task = await crud_task.create_task(db_session, task_data)
        
        assert created_task.id is not None
        assert created_task.prompt == special_prompt
        assert created_task.system_prompt == "System with special chars: ñáéíóú" 