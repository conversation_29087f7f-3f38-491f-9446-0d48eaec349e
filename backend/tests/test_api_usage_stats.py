import pytest
from unittest.mock import patch, AsyncMock, MagicMock
from httpx import AsyncClient, ASGITransport
from app.main import app
from app.db.models import Task, Generation, Evaluation, Ranking
import asyncio

@pytest.mark.asyncio
@patch('app.services.llm_service.generate_outputs_for_task_background')
async def test_task_creation_with_usage_stats(mock_generate):
    """Test creating a task and checking usage statistics via API."""
    # Mock the background task to avoid actual LLM calls
    mock_generate.return_value = None
    
    async with AsyncClient(transport=ASGITransport(app=app), base_url="http://test", timeout=30.0) as client:
        # Create a task
        task_data = {
            "prompt": "Test prompt for usage statistics",
            "system_prompt": "You are a test assistant"
        }
        response = await client.post("/api/v1/tasks", json=task_data)
        assert response.status_code == 202  # Task creation is async
        task = response.json()
        assert task["task_id"] is not None
        assert task["status"] == "PENDING"
        assert task["message"] == "Task created and generation started."

# Test removed - moved to test_api_usage_stats_separate.py to avoid event loop issues
        
@pytest.mark.asyncio
@patch('app.services.llm_service.generate_outputs_for_task_background')
async def test_get_task_with_usage_stats(mock_generate):
    """Test retrieving task with usage statistics."""
    mock_generate.return_value = None
    
    async with AsyncClient(transport=ASGITransport(app=app), base_url="http://test", timeout=30.0) as client:
        # Create a task first
        task_data = {
            "prompt": "Test prompt",
            "system_prompt": "Test system"
        }
        create_response = await client.post("/api/v1/tasks", json=task_data)
        assert create_response.status_code == 202
        task_id = create_response.json()["task_id"]
        
        # Get task status (includes generations and evaluations)
        get_response = await client.get(f"/api/v1/tasks/{task_id}/status")
        assert get_response.status_code == 200
        task_details = get_response.json()
        
        # Check that usage stats fields are present in generations
        assert "generations" in task_details
        # When generations exist, they should have usage stat fields
        for output in task_details.get("generations", []):
            # These fields should exist even if None
            assert "prompt_tokens" in output
            assert "completion_tokens" in output
            assert "total_tokens" in output
            assert "cost_credits" in output
            assert "generation_id" in output

# Test removed - moved to test_api_usage_stats_separate.py to avoid event loop issues