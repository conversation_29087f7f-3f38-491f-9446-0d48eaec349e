"""Tests for archive extraction service."""
import os
import tempfile
import zipfile
import tarfile
import pytest
from app.services.archive_extractor import ArchiveExtractor


@pytest.mark.asyncio
class TestArchiveExtractor:
    """Test archive extraction functionality."""
    
    @pytest.fixture
    def extractor(self):
        """Create archive extractor instance."""
        return ArchiveExtractor()
    
    @pytest.fixture
    def temp_files(self):
        """Create temporary test files."""
        with tempfile.TemporaryDirectory() as tmpdir:
            # Create test files
            test_files = {
                'file1.py': 'print("Hello World")',
                'subdir/file2.txt': 'Test content',
                'data.json': '{"key": "value"}'
            }
            
            for path, content in test_files.items():
                full_path = os.path.join(tmpdir, path)
                os.makedirs(os.path.dirname(full_path), exist_ok=True)
                with open(full_path, 'w') as f:
                    f.write(content)
            
            yield tmpdir, test_files
    
    async def test_extract_zip(self, extractor, temp_files):
        """Test ZIP archive extraction."""
        source_dir, files = temp_files
        
        # Create ZIP archive
        zip_path = os.path.join(source_dir, 'test.zip')
        with zipfile.ZipFile(zip_path, 'w') as zf:
            for file_path in files.keys():
                zf.write(os.path.join(source_dir, file_path), file_path)
        
        # Extract archive
        extracted_path = await extractor.extract_archive(zip_path)
        
        # Verify extraction
        for file_path, content in files.items():
            extracted_file = os.path.join(extracted_path, file_path)
            assert os.path.exists(extracted_file)
            with open(extracted_file, 'r') as f:
                assert f.read() == content
        
        # Cleanup
        extractor.cleanup_directory(extracted_path)
    
    async def test_extract_tar_gz(self, extractor, temp_files):
        """Test TAR.GZ archive extraction."""
        source_dir, files = temp_files
        
        # Create TAR.GZ archive
        tar_path = os.path.join(source_dir, 'test.tar.gz')
        with tarfile.open(tar_path, 'w:gz') as tf:
            for file_path in files.keys():
                tf.add(os.path.join(source_dir, file_path), file_path)
        
        # Extract archive
        extracted_path = await extractor.extract_archive(tar_path)
        
        # Verify extraction
        for file_path, content in files.items():
            extracted_file = os.path.join(extracted_path, file_path)
            assert os.path.exists(extracted_file)
            with open(extracted_file, 'r') as f:
                assert f.read() == content
    
    async def test_single_directory_extraction(self, extractor):
        """Test extraction when archive contains single root directory."""
        with tempfile.TemporaryDirectory() as tmpdir:
            # Create structure with single root dir
            root_dir = os.path.join(tmpdir, 'project')
            os.makedirs(root_dir)
            
            test_file = os.path.join(root_dir, 'test.py')
            with open(test_file, 'w') as f:
                f.write('print("test")')
            
            # Create ZIP with single root directory
            zip_path = os.path.join(tmpdir, 'project.zip')
            with zipfile.ZipFile(zip_path, 'w') as zf:
                zf.write(root_dir, 'project')
                zf.write(test_file, 'project/test.py')
            
            # Extract - should return the single root directory
            extracted_path = await extractor.extract_archive(zip_path)
            
            # Should point to the 'project' directory
            assert os.path.basename(extracted_path) == 'project'
            assert os.path.exists(os.path.join(extracted_path, 'test.py'))
    
    async def test_dangerous_path_rejection(self, extractor):
        """Test rejection of archives with dangerous paths."""
        with tempfile.TemporaryDirectory() as tmpdir:
            # Create ZIP with dangerous path
            zip_path = os.path.join(tmpdir, 'dangerous.zip')
            with zipfile.ZipFile(zip_path, 'w') as zf:
                # Try to write with absolute path
                zf.writestr('/etc/passwd', 'malicious content')
            
            # Should raise error
            with pytest.raises(Exception, match="Failed to extract archive"):
                await extractor.extract_archive(zip_path)
    
    async def test_unsupported_format(self, extractor):
        """Test handling of unsupported archive formats."""
        with tempfile.TemporaryDirectory() as tmpdir:
            # Create file with unsupported extension
            bad_file = os.path.join(tmpdir, 'test.rar')
            with open(bad_file, 'w') as f:
                f.write('not an archive')
            
            # Should raise error
            with pytest.raises(ValueError, match="Unsupported archive format"):
                await extractor.extract_archive(bad_file)
    
    async def test_file_extension_detection(self, extractor):
        """Test correct detection of file extensions."""
        assert extractor._get_file_extension('file.zip') == '.zip'
        assert extractor._get_file_extension('file.tar.gz') == '.tar.gz'
        assert extractor._get_file_extension('file.TAR.GZ') == '.tar.gz'
        assert extractor._get_file_extension('path/to/file.tar.bz2') == '.tar.bz2'
        assert extractor._get_file_extension('file.txt') == '.txt'