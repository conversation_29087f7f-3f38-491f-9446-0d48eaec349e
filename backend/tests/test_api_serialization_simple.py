"""Simple API serialization test without database dependencies."""
import pytest
from unittest.mock import AsyncMock
from uuid import uuid4
from datetime import datetime

from app.api.endpoints.agent_evaluations import (
    create_evaluation_task,
    list_evaluation_tasks,
    get_evaluation_task
)
from app.schemas.agent_evaluation import AgentEvaluationTaskCreate
from app.models.agent_evaluation import AgentEvaluationTask


@pytest.mark.asyncio
async def test_create_task_endpoint_response_model():
    """Test that create task endpoint properly serializes the response."""
    # Mock database session
    mock_db = AsyncMock()
    
    # Mock CRUD operations
    mock_task = AgentEvaluationTask(
        id=str(uuid4()),
        name="Test Task",
        description="Test description",
        evaluation_type="0-1",
        prompt="Test prompt",
        status="draft",
        created_at=datetime.utcnow(),
        updated_at=datetime.utcnow()
    )
    
    # Mock the crud_task.create method
    from app.crud.agent_evaluation import agent_task as crud_task
    crud_task.create = AsyncMock(return_value=mock_task)
    
    # Create task input
    task_input = AgentEvaluationTaskCreate(
        name="Test Task",
        description="Test description",
        evaluation_type="0-1",
        prompt="Test prompt"
    )
    
    # Call the endpoint function directly
    result = await create_evaluation_task(db=mock_db, task_in=task_input)
    
    # Verify the result is the SQLAlchemy model
    assert isinstance(result, AgentEvaluationTask)
    assert result.name == "Test Task"
    
    # The response_model decorator on the endpoint ensures this gets serialized
    # to AgentEvaluationTaskInDB when returned via FastAPI


@pytest.mark.asyncio 
async def test_list_tasks_endpoint_response_model():
    """Test that list tasks endpoint properly serializes the response."""
    # Mock database session
    mock_db = AsyncMock()
    
    # Mock CRUD operations
    mock_tasks = [
        AgentEvaluationTask(
            id=str(uuid4()),
            name=f"Test Task {i}",
            description=f"Test description {i}",
            evaluation_type="0-1",
            prompt=f"Test prompt {i}",
            status="draft",
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )
        for i in range(3)
    ]
    
    # Mock the crud_task.get_multi method
    from app.crud.agent_evaluation import agent_task as crud_task
    crud_task.get_multi = AsyncMock(return_value=mock_tasks)
    
    # Call the endpoint function directly
    result = await list_evaluation_tasks(db=mock_db)
    
    # Verify the result is a list of SQLAlchemy models
    assert isinstance(result, list)
    assert len(result) == 3
    assert all(isinstance(task, AgentEvaluationTask) for task in result)
    
    # The response_model decorator on the endpoint ensures this gets serialized
    # to List[AgentEvaluationTaskInDB] when returned via FastAPI


@pytest.mark.asyncio
async def test_response_model_prevents_serialization_error():
    """
    This test demonstrates why response_model is critical.
    Without response_model, FastAPI can't serialize SQLAlchemy models.
    """
    # The actual endpoints have response_model decorators like:
    # @router.post("/tasks", response_model=AgentEvaluationTaskInDB)
    # @router.get("/tasks", response_model=List[AgentEvaluationTaskInDB])
    
    # These decorators ensure that:
    # 1. SQLAlchemy models are converted to Pydantic models
    # 2. Internal attributes like _sa_instance_state are not exposed
    # 3. The API returns clean JSON
    
    # Without response_model, you would get:
    # "Unable to serialize unknown type: <class 'app.models.agent_evaluation.AgentEvaluationTask'>"
    
    assert True  # This test is for documentation purposes