"""Tests for the token estimation service."""
import pytest
from app.services.token_estimator import TokenEstimator


class TestTokenEstimator:
    """Test cases for TokenEstimator."""
    
    @pytest.fixture
    def estimator(self):
        """Create a TokenEstimator instance."""
        return TokenEstimator()
    
    def test_initialization(self, estimator):
        """Test that TokenEstimator initializes correctly."""
        assert estimator.encoding is not None
        assert len(estimator.multipliers) > 0
        assert estimator.multipliers['.py'] == 0.28
        assert estimator.multipliers['.json'] == 0.35
    
    def test_exact_token_count_small_file(self, estimator):
        """Test exact token counting for small files."""
        content = "def hello_world():\n    print('Hello, World!')"
        tokens, details = estimator.estimate_tokens(content, "test.py")
        
        assert details['method'] == 'exact'
        assert details['characters'] == len(content)
        assert details['tokens'] > 0
        assert details['file_extension'] == '.py'
        
        # Verify token count is reasonable
        # This simple function should be around 10-15 tokens
        assert 5 < details['tokens'] < 20
    
    def test_estimated_tokens_large_file(self, estimator):
        """Test token estimation for large files."""
        # Create a large Python file content
        content = "def function():\n    pass\n" * 1000  # ~20KB
        tokens, details = estimator.estimate_tokens(content, "large.py")
        
        assert details['method'] == 'estimated'
        assert details['characters'] == len(content)
        assert details['tokens'] > 0
        assert details['multiplier'] == 0.28
        
        # Rough check: tokens should be less than characters
        assert details['tokens'] < details['characters']
    
    def test_different_file_types(self, estimator):
        """Test token estimation for different file types."""
        test_cases = [
            ("test.py", "print('hello')", 0.28),
            ("test.js", "console.log('hello')", 0.30),
            ("test.json", '{"key": "value"}', 0.35),
            ("test.md", "# Hello World", 0.25),
            ("test.xml", "<root><item/></root>", 0.40),
        ]
        
        for filename, content, expected_multiplier in test_cases:
            tokens, details = estimator.estimate_tokens(content, filename)
            
            assert details['tokens'] > 0
            if details['method'] == 'estimated':
                assert details['multiplier'] == expected_multiplier
    
    def test_minified_content_detection(self, estimator):
        """Test detection of minified content."""
        # Minified JavaScript-like content
        minified = "function a(b,c){return b+c}var d=a(1,2);console.log(d);" * 20
        tokens, details = estimator.estimate_tokens(minified, "min.js")
        
        # For minified content, multiplier should be adjusted
        if details['method'] == 'estimated':
            base_multiplier = 0.30  # .js base
            assert details['multiplier'] > base_multiplier
    
    def test_estimate_context_size(self, estimator):
        """Test context size estimation for multiple files."""
        contexts = [
            {'content': 'print("Hello")', 'path': 'file1.py'},
            {'content': 'console.log("World")', 'path': 'file2.js'},
            {'content': '# README\nThis is a test', 'path': 'README.md'},
        ]
        
        result = estimator.estimate_context_size(contexts)
        
        assert result['file_count'] == 3
        assert result['total_tokens'] > 0
        assert result['overhead_tokens'] == 150  # 50 per file
        assert len(result['files']) == 3
        
        # Each file should have token info
        for file_info in result['files']:
            assert 'path' in file_info
            assert 'tokens' in file_info
            assert 'details' in file_info
    
    def test_empty_content(self, estimator):
        """Test handling of empty content."""
        tokens, details = estimator.estimate_tokens("", "empty.txt")
        
        assert tokens == 0
        assert details['tokens'] == 0
        assert details['characters'] == 0
    
    def test_unknown_file_type(self, estimator):
        """Test handling of unknown file extensions."""
        content = "Some random content"
        tokens, details = estimator.estimate_tokens(content, "file.xyz")
        
        assert details['tokens'] > 0
        # Should use default multiplier
        if details['method'] == 'estimated':
            assert details['multiplier'] == 0.33
    
    def test_binary_like_content(self, estimator):
        """Test detection of binary-like content."""
        # Create content with non-printable characters
        binary_like = "".join(chr(i) for i in range(1, 32)) * 10 + "some text"
        tokens, details = estimator.estimate_tokens(binary_like, "data.bin")
        
        # Binary-like content should have adjusted multiplier
        if details['method'] == 'estimated':
            assert details['multiplier'] > 0.33  # Higher than default