"""Tests for git integration service."""
import pytest
import tempfile
import subprocess
from pathlib import Path
from app.services.git_integration import GitIntegration


class TestGitIntegration:
    """Test cases for GitIntegration."""
    
    @pytest.fixture
    def git_repo(self):
        """Create a temporary git repository for testing."""
        with tempfile.TemporaryDirectory() as tmpdir:
            repo_path = Path(tmpdir)
            
            # Initialize git repo
            subprocess.run(['git', 'init'], cwd=repo_path, check=True)
            subprocess.run(['git', 'config', 'user.name', 'Test User'], cwd=repo_path, check=True)
            subprocess.run(['git', 'config', 'user.email', '<EMAIL>'], cwd=repo_path, check=True)
            
            # Create initial commit
            (repo_path / 'README.md').write_text('# Test Project\n\nInitial commit')
            (repo_path / '.gitignore').write_text('*.pyc\n__pycache__/\n')
            
            subprocess.run(['git', 'add', '.'], cwd=repo_path, check=True)
            subprocess.run(['git', 'commit', '-m', 'Initial commit'], cwd=repo_path, check=True)
            
            # Create some more commits
            (repo_path / 'main.py').write_text('def main():\n    print("Hello")')
            subprocess.run(['git', 'add', 'main.py'], cwd=repo_path, check=True)
            subprocess.run(['git', 'commit', '-m', 'Add main.py'], cwd=repo_path, check=True)
            
            (repo_path / 'utils.py').write_text('def helper():\n    return True')
            subprocess.run(['git', 'add', 'utils.py'], cwd=repo_path, check=True)
            subprocess.run(['git', 'commit', '-m', 'Add utils.py'], cwd=repo_path, check=True)
            
            # Modify main.py
            (repo_path / 'main.py').write_text('def main():\n    print("Hello World")\n\nif __name__ == "__main__":\n    main()')
            subprocess.run(['git', 'add', 'main.py'], cwd=repo_path, check=True)
            subprocess.run(['git', 'commit', '-m', 'Update main.py with entry point'], cwd=repo_path, check=True)
            
            yield repo_path
    
    def test_initialization(self, git_repo):
        """Test GitIntegration initialization."""
        git_service = GitIntegration(str(git_repo))
        assert git_service.repo_path == git_repo
        
        # Test with non-git directory
        with tempfile.TemporaryDirectory() as tmpdir:
            with pytest.raises(ValueError, match="Not a git repository"):
                GitIntegration(tmpdir)
    
    def test_get_current_branch(self, git_repo):
        """Test getting current branch."""
        git_service = GitIntegration(str(git_repo))
        
        # Default branch varies (master/main)
        branch = git_service.get_current_branch()
        assert branch in ['master', 'main']
        
        # Create and switch to new branch
        subprocess.run(['git', 'checkout', '-b', 'feature-branch'], cwd=git_repo, check=True)
        assert git_service.get_current_branch() == 'feature-branch'
    
    def test_get_commit_info(self, git_repo):
        """Test getting commit information."""
        git_service = GitIntegration(str(git_repo))
        
        # Get HEAD commit info
        commit_info = git_service.get_commit_info('HEAD')
        
        assert 'hash' in commit_info
        assert 'short_hash' in commit_info
        assert 'author_name' in commit_info
        assert commit_info['author_name'] == 'Test User'
        assert commit_info['author_email'] == '<EMAIL>'
        assert 'author_date' in commit_info
        assert 'subject' in commit_info
        assert commit_info['subject'] == 'Update main.py with entry point'
        assert 'changed_files' in commit_info
        assert len(commit_info['changed_files']) == 1
        assert commit_info['changed_files'][0]['path'] == 'main.py'
    
    def test_get_commit_diff(self, git_repo):
        """Test getting commit diff."""
        git_service = GitIntegration(str(git_repo))
        
        diff = git_service.get_commit_diff('HEAD')
        
        assert 'Update main.py with entry point' in diff
        assert '@@' in diff  # Diff markers
        assert 'def main():' in diff
        assert '+if __name__ == "__main__":' in diff
    
    def test_get_commit_list(self, git_repo):
        """Test getting commit list."""
        git_service = GitIntegration(str(git_repo))
        
        commits = git_service.get_commit_list(max_count=10)
        
        assert len(commits) == 4  # We created 4 commits
        assert commits[0]['subject'] == 'Update main.py with entry point'
        assert commits[1]['subject'] == 'Add utils.py'
        assert commits[2]['subject'] == 'Add main.py'
        assert commits[3]['subject'] == 'Initial commit'
        
        # Test with author filter
        commits_filtered = git_service.get_commit_list(author='Test User')
        assert len(commits_filtered) == 4
    
    def test_get_file_at_commit(self, git_repo):
        """Test getting file content at specific commit."""
        git_service = GitIntegration(str(git_repo))
        
        # Get commits
        commits = git_service.get_commit_list()
        
        # Get main.py at different commits
        current_content = git_service.get_file_at_commit('main.py', 'HEAD')
        assert 'if __name__ == "__main__":' in current_content
        
        # Get main.py at previous commit (before update)
        prev_commit = commits[2]['hash']  # "Add main.py" commit
        old_content = git_service.get_file_at_commit('main.py', prev_commit)
        assert 'if __name__ == "__main__":' not in old_content
        assert 'print("Hello")' in old_content
        
        # Try non-existent file
        none_content = git_service.get_file_at_commit('nonexistent.py', 'HEAD')
        assert none_content is None
    
    def test_create_and_remove_worktree(self, git_repo):
        """Test creating and removing worktrees."""
        git_service = GitIntegration(str(git_repo))
        
        # Create worktree
        worktree_path = git_service.create_worktree('test-agent-1')
        assert worktree_path.exists()
        assert (worktree_path / 'main.py').exists()
        
        # List worktrees
        worktrees = git_service.list_worktrees()
        assert len(worktrees) >= 2  # Main + our worktree
        
        # Find our worktree
        test_worktree = next(
            (w for w in worktrees if 'test-agent-1' in w['path']),
            None
        )
        assert test_worktree is not None
        
        # Remove worktree
        git_service.remove_worktree('test-agent-1')
        assert not worktree_path.exists()
    
    def test_get_diff_between_commits(self, git_repo):
        """Test getting diff between two commits."""
        git_service = GitIntegration(str(git_repo))
        
        # Get commits
        commits = git_service.get_commit_list()
        
        # Get diff between "Add main.py" and "Update main.py"
        diff_info = git_service.get_diff_between_commits(
            commits[2]['hash'],  # Add main.py
            commits[0]['hash']   # Update main.py
        )
        
        assert 'diff' in diff_info
        assert 'stats' in diff_info
        assert 'changed_files' in diff_info
        
        # Should have 2 files: main.py (modified) and utils.py (added)
        assert len(diff_info['changed_files']) == 2
        
        # Find main.py in changed files
        main_py_change = next(
            (f for f in diff_info['changed_files'] if f['path'] == 'main.py'),
            None
        )
        assert main_py_change is not None
        assert main_py_change['status'] == 'M'  # Modified
        
        # Check diff content
        assert '+if __name__ == "__main__":' in diff_info['diff']
    
    def test_get_file_history(self, git_repo):
        """Test getting file history."""
        git_service = GitIntegration(str(git_repo))
        
        # Get history for main.py
        history = git_service.get_file_history('main.py')
        
        assert len(history) == 2  # Created and updated
        assert history[0]['subject'] == 'Update main.py with entry point'
        assert history[1]['subject'] == 'Add main.py'
        
        # Get history for README.md
        readme_history = git_service.get_file_history('README.md')
        assert len(readme_history) == 1
        assert readme_history[0]['subject'] == 'Initial commit'
    
    def test_cleanup_worktrees(self, git_repo):
        """Test cleaning up worktrees."""
        git_service = GitIntegration(str(git_repo))
        
        # Create multiple worktrees
        worktree1 = git_service.create_worktree('cleanup-test-1')
        worktree2 = git_service.create_worktree('cleanup-test-2')
        
        assert worktree1.exists()
        assert worktree2.exists()
        
        # Cleanup
        git_service.cleanup_worktrees()
        
        # Worktrees should be removed
        assert not worktree1.exists()
        assert not worktree2.exists()