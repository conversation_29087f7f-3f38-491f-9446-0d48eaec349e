"""
Tests for criteria version control and modification history features.
"""
import uuid
import pytest
import asyncio
from httpx import AsyncClient
from sqlalchemy import text
from datetime import datetime

from app.main import app
from app.models.agent_evaluation import EvaluationCriteria, EvaluationResult

pytestmark = pytest.mark.asyncio

# ---------- fixtures ----------
@pytest.fixture
async def client():
    async with Async<PERSON><PERSON>(app=app, base_url="http://test") as ac:
        yield ac

# ---------- helpers ----------
HEADERS = {"X-User-ID": "test-user-version-control"}
HEADERS_OTHER_USER = {"X-User-ID": "test-user-other"}

async def _create_task(client):
    resp = await client.post("/api/v1/agent-evaluations/tasks",
                             headers=HEADERS,
                             json={"name": "pytest-task-version", "evaluation_type": "0-1"})
    resp.raise_for_status()
    return resp.json()["id"]

async def _create_criteria(client, task_id, name="Test Criteria"):
    body = {
        "name": name,
        "description": "Test criteria for version control",
        "weight": 1.0,
        "criteria_type": "quality",
        "evaluation_prompt": "Evaluate based on quality",
        "meta_data": {
            "components": [
                {
                    "name": "Component 1",
                    "description": "First component",
                    "weight": 0.5,
                    "evaluation_method": "Review",
                    "scoring_rubric": {
                        "0-30": "Poor",
                        "31-60": "Fair",
                        "61-80": "Good",
                        "81-100": "Excellent"
                    }
                },
                {
                    "name": "Component 2",
                    "description": "Second component",
                    "weight": 0.5,
                    "evaluation_method": "Analysis",
                    "scoring_rubric": {
                        "0-30": "Poor",
                        "31-60": "Fair",
                        "61-80": "Good",
                        "81-100": "Excellent"
                    }
                }
            ]
        }
    }
    resp = await client.post(f"/api/v1/agent-evaluations/tasks/{task_id}/criteria",
                             headers=HEADERS, json=body)
    resp.raise_for_status()
    return resp.json()

async def _create_evaluation_with_results(client, db_session, task_id, criteria_id):
    """Create an evaluation that uses the given criteria."""
    # Directly create test data in the database
    from app.models.agent_evaluation import AgentSubmission, AgentEvaluation, EvaluationResult
    
    # Create submission
    submission = AgentSubmission(
        id=str(uuid.uuid4()),
        task_id=task_id,
        agent_name="test-agent",
        agent_version="1.0",
        status="evaluated"
    )
    db_session.add(submission)
    await db_session.flush()
    
    # Create evaluation
    evaluation = AgentEvaluation(
        id=str(uuid.uuid4()),
        task_id=task_id,
        submission_id=submission.id,
        model_name="test-model",
        status="completed",
        overall_score=85.0
    )
    db_session.add(evaluation)
    await db_session.flush()
    
    # Create result using the criteria
    result = EvaluationResult(
        id=str(uuid.uuid4()),
        evaluation_id=evaluation.id,
        criteria_id=criteria_id,
        score=85.0,
        reasoning="Test reasoning",
        evidence=["evidence1", "evidence2"]
    )
    db_session.add(result)
    await db_session.commit()
    
    return evaluation.id

# ---------- tests ----------
async def test_criteria_usage_check_endpoint(async_client, db_session):
    """Test the /criteria/{id}/usage endpoint."""
    task_id = await _create_task(async_client)
    criteria = await _create_criteria(async_client, task_id)
    criteria_id = criteria["id"]
    
    # Check usage when no results exist
    resp = await async_client.get(f"/api/v1/agent-evaluations/criteria/{criteria_id}/usage")
    assert resp.status_code == 200
    usage_data = resp.json()
    assert usage_data["criteria_id"] == criteria_id
    assert usage_data["has_results"] is False
    assert usage_data["can_delete"] is True
    assert usage_data["can_modify"] is True
    
    # Create evaluation results that use this criteria
    await _create_evaluation_with_results(async_client, db_session, task_id, criteria_id)
    
    # Check usage again
    resp = await async_client.get(f"/api/v1/agent-evaluations/criteria/{criteria_id}/usage")
    assert resp.status_code == 200
    usage_data = resp.json()
    assert usage_data["has_results"] is True
    assert usage_data["can_delete"] is False
    assert usage_data["can_modify"] is True

async def test_criteria_version_control_on_edit(async_client, db_session):
    """Test that editing criteria with results creates a new version."""
    task_id = await _create_task(async_client)
    criteria = await _create_criteria(async_client, task_id, "Original Criteria")
    criteria_id = criteria["id"]
    
    # Update criteria without results (should update in place)
    update_data = {
        "description": "Updated description",
        "weight": 0.8
    }
    resp = await async_client.put(f"/api/v1/agent-evaluations/criteria/{criteria_id}",
                                  headers=HEADERS, json=update_data)
    assert resp.status_code == 200
    updated = resp.json()
    assert updated["id"] == criteria_id  # Same ID
    assert updated["description"] == "Updated description"
    assert updated["weight"] == 0.8
    assert updated["modified_by_user_id"] == HEADERS["X-User-ID"]
    
    # Create evaluation results
    await _create_evaluation_with_results(async_client, db_session, task_id, criteria_id)
    
    # Now updates should create a new version if frontend sends POST to create new
    # (The frontend logic handles the version decision, backend just provides the capability)

async def test_criteria_history_endpoint(async_client, db_session):
    """Test the /criteria/{id}/history endpoint."""
    task_id = await _create_task(async_client)
    
    # Create original criteria
    original = await _create_criteria(async_client, task_id, "Original V1")
    original_id = original["id"]
    
    # Create a new version (simulating frontend decision)
    version2_data = {
        "name": "Original V2",
        "description": "Second version of criteria",
        "weight": 0.8,
        "criteria_type": "quality",
        "original_criteria_id": original_id,  # Link to original
        "source": "USER_CREATED",
        "meta_data": {"components": []}
    }
    resp = await async_client.post(f"/api/v1/agent-evaluations/tasks/{task_id}/criteria",
                                   headers=HEADERS, json=version2_data)
    assert resp.status_code == 200
    version2 = resp.json()
    
    # Get history for original
    resp = await async_client.get(f"/api/v1/agent-evaluations/criteria/{original_id}/history")
    assert resp.status_code == 200
    history = resp.json()
    
    # Should include both versions
    assert len(history) >= 2
    assert any(h["criteria_id"] == original_id for h in history)
    assert any(h["criteria_id"] == version2["id"] for h in history)
    
    # Check history entries have required fields
    for entry in history:
        assert "id" in entry
        assert "criteria_id" in entry
        assert "modified_at" in entry
        assert "modified_by_user_id" in entry
        assert "source" in entry
        assert "version_number" in entry
        assert "is_current" in entry

async def test_criteria_deletion_blocked_with_results(async_client, db_session):
    """Test that criteria with evaluation results cannot be deleted."""
    task_id = await _create_task(async_client)
    criteria = await _create_criteria(async_client, task_id)
    criteria_id = criteria["id"]
    
    # Should be able to delete without results
    resp = await async_client.delete(f"/api/v1/agent-evaluations/criteria/{criteria_id}",
                                     headers=HEADERS)
    assert resp.status_code == 204
    
    # Create another criteria
    criteria2 = await _create_criteria(async_client, task_id, "Criteria 2")
    criteria2_id = criteria2["id"]
    
    # Create evaluation results
    await _create_evaluation_with_results(async_client, db_session, task_id, criteria2_id)
    
    # Should not be able to delete with results
    resp = await async_client.delete(f"/api/v1/agent-evaluations/criteria/{criteria2_id}",
                                     headers=HEADERS)
    assert resp.status_code == 400
    assert "Cannot delete criteria that has been used in evaluations" in resp.json()["detail"]

async def test_criteria_source_tracking_on_edit(async_client, db_session):
    """Test that source field is properly updated when editing criteria."""
    task_id = await _create_task(async_client)
    
    # Create a criteria marked as GENERATED (simulating LLM-generated)
    criteria_data = {
        "name": "Generated Criteria",
        "description": "LLM generated this",
        "weight": 1.0,
        "criteria_type": "quality",
        "source": "GENERATED",
        "meta_data": {"components": []}
    }
    resp = await async_client.post(f"/api/v1/agent-evaluations/tasks/{task_id}/criteria",
                                   headers=HEADERS, json=criteria_data)
    assert resp.status_code == 200
    criteria = resp.json()
    criteria_id = criteria["id"]
    assert criteria["source"] == "GENERATED"
    
    # Edit the criteria
    update_data = {"description": "User edited this"}
    resp = await async_client.put(f"/api/v1/agent-evaluations/criteria/{criteria_id}",
                                  headers=HEADERS, json=update_data)
    assert resp.status_code == 200
    updated = resp.json()
    assert updated["source"] == "GENERATED_EDITED"

async def test_batch_component_update(async_client, db_session):
    """Test batch updating components within criteria."""
    task_id = await _create_task(async_client)
    criteria = await _create_criteria(async_client, task_id)
    criteria_id = criteria["id"]
    
    # Prepare new components
    new_components = [
        {
            "name": "Updated Component 1",
            "description": "Updated first component",
            "weight": 0.6,
            "evaluation_method": "Updated Review",
            "scoring_rubric": {
                "0-30": "Very Poor",
                "31-60": "Below Average",
                "61-80": "Above Average",
                "81-100": "Outstanding"
            }
        },
        {
            "name": "Updated Component 2",
            "description": "Updated second component",
            "weight": 0.4,
            "evaluation_method": "Updated Analysis",
            "scoring_rubric": {
                "0-30": "Very Poor",
                "31-60": "Below Average",
                "61-80": "Above Average",
                "81-100": "Outstanding"
            }
        }
    ]
    
    # Batch update components
    resp = await async_client.post(f"/api/v1/agent-evaluations/criteria/{criteria_id}/components/batch",
                                   headers=HEADERS, json=new_components)
    assert resp.status_code == 200
    result = resp.json()
    assert result["status"] == "success"
    assert result["updated_components"] == 2
    
    # Verify components were updated
    resp = await async_client.get(f"/api/v1/agent-evaluations/tasks/{task_id}/criteria")
    assert resp.status_code == 200
    criteria_list = resp.json()
    updated_criteria = next(c for c in criteria_list if c["id"] == criteria_id)
    assert len(updated_criteria["meta_data"]["components"]) == 2
    assert updated_criteria["meta_data"]["components"][0]["name"] == "Updated Component 1"
    assert updated_criteria["meta_data"]["components"][0]["weight"] == 0.6

async def test_criteria_modification_tracking(async_client, db_session):
    """Test that modification timestamps and user IDs are properly tracked."""
    task_id = await _create_task(async_client)
    criteria = await _create_criteria(async_client, task_id)
    criteria_id = criteria["id"]
    
    # Initial state
    assert criteria["created_by_user_id"] == HEADERS["X-User-ID"]
    assert criteria["modified_by_user_id"] is None or criteria["modified_by_user_id"] == HEADERS["X-User-ID"]
    
    # Update with different user
    update_data = {"description": "Modified by another user"}
    resp = await async_client.put(f"/api/v1/agent-evaluations/criteria/{criteria_id}",
                                  headers=HEADERS_OTHER_USER, json=update_data)
    assert resp.status_code == 200
    updated = resp.json()
    assert updated["modified_by_user_id"] == HEADERS_OTHER_USER["X-User-ID"]
    assert updated["modified_at"] is not None
    
    # Parse dates to compare
    if criteria.get("created_at"):
        created_at = datetime.fromisoformat(criteria["created_at"].replace("Z", "+00:00"))
        modified_at = datetime.fromisoformat(updated["modified_at"].replace("Z", "+00:00"))
        assert modified_at > created_at

async def test_criteria_weight_validation(async_client, db_session):
    """Test validation of criteria and component weights."""
    task_id = await _create_task(async_client)
    
    # Test invalid criteria weight (> 1)
    invalid_criteria = {
        "name": "Invalid Weight Criteria",
        "description": "This has invalid weight",
        "weight": 1.5,  # Invalid
        "criteria_type": "quality",
        "meta_data": {"components": []}
    }
    resp = await async_client.post(f"/api/v1/agent-evaluations/tasks/{task_id}/criteria",
                                   headers=HEADERS, json=invalid_criteria)
    # Backend might not validate this strictly, but frontend should
    
    # Test component weights validation (frontend responsibility)
    # The backend stores what's sent, validation is primarily frontend

async def test_nonexistent_criteria_endpoints(async_client):
    """Test error handling for non-existent criteria."""
    fake_id = str(uuid.uuid4())
    
    # Test usage endpoint
    resp = await async_client.get(f"/api/v1/agent-evaluations/criteria/{fake_id}/usage")
    assert resp.status_code == 404
    assert "not found" in resp.json()["detail"].lower()
    
    # Test history endpoint
    resp = await async_client.get(f"/api/v1/agent-evaluations/criteria/{fake_id}/history")
    assert resp.status_code == 404
    assert "not found" in resp.json()["detail"].lower()
    
    # Test update endpoint
    resp = await async_client.put(f"/api/v1/agent-evaluations/criteria/{fake_id}",
                                  headers=HEADERS, json={"description": "test"})
    assert resp.status_code == 404
    assert "not found" in resp.json()["detail"].lower()