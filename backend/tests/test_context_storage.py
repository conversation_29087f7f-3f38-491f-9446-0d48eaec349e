"""Tests for context storage service."""
import pytest
import tempfile
from pathlib import Path
from app.services.context_storage import ContextStorage
from app.services.blob_storage import BlobStorage


class TestContextStorage:
    """Test cases for ContextStorage."""
    
    @pytest.fixture
    def storage_service(self):
        """Create a ContextStorage instance."""
        with tempfile.TemporaryDirectory() as tmpdir:
            blob_storage = BlobStorage(base_path=tmpdir)
            yield ContextStorage(blob_storage=blob_storage)
    
    @pytest.mark.asyncio
    async def test_store_small_context(self, storage_service):
        """Test storing small context inline."""
        # Mock database session
        class MockDB:
            async def add(self, obj):
                pass
            async def commit(self):
                pass
            async def refresh(self, obj):
                obj.id = "test-context-123"
        
        # Mock CRUD
        class MockContextCRUD:
            async def create(self, db, obj_in):
                class MockRecord:
                    id = "test-context-123"
                    submission_id = obj_in['submission_id']
                    context_type = obj_in['context_type']
                    file_path = obj_in.get('file_path')
                    content_hash = obj_in['content_hash']
                    storage_type = obj_in['storage_type']
                    estimated_tokens = obj_in['estimated_tokens']
                return MockRecord()
        
        # Patch the CRUD import
        import app.crud.agent_evaluation
        original_crud = getattr(app.crud.agent_evaluation, 'evaluation_context', None)
        app.crud.agent_evaluation.evaluation_context = MockContextCRUD()
        
        try:
            context = {
                'content': 'def hello():\n    print("Hello World")',
                'context_type': 'file',
                'file_path': 'hello.py',
                'estimated_tokens': 15,
                'file_metadata': {'type': 'python'}
            }
            
            db = MockDB()
            result = await storage_service.store_context(
                context,
                'submission-123',
                db
            )
            
            assert result['id'] == 'test-context-123'
            assert result['storage_type'] == 'inline'
            assert result['submission_id'] == 'submission-123'
        finally:
            # Restore original
            if original_crud:
                app.crud.agent_evaluation.evaluation_context = original_crud
    
    @pytest.mark.asyncio
    async def test_store_large_context(self, storage_service):
        """Test storing large context in blob storage."""
        # Create large content
        large_content = 'x' * (150 * 1024)  # 150KB
        
        # Mock database session and CRUD
        class MockDB:
            async def add(self, obj):
                pass
            async def commit(self):
                pass
            async def refresh(self, obj):
                obj.id = "test-blob-123"
        
        class MockBlobCRUD:
            async def create(self, db, obj_in):
                class MockBlob:
                    id = "test-blob-123"
                return MockBlob()
        
        class MockContextCRUD:
            async def create(self, db, obj_in):
                class MockRecord:
                    id = "test-context-456"
                    submission_id = obj_in['submission_id']
                    context_type = obj_in['context_type']
                    file_path = obj_in.get('file_path')
                    content_hash = obj_in['content_hash']
                    storage_type = obj_in['storage_type']
                    estimated_tokens = obj_in['estimated_tokens']
                return MockRecord()
        
        # Patch the CRUD imports
        import app.crud.agent_evaluation
        original_context_crud = getattr(app.crud.agent_evaluation, 'evaluation_context', None)
        original_blob_crud = getattr(app.crud.agent_evaluation, 'evaluation_context_blob', None)
        app.crud.agent_evaluation.evaluation_context = MockContextCRUD()
        app.crud.agent_evaluation.evaluation_context_blob = MockBlobCRUD()
        
        try:
            context = {
                'content': large_content,
                'context_type': 'file',
                'file_path': 'large.txt',
                'estimated_tokens': 50000,
                'file_metadata': {'type': 'text'}
            }
            
            db = MockDB()
            result = await storage_service.store_context(
                context,
                'submission-456',
                db
            )
            
            assert result['id'] == 'test-context-456'
            assert result['storage_type'] == 'blob'
            assert result['submission_id'] == 'submission-456'
        finally:
            # Restore originals
            if original_context_crud:
                app.crud.agent_evaluation.evaluation_context = original_context_crud
            if original_blob_crud:
                app.crud.agent_evaluation.evaluation_context_blob = original_blob_crud
    
    @pytest.mark.asyncio
    async def test_estimate_storage_size(self, storage_service):
        """Test storage size estimation."""
        # Mock CRUD
        class MockContextCRUD:
            async def get_by_submission(self, db, submission_id, context_type=None):
                class MockContext:
                    content_size = 1024
                    estimated_tokens = 256
                    storage_type = 'inline'
                
                contexts = []
                for i in range(3):
                    ctx = MockContext()
                    if i == 2:
                        ctx.storage_type = 'blob'
                        ctx.content_size = 10240
                    contexts.append(ctx)
                
                return contexts
        
        # Patch the CRUD import
        import app.crud.agent_evaluation
        original_crud = getattr(app.crud.agent_evaluation, 'evaluation_context', None)
        app.crud.agent_evaluation.evaluation_context = MockContextCRUD()
        
        try:
            db = None  # Not used in this mock
            result = await storage_service.estimate_storage_size(
                'submission-789',
                db
            )
            
            assert result['submission_id'] == 'submission-789'
            assert result['total_size'] == (1024 * 2 + 10240)
            assert result['inline_size'] == 1024 * 2
            assert result['blob_size'] == 10240
            assert result['total_tokens'] == 256 * 3
            assert result['context_count'] == 3
        finally:
            # Restore original
            if original_crud:
                app.crud.agent_evaluation.evaluation_context = original_crud