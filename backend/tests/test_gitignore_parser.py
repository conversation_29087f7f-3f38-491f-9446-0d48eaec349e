"""Tests for the gitignore parser."""
import pytest
import tempfile
import os
from pathlib import Path
from app.services.gitignore_parser import GitignoreParser


class TestGitignoreParser:
    """Test cases for GitignoreParser."""
    
    @pytest.fixture
    def temp_dir(self):
        """Create a temporary directory for testing."""
        with tempfile.TemporaryDirectory() as tmpdir:
            yield Path(tmpdir)
    
    def test_initialization_without_gitignore(self, temp_dir):
        """Test initialization when no .gitignore exists."""
        parser = GitignoreParser(str(temp_dir))
        
        assert parser.root_path == temp_dir
        # Should have default patterns
        assert len(parser.patterns) == len(GitignoreParser.DEFAULT_EXCLUDES)
        assert '__pycache__/' in parser.patterns
        assert 'node_modules/' in parser.patterns
    
    def test_initialization_with_gitignore(self, temp_dir):
        """Test initialization with .gitignore file."""
        # Create .gitignore
        gitignore_content = """
# Comments should be ignored
*.log
/build/
!important.log

# Another comment
secret/
*.tmp
"""
        gitignore_path = temp_dir / '.gitignore'
        gitignore_path.write_text(gitignore_content)
        
        parser = GitignoreParser(str(temp_dir))
        
        # Should have default + custom patterns
        assert len(parser.patterns) > len(GitignoreParser.DEFAULT_EXCLUDES)
        assert '*.log' in parser.patterns
        assert '/build/' in parser.patterns
        assert 'secret/' in parser.patterns
        assert '*.tmp' in parser.patterns
        
        # Check negated patterns
        assert 'important.log' in parser.negated_patterns
    
    def test_should_ignore_default_patterns(self, temp_dir):
        """Test ignoring files based on default patterns."""
        parser = GitignoreParser(str(temp_dir))
        
        # Create test paths
        test_cases = [
            (temp_dir / '__pycache__' / 'module.pyc', True),
            (temp_dir / 'file.pyc', True),
            (temp_dir / 'node_modules' / 'package' / 'index.js', True),
            (temp_dir / '.git' / 'config', True),
            (temp_dir / '.env', True),
            (temp_dir / 'normal_file.py', False),
            (temp_dir / 'readme.md', False),
        ]
        
        for path, should_ignore in test_cases:
            assert parser.should_ignore(path) == should_ignore, f"Failed for {path}"
    
    def test_should_ignore_custom_patterns(self, temp_dir):
        """Test ignoring files based on custom patterns."""
        # Create .gitignore with custom patterns
        gitignore_content = """
*.secret
temp/
/logs/
data/*.csv
!data/important.csv
"""
        (temp_dir / '.gitignore').write_text(gitignore_content)
        
        parser = GitignoreParser(str(temp_dir))
        
        test_cases = [
            (temp_dir / 'config.secret', True),
            (temp_dir / 'nested' / 'file.secret', True),
            (temp_dir / 'temp' / 'file.txt', True),
            (temp_dir / 'logs' / 'app.log', True),
            (temp_dir / 'nested' / 'logs' / 'file.txt', False),  # Only root logs/
            (temp_dir / 'data' / 'export.csv', True),
            (temp_dir / 'data' / 'important.csv', False),  # Negated
            (temp_dir / 'normal.txt', False),
        ]
        
        for path, should_ignore in test_cases:
            result = parser.should_ignore(path)
            assert result == should_ignore, f"Failed for {path}: expected {should_ignore}, got {result}"
    
    def test_directory_patterns(self, temp_dir):
        """Test directory-specific patterns."""
        gitignore_content = """
build/
src/temp/
"""
        (temp_dir / '.gitignore').write_text(gitignore_content)
        
        parser = GitignoreParser(str(temp_dir))
        
        # Create directory structure
        (temp_dir / 'build').mkdir()
        (temp_dir / 'src' / 'temp').mkdir(parents=True)
        
        test_cases = [
            (temp_dir / 'build' / 'output.js', True),
            (temp_dir / 'build', True),  # Directory itself
            (temp_dir / 'nested' / 'build' / 'file.txt', True),
            (temp_dir / 'src' / 'temp' / 'file.py', True),
            (temp_dir / 'src' / 'main.py', False),
            (temp_dir / 'buildfile.txt', False),  # Not a directory
        ]
        
        for path, should_ignore in test_cases:
            assert parser.should_ignore(path) == should_ignore, f"Failed for {path}"
    
    def test_wildcard_patterns(self, temp_dir):
        """Test wildcard patterns."""
        gitignore_content = """
*.log
test_*.py
debug/
"""
        (temp_dir / '.gitignore').write_text(gitignore_content)
        
        parser = GitignoreParser(str(temp_dir))
        
        test_cases = [
            (temp_dir / 'app.log', True),
            (temp_dir / 'logs' / 'error.log', True),
            (temp_dir / 'test_module.py', True),
            (temp_dir / 'tests' / 'test_utils.py', True),
            (temp_dir / 'module_test.py', False),
            # Use simpler directory pattern
            (temp_dir / 'debug' / 'info.txt', True),
            (temp_dir / 'src' / 'debug' / 'file.txt', True),
        ]
        
        for path, should_ignore in test_cases:
            result = parser.should_ignore(path)
            assert result == should_ignore, f"Failed for {path}: expected {should_ignore}, got {result}"
    
    def test_filter_files(self, temp_dir):
        """Test filtering a list of files."""
        gitignore_content = "*.pyc\n__pycache__/"
        (temp_dir / '.gitignore').write_text(gitignore_content)
        
        parser = GitignoreParser(str(temp_dir))
        
        file_paths = [
            temp_dir / 'main.py',
            temp_dir / 'module.pyc',
            temp_dir / '__pycache__' / 'cache.pyc',
            temp_dir / 'src' / 'utils.py',
            temp_dir / 'src' / 'compiled.pyc',
            temp_dir / 'README.md',
        ]
        
        filtered = parser.filter_files(file_paths)
        
        expected = [
            temp_dir / 'main.py',
            temp_dir / 'src' / 'utils.py',
            temp_dir / 'README.md',
        ]
        
        assert sorted(filtered) == sorted(expected)
    
    def test_patterns_summary(self, temp_dir):
        """Test getting patterns summary."""
        gitignore_content = "*.log\ntemp/"
        (temp_dir / '.gitignore').write_text(gitignore_content)
        
        parser = GitignoreParser(str(temp_dir))
        summary = parser.get_patterns_summary()
        
        assert summary['root_path'] == str(temp_dir)
        assert summary['total_patterns'] > len(GitignoreParser.DEFAULT_EXCLUDES)
        assert summary['default_patterns'] == len(GitignoreParser.DEFAULT_EXCLUDES)
        assert summary['custom_patterns'] == 2  # *.log and temp/
        assert summary['negated_patterns'] == 0
    
    def test_outside_root_path(self, temp_dir):
        """Test handling files outside root path."""
        parser = GitignoreParser(str(temp_dir))
        
        # Create a path outside the root
        outside_path = Path('/tmp/outside_file.pyc')
        
        # Should not be ignored (not in our directory)
        assert not parser.should_ignore(outside_path)