"""Tests for criteria generation service."""
import pytest
import json
from unittest.mock import Mock, AsyncMock, patch
from app.services.criteria_generator import (
    CriteriaGenerator,
    CriterionComponent,
    GeneratedCriterion
)


class TestCriteriaGenerator:
    """Test criteria generation functionality."""
    
    @pytest.fixture
    def mock_llm_service(self):
        """Create mock LLM service."""
        mock = Mock()
        mock.generate = AsyncMock()
        return mock
    
    @pytest.fixture
    def criteria_generator(self, mock_llm_service):
        """Create criteria generator with mocked dependencies."""
        return CriteriaGenerator(llm_service=mock_llm_service)
    
    @pytest.mark.asyncio
    async def test_generate_criteria_0_1_evaluation(self, criteria_generator, mock_llm_service):
        """Test criteria generation for 0-1 evaluation."""
        # Mock LLM response
        mock_response = Mock()
        mock_response.output_text = json.dumps([
            {
                "name": "Functional Correctness",
                "description": "Evaluates if the implementation correctly fulfills all requirements",
                "criteria_type": "quality",
                "weight": 0.3,
                "components": [
                    {
                        "name": "Core Functionality",
                        "description": "All main features work as specified",
                        "weight": 0.6,
                        "evaluation_method": "Test execution and output verification",
                        "scoring_rubric": {
                            "0-20": "Major functionality missing",
                            "21-100": "Functionality implemented"
                        }
                    }
                ],
                "evaluation_prompt_template": "Evaluate the functional correctness..."
            }
        ])
        mock_response.prompt_tokens = 1000
        mock_response.completion_tokens = 500
        mock_llm_service.generate.return_value = mock_response
        
        # Generate criteria
        criteria, usage_stats = await criteria_generator.generate_criteria(
            task_prompt="Build a todo app with React",
            evaluation_type="0-1",
            num_criteria=1
        )
        
        # Verify
        assert len(criteria) == 1
        assert criteria[0].name == "Functional Correctness"
        assert criteria[0].weight == 0.3
        assert len(criteria[0].components) == 1
        
        # Verify LLM was called correctly
        mock_llm_service.generate.assert_called_once()
        call_args = mock_llm_service.generate.call_args
        assert "0-1" in call_args[1]['system_prompt']
        assert "Build a todo app with React" in call_args[1]['user_prompt']
    
    @pytest.mark.asyncio
    async def test_generate_criteria_90_100_evaluation(self, criteria_generator, mock_llm_service):
        """Test criteria generation for 90-100 evaluation."""
        # Mock LLM response
        mock_response = Mock()
        mock_response.output_text = json.dumps({
            "criteria": [
                {
                    "name": "Improvement Quality",
                    "description": "Evaluates the quality and impact of improvements",
                    "criteria_type": "quality",
                    "weight": 0.4,
                    "components": [
                        {
                            "name": "Code Optimization",
                            "description": "Performance improvements made",
                            "weight": 0.5,
                            "evaluation_method": "Performance comparison",
                            "scoring_rubric": {
                                "0-50": "Minimal improvements",
                                "51-100": "Significant improvements"
                            }
                        }
                    ],
                    "evaluation_prompt_template": "Evaluate the improvement quality..."
                }
            ]
        })
        mock_response.prompt_tokens = 1000
        mock_response.completion_tokens = 500
        mock_llm_service.generate.return_value = mock_response
        
        # Generate criteria
        criteria, usage_stats = await criteria_generator.generate_criteria(
            task_prompt="Optimize the existing codebase",
            evaluation_type="90-100",
            num_criteria=1
        )
        
        # Verify
        assert len(criteria) == 1
        assert criteria[0].name == "Improvement Quality"
        assert "90-100" in mock_llm_service.generate.call_args[1]['system_prompt']
    
    @pytest.mark.asyncio
    async def test_generate_criteria_with_context(self, criteria_generator, mock_llm_service):
        """Test criteria generation with additional context."""
        mock_response = Mock()
        mock_response.output_text = json.dumps([
            {
                "name": "Test Criterion",
                "description": "Test description",
                "criteria_type": "quality",
                "weight": 1.0,
                "components": [],
                "evaluation_prompt_template": "Test template"
            }
        ])
        mock_response.prompt_tokens = 1000
        mock_response.completion_tokens = 500
        mock_llm_service.generate.return_value = mock_response
        
        # Generate with context
        task_context = {
            "framework": "React",
            "requirements": ["responsive", "accessible"]
        }
        
        criteria, usage_stats = await criteria_generator.generate_criteria(
            task_prompt="Build an app",
            evaluation_type="0-1",
            task_context=task_context,
            num_criteria=1
        )
        
        # Verify context was included
        call_args = mock_llm_service.generate.call_args
        assert "React" in call_args[1]['user_prompt']
        assert "responsive" in call_args[1]['user_prompt']
    
    @pytest.mark.asyncio
    async def test_generate_criteria_error_handling(self, criteria_generator, mock_llm_service):
        """Test error handling in criteria generation."""
        # Mock LLM error
        mock_llm_service.generate.side_effect = Exception("LLM API error")
        
        # Should return default criteria
        criteria, usage_stats = await criteria_generator.generate_criteria(
            task_prompt="Build something",
            evaluation_type="0-1",
            num_criteria=2
        )
        
        # Verify defaults
        assert len(criteria) == 2
        assert all(isinstance(c, GeneratedCriterion) for c in criteria)
        assert criteria[0].name == "Functional Completeness"
        assert criteria[1].name == "Code Quality"
    
    def test_criteria_to_db_format(self, criteria_generator):
        """Test converting criteria to database format."""
        # Create test criteria
        criterion = GeneratedCriterion(
            name="Test Criterion",
            description="Test description",
            criteria_type="quality",
            weight=0.5,
            components=[
                CriterionComponent(
                    name="Component 1",
                    description="Comp desc",
                    weight=1.0,
                    evaluation_method="Method",
                    scoring_rubric={"0-100": "Score range"}
                )
            ],
            evaluation_prompt_template="Template"
        )
        
        # Convert to DB format
        db_criteria = criteria_generator.criteria_to_db_format(
            [criterion],
            task_id="task-123",
            generated_by_model="claude-3"
        )
        
        # Verify
        assert len(db_criteria) == 1
        db_criterion = db_criteria[0]
        assert db_criterion.task_id == "task-123"
        assert db_criterion.name == "Test Criterion"
        assert db_criterion.weight == 0.5
        assert db_criterion.meta_data["generated_by"] == "claude-3"
        assert len(db_criterion.meta_data["components"]) == 1
    
    def test_format_criterion_for_evaluation(self, criteria_generator):
        """Test formatting criterion for evaluation."""
        criterion = GeneratedCriterion(
            name="Test Criterion",
            description="Test description",
            criteria_type="quality",
            weight=0.5,
            components=[
                CriterionComponent(
                    name="Component 1",
                    description="Component description",
                    weight=0.7,
                    evaluation_method="Test method",
                    scoring_rubric={
                        "0-50": "Poor",
                        "51-100": "Good"
                    }
                )
            ],
            evaluation_prompt_template="Evaluate this: {context}"
        )
        
        context = {
            'file_count': 10,
            'total_tokens': 5000,
            'evaluation_type': '0-1'
        }
        
        # Format for evaluation
        formatted = criteria_generator.format_criterion_for_evaluation(
            criterion, context
        )
        
        # Verify
        assert "Test Criterion" in formatted
        assert "Component 1" in formatted
        assert "Weight: 0.7" in formatted
        assert "0-50: Poor" in formatted
        assert "Files analyzed: 10" in formatted
        assert "Total tokens: 5000" in formatted
    
    @pytest.mark.asyncio
    async def test_invalid_evaluation_type(self, criteria_generator):
        """Test handling of invalid evaluation type."""
        with pytest.raises(ValueError, match="Invalid evaluation type"):
            await criteria_generator.generate_criteria(
                task_prompt="Test",
                evaluation_type="invalid-type",
                num_criteria=1
            )
    
    def test_default_criteria_0_1(self, criteria_generator):
        """Test default criteria for 0-1 evaluation."""
        defaults = criteria_generator._get_default_criteria("0-1", 2)
        
        assert len(defaults) == 2
        assert defaults[0].name == "Functional Completeness"
        assert defaults[1].name == "Code Quality"
        assert all(len(c.components) > 0 for c in defaults)
    
    def test_default_criteria_90_100(self, criteria_generator):
        """Test default criteria for 90-100 evaluation."""
        defaults = criteria_generator._get_default_criteria("90-100", 1)
        
        assert len(defaults) == 1
        assert defaults[0].name == "Improvement Quality"
        assert defaults[0].weight == 0.4
        assert len(defaults[0].components) > 0