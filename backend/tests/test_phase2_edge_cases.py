"""Edge case tests for Phase 2 components."""
import pytest
import tempfile
import subprocess
from pathlib import Path
import os
from app.services.folder_context_capture import FolderContextCapture
from app.services.git_integration import GitIntegration
from app.services.git_context_capture import GitContextCapture
from app.services.token_estimator import TokenEstimator
from app.services.gitignore_parser import GitignoreParser
from app.services.blob_storage import BlobStorage


class TestPhase2EdgeCases:
    """Test edge cases and error scenarios."""
    
    @pytest.mark.asyncio
    async def test_empty_repository(self):
        """Test handling of empty git repository."""
        with tempfile.TemporaryDirectory() as tmpdir:
            # Initialize empty repo
            subprocess.run(['git', 'init'], cwd=tmpdir, check=True)
            subprocess.run(['git', 'config', 'user.name', 'Test'], cwd=tmpdir, check=True)
            subprocess.run(['git', 'config', 'user.email', '<EMAIL>'], cwd=tmpdir, check=True)
            
            git_integration = GitIntegration(tmpdir)
            
            # Should handle empty repo gracefully
            commits = git_integration.get_commit_list()
            assert commits == []
            
            # Folder capture should work even with no commits
            folder_capture = FolderContextCapture()
            result = await folder_capture.capture_folder(tmpdir, 'test-sub')
            assert result['file_count'] == 0
            assert result['contexts'] == []
    
    @pytest.mark.asyncio
    async def test_binary_files(self):
        """Test handling of binary files."""
        with tempfile.TemporaryDirectory() as tmpdir:
            # Create binary file
            binary_file = Path(tmpdir) / 'test.bin'
            binary_file.write_bytes(b'\x00\x01\x02\x03' * 1000)
            
            # Create text file for comparison
            text_file = Path(tmpdir) / 'test.txt'
            text_file.write_text('Hello World' * 100)
            
            folder_capture = FolderContextCapture()
            result = await folder_capture.capture_folder(tmpdir, 'test-sub')
            
            # Binary file should be skipped or handled differently
            contexts = result['contexts']
            binary_context = next(
                (ctx for ctx in contexts if ctx['file_path'] == 'test.bin'),
                None
            )
            
            if binary_context:
                # If captured, should handle encoding properly
                assert binary_context.get('error') or binary_context.get('is_binary')
    
    @pytest.mark.asyncio
    async def test_unicode_content(self):
        """Test handling of Unicode content."""
        with tempfile.TemporaryDirectory() as tmpdir:
            # Create files with various encodings
            unicode_file = Path(tmpdir) / 'unicode.txt'
            unicode_file.write_text('Hello 世界 🌍 Здравствуй мир', encoding='utf-8')
            
            latin1_file = Path(tmpdir) / 'latin1.txt'
            latin1_file.write_text('Café résumé naïve', encoding='latin-1')
            
            folder_capture = FolderContextCapture()
            result = await folder_capture.capture_folder(tmpdir, 'test-sub')
            
            # Should handle different encodings
            assert result['file_count'] >= 2
            
            # Check Unicode content is preserved
            unicode_ctx = next(
                ctx for ctx in result['contexts']
                if ctx['file_path'] == 'unicode.txt'
            )
            assert '世界' in unicode_ctx['content']
            assert '🌍' in unicode_ctx['content']
    
    @pytest.mark.asyncio
    async def test_very_large_files(self):
        """Test handling of very large files."""
        with tempfile.TemporaryDirectory() as tmpdir:
            # Create a very large file (10MB)
            large_file = Path(tmpdir) / 'large.txt'
            chunk = 'x' * 1024  # 1KB chunk
            with open(large_file, 'w') as f:
                for _ in range(10 * 1024):  # 10MB
                    f.write(chunk)
            
            folder_capture = FolderContextCapture()
            
            # Should handle large files without memory issues
            result = await folder_capture.capture_folder(tmpdir, 'test-sub')
            
            large_ctx = next(
                ctx for ctx in result['contexts']
                if ctx['file_path'] == 'large.txt'
            )
            
            # Should truncate or skip very large files
            assert large_ctx['content_size'] > 1024 * 1024  # > 1MB
            assert large_ctx.get('truncated') or len(large_ctx['content']) < large_ctx['content_size']
    
    @pytest.mark.asyncio
    async def test_symlinks_and_special_files(self):
        """Test handling of symlinks and special files."""
        with tempfile.TemporaryDirectory() as tmpdir:
            base_path = Path(tmpdir)
            
            # Create regular file
            regular_file = base_path / 'regular.txt'
            regular_file.write_text('Regular content')
            
            # Create symlink
            symlink = base_path / 'link.txt'
            try:
                symlink.symlink_to(regular_file)
                
                folder_capture = FolderContextCapture()
                result = await folder_capture.capture_folder(tmpdir, 'test-sub')
                
                # Should handle symlinks appropriately
                # Either follow them or skip them
                assert result['file_count'] >= 1
            except OSError:
                # Skip test on systems that don't support symlinks
                pytest.skip("Symlinks not supported on this system")
    
    @pytest.mark.asyncio
    async def test_deeply_nested_directories(self):
        """Test handling of deeply nested directory structures."""
        with tempfile.TemporaryDirectory() as tmpdir:
            # Create deeply nested structure
            current = Path(tmpdir)
            for i in range(20):  # 20 levels deep
                current = current / f'level_{i}'
                current.mkdir()
                (current / f'file_{i}.txt').write_text(f'Content at level {i}')
            
            folder_capture = FolderContextCapture()
            result = await folder_capture.capture_folder(tmpdir, 'test-sub')
            
            # Should capture all levels
            assert result['file_count'] == 20
            
            # Check deepest file
            deepest = next(
                ctx for ctx in result['contexts']
                if 'level_19' in ctx['file_path']
            )
            assert 'Content at level 19' in deepest['content']
    
    def test_concurrent_git_operations(self):
        """Test concurrent git operations."""
        with tempfile.TemporaryDirectory() as tmpdir:
            # Initialize repo
            subprocess.run(['git', 'init'], cwd=tmpdir, check=True)
            subprocess.run(['git', 'config', 'user.name', 'Test'], cwd=tmpdir, check=True)
            subprocess.run(['git', 'config', 'user.email', '<EMAIL>'], cwd=tmpdir, check=True)
            
            # Create initial commit
            Path(tmpdir, 'file.txt').write_text('Initial')
            subprocess.run(['git', 'add', '.'], cwd=tmpdir, check=True)
            subprocess.run(['git', 'commit', '-m', 'Initial'], cwd=tmpdir, check=True)
            
            git_integration = GitIntegration(tmpdir)
            
            # Create multiple worktrees
            worktrees = []
            for i in range(3):
                wt = git_integration.create_worktree(f'wt-{i}')
                worktrees.append(wt)
            
            try:
                # Verify all worktrees exist
                wt_list = git_integration.list_worktrees()
                assert len(wt_list) >= 4  # main + 3 worktrees
                
                # Make changes in each worktree
                for i, wt in enumerate(worktrees):
                    (wt / f'file_{i}.txt').write_text(f'Content {i}')
                    subprocess.run(['git', 'add', '.'], cwd=wt, check=True)
                    subprocess.run(['git', 'commit', '-m', f'Change {i}'], cwd=wt, check=True)
                
            finally:
                # Clean up all worktrees
                for i in range(3):
                    git_integration.remove_worktree(f'wt-{i}')
    
    @pytest.mark.asyncio
    async def test_malformed_gitignore(self):
        """Test handling of malformed .gitignore patterns."""
        with tempfile.TemporaryDirectory() as tmpdir:
            # Create malformed .gitignore
            gitignore = Path(tmpdir) / '.gitignore'
            gitignore.write_text('''
# Valid patterns
*.pyc
__pycache__/

# Malformed patterns
[invalid
***/broken
\\escape\\issues
/absolute/path/that/is/very/long/and/might/cause/issues
!!!not_a_pattern
            ''')
            
            # Should not crash
            parser = GitignoreParser(tmpdir)
            
            # Test with various paths
            assert parser.should_ignore('test.pyc')
            assert parser.should_ignore('__pycache__/file.py')
            
            # Malformed patterns should be handled gracefully
            assert not parser.should_ignore('[invalid')
    
    @pytest.mark.asyncio
    async def test_token_estimation_edge_cases(self):
        """Test token estimation with edge cases."""
        estimator = TokenEstimator()
        
        # Empty content
        tokens, details = estimator.estimate_tokens('', 'empty.txt')
        assert tokens == 0
        
        # Very long single line
        long_line = 'x' * 10000
        tokens, details = estimator.estimate_tokens(long_line, 'long.txt')
        assert tokens > 0
        assert tokens < 50000  # Should not explode
        
        # Special characters and emojis
        special = '🚀 Special chars: < > & " \' \\n \\t'
        tokens, details = estimator.estimate_tokens(special, 'special.txt')
        assert tokens > 0
        
        # Minified code
        minified = 'function a(b,c){return b+c;}var d=a(1,2);console.log(d);' * 100
        tokens, details = estimator.estimate_tokens(minified, 'min.js')
        assert tokens > 0
        assert details['file_type'] == 'javascript'
    
    @pytest.mark.asyncio
    async def test_blob_storage_edge_cases(self):
        """Test blob storage edge cases."""
        with tempfile.TemporaryDirectory() as tmpdir:
            blob_storage = BlobStorage(base_path=tmpdir)
            
            # Empty content
            result = await blob_storage.store_blob(b'', {'test': 'empty'})
            assert result['blob_id']
            retrieved = await blob_storage.retrieve_blob(result['blob_id'])
            assert retrieved == b''
            
            # Very large blob (5MB)
            large_data = b'x' * (5 * 1024 * 1024)
            result = await blob_storage.store_blob(large_data, {'test': 'large'})
            assert result['compressed_size'] < len(large_data)
            
            # Invalid blob ID
            invalid = await blob_storage.retrieve_blob('invalid-id-12345')
            assert invalid is None
            
            # Concurrent access
            blob_ids = []
            for i in range(10):
                data = f'Concurrent {i}'.encode()
                result = await blob_storage.store_blob(data, {'index': i})
                blob_ids.append(result['blob_id'])
            
            # Retrieve all
            for i, blob_id in enumerate(blob_ids):
                data = await blob_storage.retrieve_blob(blob_id)
                assert data == f'Concurrent {i}'.encode()
    
    def test_git_error_scenarios(self):
        """Test git operation error scenarios."""
        with tempfile.TemporaryDirectory() as tmpdir:
            # Not a git repo
            with pytest.raises(ValueError):
                GitIntegration(tmpdir)
            
            # Initialize repo for further tests
            subprocess.run(['git', 'init'], cwd=tmpdir, check=True)
            subprocess.run(['git', 'config', 'user.name', 'Test'], cwd=tmpdir, check=True)
            subprocess.run(['git', 'config', 'user.email', '<EMAIL>'], cwd=tmpdir, check=True)
            
            git_integration = GitIntegration(tmpdir)
            
            # Invalid commit operations
            info = git_integration.get_commit_info('nonexistent')
            # Should handle error in _run_git_command
            
            # File at invalid commit
            content = git_integration.get_file_at_commit('any.txt', 'invalid-hash')
            assert content is None
            
            # Diff between invalid commits
            try:
                diff = git_integration.get_diff_between_commits('invalid1', 'invalid2')
                # Should handle error gracefully
            except subprocess.CalledProcessError:
                pass  # Expected
    
    @pytest.mark.asyncio
    async def test_permission_errors(self):
        """Test handling of permission errors."""
        if os.name == 'nt':  # Windows
            pytest.skip("Permission test not applicable on Windows")
        
        with tempfile.TemporaryDirectory() as tmpdir:
            # Create read-only directory
            readonly_dir = Path(tmpdir) / 'readonly'
            readonly_dir.mkdir()
            file_in_readonly = readonly_dir / 'file.txt'
            file_in_readonly.write_text('Content')
            
            # Make directory read-only
            os.chmod(readonly_dir, 0o444)
            
            try:
                # Blob storage should handle permission errors
                blob_storage = BlobStorage(base_path=str(readonly_dir / 'blobs'))
                # Should fall back to temp directory
                
                # Folder capture should handle permission errors
                folder_capture = FolderContextCapture()
                result = await folder_capture.capture_folder(tmpdir, 'test-sub')
                # Should capture what it can
                
            finally:
                # Restore permissions for cleanup
                os.chmod(readonly_dir, 0o755)