"""Tests for git context capture service."""
import pytest
import tempfile
import subprocess
from pathlib import Path
from app.services.git_context_capture import GitContextCapture
from app.services.token_estimator import TokenEstimator


class TestGitContextCapture:
    """Test cases for GitContextCapture."""
    
    @pytest.fixture
    def capture_service(self):
        """Create a GitContextCapture instance."""
        return GitContextCapture()
    
    @pytest.fixture
    def git_repo(self):
        """Create a test git repository."""
        with tempfile.TemporaryDirectory() as tmpdir:
            repo_path = Path(tmpdir)
            
            # Initialize repo
            subprocess.run(['git', 'init'], cwd=repo_path, check=True)
            subprocess.run(['git', 'config', 'user.name', 'Test Agent'], cwd=repo_path, check=True)
            subprocess.run(['git', 'config', 'user.email', '<EMAIL>'], cwd=repo_path, check=True)
            
            # Initial commit
            (repo_path / 'README.md').write_text('# Test Project\n\nInitial version')
            subprocess.run(['git', 'add', '.'], cwd=repo_path, check=True)
            subprocess.run(['git', 'commit', '-m', 'Initial commit'], cwd=repo_path, check=True)
            
            # Feature commit 1
            (repo_path / 'app.py').write_text('''def main():
    print("Hello World")
    
if __name__ == "__main__":
    main()
''')
            subprocess.run(['git', 'add', 'app.py'], cwd=repo_path, check=True)
            subprocess.run(['git', 'commit', '-m', 'Add main application'], cwd=repo_path, check=True)
            
            # Feature commit 2
            (repo_path / 'utils.py').write_text('''def format_output(text):
    return f"[OUTPUT] {text}"
''')
            subprocess.run(['git', 'add', 'utils.py'], cwd=repo_path, check=True)
            subprocess.run(['git', 'commit', '-m', 'Add utilities module'], cwd=repo_path, check=True)
            
            # Feature commit 3 - Update app.py
            (repo_path / 'app.py').write_text('''from utils import format_output

def main():
    message = "Hello World"
    formatted = format_output(message)
    print(formatted)
    
if __name__ == "__main__":
    main()
''')
            subprocess.run(['git', 'add', 'app.py'], cwd=repo_path, check=True)
            subprocess.run(['git', 'commit', '-m', 'Integrate utils into main app'], cwd=repo_path, check=True)
            
            # Get commit hashes
            result = subprocess.run(
                ['git', 'log', '--format=%H', '-n', '4'],
                cwd=repo_path,
                capture_output=True,
                text=True,
                check=True
            )
            commits = result.stdout.strip().split('\n')
            
            yield {
                'path': repo_path,
                'commits': commits  # [latest, utils, app, initial]
            }
    
    @pytest.mark.asyncio
    async def test_capture_commit_context(self, capture_service, git_repo):
        """Test capturing context from a single commit."""
        repo_path = str(git_repo['path'])
        commit_hash = git_repo['commits'][1]  # utils.py commit
        submission_id = "test-sub-123"
        
        result = await capture_service.capture_commit_context(
            repo_path,
            commit_hash,
            submission_id,
            include_diff=True,
            include_files=True
        )
        
        assert result['submission_id'] == submission_id
        assert result['commit_hash'] == commit_hash
        assert result['total_tokens'] > 0
        assert len(result['contexts']) > 0
        
        # Check context types
        context_types = [ctx['context_type'] for ctx in result['contexts']]
        assert 'commit_metadata' in context_types
        assert 'commit_diff' in context_types
        assert 'file' in context_types
        
        # Find metadata context
        metadata_ctx = next(
            ctx for ctx in result['contexts'] 
            if ctx['context_type'] == 'commit_metadata'
        )
        assert 'Add utilities module' in metadata_ctx['content']
        
        # Find file context
        file_ctx = next(
            ctx for ctx in result['contexts']
            if ctx['context_type'] == 'file' and ctx.get('file_path') == 'utils.py'
        )
        assert 'format_output' in file_ctx['content']
    
    @pytest.mark.asyncio
    async def test_capture_commit_without_diff(self, capture_service, git_repo):
        """Test capturing commit without diff."""
        repo_path = str(git_repo['path'])
        commit_hash = git_repo['commits'][2]  # app.py commit
        
        result = await capture_service.capture_commit_context(
            repo_path,
            commit_hash,
            "test-sub",
            include_diff=False,
            include_files=True
        )
        
        # Should not have diff context
        context_types = [ctx['context_type'] for ctx in result['contexts']]
        assert 'commit_diff' not in context_types
        assert 'commit_metadata' in context_types
        assert 'file' in context_types
    
    @pytest.mark.asyncio
    async def test_capture_commit_without_files(self, capture_service, git_repo):
        """Test capturing commit without file contents."""
        repo_path = str(git_repo['path'])
        commit_hash = git_repo['commits'][0]  # latest commit
        
        result = await capture_service.capture_commit_context(
            repo_path,
            commit_hash,
            "test-sub",
            include_diff=True,
            include_files=False
        )
        
        # Should only have metadata and diff
        context_types = [ctx['context_type'] for ctx in result['contexts']]
        assert 'file' not in context_types
        assert 'commit_metadata' in context_types
        assert 'commit_diff' in context_types
    
    @pytest.mark.asyncio
    async def test_capture_commit_range_context(self, capture_service, git_repo):
        """Test capturing context from commit range."""
        repo_path = str(git_repo['path'])
        base_commit = git_repo['commits'][3]  # initial commit
        target_commit = git_repo['commits'][0]  # latest commit
        
        result = await capture_service.capture_commit_range_context(
            repo_path,
            base_commit,
            target_commit,
            "test-sub",
            include_intermediate=True
        )
        
        assert result['base_commit'] == base_commit
        assert result['target_commit'] == target_commit
        assert result['total_tokens'] > 0
        assert len(result['contexts']) > 0
        assert len(result['commits_captured']) == 3  # 3 commits in range
        
        # Check for range diff
        range_diff_ctx = next(
            ctx for ctx in result['contexts']
            if ctx['context_type'] == 'range_diff'
        )
        assert range_diff_ctx is not None
        
        # Check that we have contexts for intermediate commits
        commit_metadata_contexts = [
            ctx for ctx in result['contexts']
            if ctx['context_type'] == 'commit_metadata'
        ]
        assert len(commit_metadata_contexts) >= 3
    
    @pytest.mark.asyncio
    async def test_capture_commit_range_without_intermediate(self, capture_service, git_repo):
        """Test capturing commit range without intermediate commits."""
        repo_path = str(git_repo['path'])
        base_commit = git_repo['commits'][2]  # app.py commit
        target_commit = git_repo['commits'][0]  # latest commit
        
        result = await capture_service.capture_commit_range_context(
            repo_path,
            base_commit,
            target_commit,
            "test-sub",
            include_intermediate=False
        )
        
        assert len(result['commits_captured']) == 0
        
        # Should still have range diff and final files
        context_types = [ctx['context_type'] for ctx in result['contexts']]
        assert 'range_diff' in context_types
        assert 'file' in context_types
    
    @pytest.mark.asyncio
    async def test_estimate_commit_tokens(self, capture_service, git_repo):
        """Test token estimation for commits."""
        repo_path = str(git_repo['path'])
        commit_hash = git_repo['commits'][1]  # utils.py commit (adds new file)
        
        result = await capture_service.estimate_commit_tokens(
            repo_path,
            commit_hash,
            include_diff=True,
            include_files=True
        )
        
        assert result['commit_hash'] == commit_hash
        assert result['total_tokens'] > 0
        assert result['metadata_tokens'] > 0
        assert result['diff_tokens'] > 0
        assert result['file_tokens'] > 0
        assert result['file_count'] > 0
        
        # Test without files
        result_no_files = await capture_service.estimate_commit_tokens(
            repo_path,
            commit_hash,
            include_diff=True,
            include_files=False
        )
        
        assert result_no_files['file_tokens'] == 0
        assert result_no_files['file_count'] == 0
        # When files are included, total tokens should be higher or equal
        # (could be equal if file is very small)
        assert result_no_files['total_tokens'] <= result['total_tokens']
    
    @pytest.mark.asyncio
    async def test_file_type_detection(self, capture_service, git_repo):
        """Test file type detection in commit contexts."""
        repo_path = str(git_repo['path'])
        
        # Add various file types
        test_files = {
            'config.json': '{"test": true}',
            'styles.css': 'body { color: red; }',
            'Dockerfile': 'FROM python:3.9',
            'script.sh': '#!/bin/bash\necho "test"'
        }
        
        for filename, content in test_files.items():
            (git_repo['path'] / filename).write_text(content)
        
        subprocess.run(['git', 'add', '.'], cwd=git_repo['path'], check=True)
        subprocess.run(['git', 'commit', '-m', 'Add various file types'], cwd=git_repo['path'], check=True)
        
        # Get new commit hash
        result = subprocess.run(
            ['git', 'rev-parse', 'HEAD'],
            cwd=git_repo['path'],
            capture_output=True,
            text=True,
            check=True
        )
        commit_hash = result.stdout.strip()
        
        # Capture context
        context_result = await capture_service.capture_commit_context(
            repo_path,
            commit_hash,
            "test-sub",
            include_files=True
        )
        
        # Check file types
        file_contexts = [
            ctx for ctx in context_result['contexts']
            if ctx['context_type'] == 'file'
        ]
        
        type_map = {
            'config.json': 'json',
            'styles.css': 'css',
            'Dockerfile': 'dockerfile',
            'script.sh': 'shell'
        }
        
        for file_ctx in file_contexts:
            file_path = file_ctx['file_path']
            if file_path in type_map:
                expected_type = type_map[file_path]
                actual_type = file_ctx['file_metadata']['type']
                assert actual_type == expected_type, f"{file_path} should be {expected_type}"