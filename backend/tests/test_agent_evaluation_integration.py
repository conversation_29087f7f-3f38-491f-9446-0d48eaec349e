"""Integration tests for the complete agent evaluation workflow."""
import pytest
import asyncio
import tempfile
import os
import json
from pathlib import Path
from unittest.mock import Mock, AsyncMock, patch
from sqlalchemy.ext.asyncio import AsyncSession

from app.services.folder_context_capture import FolderContextCapture
from app.services.git_context_capture import GitContextCapture
from app.services.context_storage import ContextStorage
from app.services.evaluation_pipeline import EvaluationPipeline
from app.services.criteria_generator import CriteriaGenerator
from app.services.multi_model_evaluator import MultiModelEvaluator
from app.schemas.agent_evaluation import AgentEvaluationTaskCreate


class TestAgentEvaluationIntegration:
    """Test complete agent evaluation workflows."""
    
    @pytest.fixture
    async def mock_db(self):
        """Create mock database session."""
        return AsyncMock(spec=AsyncSession)
    
    @pytest.fixture
    def temp_folder(self):
        """Create temporary folder with test files."""
        with tempfile.TemporaryDirectory() as tmpdir:
            # Create test project structure
            project_dir = Path(tmpdir) / "test-project"
            project_dir.mkdir()
            
            # Add some files
            (project_dir / "main.py").write_text("""
def hello_world():
    return "Hello, World!"

if __name__ == "__main__":
    print(hello_world())
""")
            
            (project_dir / "README.md").write_text("""
# Test Project

A simple test project for evaluation.
""")
            
            (project_dir / "requirements.txt").write_text("flask==2.0.1\nrequests==2.26.0")
            
            # Add .gitignore
            (project_dir / ".gitignore").write_text("__pycache__/\n*.pyc\n.env")
            
            yield str(project_dir)
    
    @pytest.fixture
    def mock_llm_service(self):
        """Create mock LLM service."""
        mock = Mock()
        mock.generate = AsyncMock()
        return mock
    
    @pytest.mark.asyncio
    async def test_0_1_evaluation_workflow(self, mock_db, temp_folder, mock_llm_service):
        """Test complete 0-1 evaluation workflow."""
        # Initialize services
        folder_capture = FolderContextCapture()
        context_storage = ContextStorage()
        criteria_generator = CriteriaGenerator(llm_service=mock_llm_service)
        evaluator = MultiModelEvaluator(llm_service=mock_llm_service)
        pipeline = EvaluationPipeline(
            criteria_generator=criteria_generator,
            multi_model_evaluator=evaluator,
            context_storage=context_storage
        )
        
        # Mock LLM responses
        mock_llm_service.generate.side_effect = [
            # Criteria generation response
            Mock(
                output_text=json.dumps([{
                    "name": "Code Quality",
                    "description": "Evaluates code structure and practices",
                    "criteria_type": "quality",
                    "weight": 0.5,
                    "components": [{
                        "name": "Structure",
                        "description": "Code organization",
                        "weight": 1.0,
                        "evaluation_method": "Analysis",
                        "scoring_rubric": {"0-100": "Full range"}
                    }],
                    "evaluation_prompt_template": "Evaluate code quality"
                }]),
                prompt_tokens=100,
                completion_tokens=200
            ),
            # Evaluation response
            Mock(
                output_text=json.dumps({
                    "component_scores": [{
                        "name": "Structure",
                        "score": 85,
                        "reasoning": "Well organized",
                        "evidence": ["Clear structure"]
                    }],
                    "overall_reasoning": "Good quality",
                    "confidence": 0.9
                }),
                prompt_tokens=500,
                completion_tokens=100
            )
        ]
        
        # Mock database operations
        task_id = "test-task-123"
        submission_id = "test-submission-456"
        
        # Step 1: Capture folder context
        result = await folder_capture.capture_folder(temp_folder, submission_id)
        
        assert result['status'] == 'success'
        assert result['total_files'] == 3  # main.py, README.md, requirements.txt
        assert result['total_tokens'] > 0
        assert len(result['contexts']) == 3
        
        # Step 2: Store contexts (mocked)
        with patch.object(context_storage, 'store_contexts', new_callable=AsyncMock) as mock_store:
            mock_store.return_value = {'stored_count': 3}
            
            stored = await context_storage.store_contexts(
                result['contexts'],
                submission_id,
                mock_db
            )
            
            assert stored['stored_count'] == 3
        
        # Step 3: Generate criteria
        criteria, usage_stats = await criteria_generator.generate_criteria(
            "Build a simple Python application",
            "0-1",
            num_criteria=1
        )
        
        assert len(criteria) == 1
        assert criteria[0].name == "Code Quality"
        
        # Step 4: Mock evaluation
        with patch('app.services.multi_model_evaluator.crud_evaluation') as mock_crud_eval:
            with patch('app.services.multi_model_evaluator.crud_result') as mock_crud_result:
                mock_crud_eval.create = AsyncMock(return_value=Mock(id='eval-123'))
                mock_crud_eval.update = AsyncMock()
                mock_crud_result.create = AsyncMock()
                
                # Mock context retrieval
                with patch.object(evaluator.context_storage, 'retrieve_submission_contexts') as mock_retrieve:
                    mock_retrieve.return_value = result['contexts']
                    
                    # Run evaluation
                    eval_result = await evaluator.evaluate_submission(
                        submission_id,
                        [c.dict() for c in criteria],
                        mock_db,
                        models=['test-model']
                    )
                    
                    assert 'total_evaluations' in eval_result
                    assert eval_result['total_evaluations'] == 1
    
    @pytest.mark.asyncio
    async def test_90_100_evaluation_workflow(self, mock_db, mock_llm_service):
        """Test 90-100 evaluation workflow with git."""
        # Create a temporary git repository
        with tempfile.TemporaryDirectory() as tmpdir:
            repo_path = Path(tmpdir) / "test-repo"
            repo_path.mkdir()
            
            # Initialize git repo
            os.system(f"cd {repo_path} && git init")
            os.system(f"cd {repo_path} && git config user.email '<EMAIL>'")
            os.system(f"cd {repo_path} && git config user.name 'Test User'")
            
            # Create initial commit
            (repo_path / "app.py").write_text("def main():\n    pass")
            os.system(f"cd {repo_path} && git add . && git commit -m 'Initial commit'")
            
            # Create improvement commit
            (repo_path / "app.py").write_text("""
def main():
    '''Main application entry point.'''
    print("Hello, World!")

if __name__ == "__main__":
    main()
""")
            os.system(f"cd {repo_path} && git add . && git commit -m 'Add hello world'")
            
            # Get commit hash
            import subprocess
            result = subprocess.run(
                ["git", "rev-parse", "HEAD"],
                cwd=repo_path,
                capture_output=True,
                text=True
            )
            commit_hash = result.stdout.strip()
            
            # Initialize services
            git_capture = GitContextCapture()
            
            # Capture commit context
            submission_id = "test-git-submission"
            capture_result = await git_capture.capture_commit_context(
                str(repo_path),
                commit_hash,
                submission_id
            )
            
            assert capture_result['status'] == 'success'
            assert capture_result['total_tokens'] > 0
            assert any(ctx['context_type'] == 'diff' for ctx in capture_result['contexts'])
    
    @pytest.mark.asyncio
    async def test_evaluation_pipeline_with_progress(self, mock_db, mock_llm_service):
        """Test evaluation pipeline with progress tracking."""
        pipeline = EvaluationPipeline()
        
        # Track progress updates
        progress_updates = []
        
        async def progress_callback(progress):
            progress_updates.append(progress)
        
        pipeline.add_progress_callback(progress_callback)
        
        # Mock task and submissions
        with patch.object(pipeline, '_generate_and_store_criteria') as mock_criteria:
            with patch.object(pipeline.evaluator, 'evaluate_submission') as mock_eval:
                with patch('app.services.evaluation_pipeline.crud_task') as mock_crud_task:
                    with patch('app.services.evaluation_pipeline.crud_submission') as mock_crud_sub:
                        with patch('app.services.evaluation_pipeline.crud_result') as mock_crud_result:
                            # Mock data
                            mock_task = Mock(
                                id='task-123',
                                name='Test Task',
                                evaluation_type='0-1',
                                prompt='Test prompt',
                                status='ready'
                            )
                            mock_submissions = [
                                Mock(
                                    id='sub-1',
                                    agent_name='Agent A',
                                    agent_version='1.0'
                                )
                            ]
                            
                            mock_crud_task.get = AsyncMock(return_value=mock_task)
                            mock_crud_task.update = AsyncMock()
                            mock_crud_sub.get_by_task = AsyncMock(return_value=mock_submissions)
                            mock_crud_result.create = AsyncMock()
                            
                            mock_criteria.return_value = [{
                                'id': 'crit-1',
                                'name': 'Test Criterion',
                                'weight': 1.0
                            }]
                            
                            mock_eval.return_value = {
                                'by_criterion': {
                                    'crit-1': {
                                        'mean_score': 80.0,
                                        'model_scores': {'model1': 80.0}
                                    }
                                },
                                'overall': {'mean_score': 80.0}
                            }
                            
                            # Run evaluation
                            result = await pipeline.run_evaluation(
                                'task-123',
                                mock_db,
                                evaluation_models=['test-model']
                            )
                            
                            # Verify progress updates
                            assert len(progress_updates) >= 6  # At least 6 steps
                            assert progress_updates[0]['current_step'] == 1
                            assert progress_updates[-1]['current_step'] == 6
                            
                            # Verify result
                            assert result['status'] == 'completed'
                            assert result['submissions_evaluated'] == 1
    
    @pytest.mark.asyncio
    async def test_error_handling_in_pipeline(self, mock_db):
        """Test error handling in evaluation pipeline."""
        pipeline = EvaluationPipeline()
        
        # Test with non-existent task
        with patch('app.services.evaluation_pipeline.crud_task') as mock_crud:
            mock_crud.get = AsyncMock(return_value=None)
            
            with pytest.raises(ValueError, match="Task .* not found"):
                await pipeline.run_evaluation('non-existent', mock_db)
        
        # Test with no submissions
        with patch('app.services.evaluation_pipeline.crud_task') as mock_crud_task:
            with patch('app.services.evaluation_pipeline.crud_submission') as mock_crud_sub:
                mock_crud_task.get = AsyncMock(return_value=Mock(id='task-123'))
                mock_crud_sub.get_by_task = AsyncMock(return_value=[])
                
                with pytest.raises(ValueError, match="No submissions found"):
                    await pipeline.run_evaluation('task-123', mock_db)
    
    @pytest.mark.asyncio
    async def test_token_estimation_accuracy(self, temp_folder):
        """Test token estimation accuracy across different file types."""
        folder_capture = FolderContextCapture()
        
        # Create files with known content
        test_files = {
            "simple.py": "print('hello')",  # ~5 tokens
            "complex.py": "\n".join([f"def function_{i}():\n    pass" for i in range(10)]),  # ~100 tokens
            "data.json": json.dumps({"key": "value"} for _ in range(20)),  # ~200 tokens
        }
        
        for filename, content in test_files.items():
            (Path(temp_folder) / filename).write_text(content)
        
        # Estimate tokens
        result = await folder_capture.estimate_folder_tokens(temp_folder)
        
        assert result['status'] == 'success'
        assert result['total_tokens'] > 0
        assert result['file_count'] == len(test_files) + 3  # +3 for existing files
        
        # Check breakdown
        assert 'breakdown' in result
        assert any('simple.py' in str(item) for item in result['breakdown'])