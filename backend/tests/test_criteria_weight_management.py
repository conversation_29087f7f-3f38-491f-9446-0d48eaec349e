"""
Tests for criteria weight management features.
"""
import pytest
import asyncio
from httpx import AsyncClient
from sqlalchemy import text

from app.main import app

pytestmark = pytest.mark.asyncio

# ---------- fixtures ----------
@pytest.fixture
async def client():
    async with AsyncClient(app=app, base_url="http://test") as ac:
        yield ac

# ---------- helpers ----------
HEADERS = {"X-User-ID": "test-user-weights"}

async def _create_task(client):
    resp = await client.post("/api/v1/agent-evaluations/tasks",
                             headers=HEADERS,
                             json={"name": "pytest-task-weights", "evaluation_type": "0-1"})
    resp.raise_for_status()
    return resp.json()["id"]

async def _create_criteria_with_components(client, task_id, name, weight, components):
    body = {
        "name": name,
        "description": f"Criteria with weight {weight}",
        "weight": weight,
        "criteria_type": "quality",
        "evaluation_prompt": "Evaluate based on criteria",
        "meta_data": {"components": components}
    }
    resp = await client.post(f"/api/v1/agent-evaluations/tasks/{task_id}/criteria",
                             headers=HEADERS, json=body)
    resp.raise_for_status()
    return resp.json()

# ---------- tests ----------
async def test_criteria_weight_updates(async_client, db_session):
    """Test updating weights for multiple criteria."""
    task_id = await _create_task(async_client)
    
    # Create multiple criteria with different weights
    criteria1 = await _create_criteria_with_components(
        async_client, task_id, "Criteria 1", 0.4,
        [
            {"name": "Component 1A", "description": "First", "weight": 0.5, "evaluation_method": "Review"},
            {"name": "Component 1B", "description": "Second", "weight": 0.5, "evaluation_method": "Analysis"}
        ]
    )
    
    criteria2 = await _create_criteria_with_components(
        async_client, task_id, "Criteria 2", 0.3,
        [
            {"name": "Component 2A", "description": "First", "weight": 1.0, "evaluation_method": "Review"}
        ]
    )
    
    criteria3 = await _create_criteria_with_components(
        async_client, task_id, "Criteria 3", 0.3,
        [
            {"name": "Component 3A", "description": "First", "weight": 0.7, "evaluation_method": "Review"},
            {"name": "Component 3B", "description": "Second", "weight": 0.3, "evaluation_method": "Analysis"}
        ]
    )
    
    # Update weights to sum to 1.0
    new_weights = [
        (criteria1["id"], 0.5),
        (criteria2["id"], 0.25),
        (criteria3["id"], 0.25)
    ]
    
    for crit_id, new_weight in new_weights:
        resp = await async_client.put(f"/api/v1/agent-evaluations/criteria/{crit_id}",
                                      headers=HEADERS, json={"weight": new_weight})
        assert resp.status_code == 200
        updated = resp.json()
        assert updated["weight"] == new_weight
    
    # Verify all weights
    resp = await async_client.get(f"/api/v1/agent-evaluations/tasks/{task_id}/criteria")
    assert resp.status_code == 200
    all_criteria = resp.json()
    
    total_weight = sum(c["weight"] for c in all_criteria)
    assert abs(total_weight - 1.0) < 0.001  # Allow small floating point differences

async def test_component_weight_normalization(async_client, db_session):
    """Test component weight normalization within criteria."""
    task_id = await _create_task(async_client)
    
    # Create criteria with components that don't sum to 1.0
    components = [
        {
            "name": "Component A",
            "description": "First component",
            "weight": 0.3,
            "evaluation_method": "Review",
            "scoring_rubric": {
                "0-30": "Poor",
                "31-60": "Fair",
                "61-80": "Good",
                "81-100": "Excellent"
            }
        },
        {
            "name": "Component B",
            "description": "Second component",
            "weight": 0.4,
            "evaluation_method": "Analysis",
            "scoring_rubric": {
                "0-30": "Poor",
                "31-60": "Fair",
                "61-80": "Good",
                "81-100": "Excellent"
            }
        }
    ]
    
    criteria = await _create_criteria_with_components(
        async_client, task_id, "Test Criteria", 1.0, components
    )
    criteria_id = criteria["id"]
    
    # Backend stores whatever is sent - normalization is frontend responsibility
    assert len(criteria["meta_data"]["components"]) == 2
    total_component_weight = sum(c["weight"] for c in criteria["meta_data"]["components"])
    assert total_component_weight == 0.7  # 0.3 + 0.4
    
    # Update with normalized weights
    normalized_components = [
        {
            "name": "Component A",
            "description": "First component",
            "weight": 0.45,  # Normalized from 0.3
            "evaluation_method": "Review",
            "scoring_rubric": {
                "0-30": "Poor",
                "31-60": "Fair",
                "61-80": "Good",
                "81-100": "Excellent"
            }
        },
        {
            "name": "Component B",
            "description": "Second component",
            "weight": 0.55,  # Normalized from 0.4
            "evaluation_method": "Analysis",
            "scoring_rubric": {
                "0-30": "Poor",
                "31-60": "Fair",
                "61-80": "Good",
                "81-100": "Excellent"
            }
        }
    ]
    
    resp = await async_client.post(f"/api/v1/agent-evaluations/criteria/{criteria_id}/components/batch",
                                   headers=HEADERS, json=normalized_components)
    assert resp.status_code == 200
    
    # Verify normalization
    resp = await async_client.get(f"/api/v1/agent-evaluations/tasks/{task_id}/criteria")
    assert resp.status_code == 200
    criteria_list = resp.json()
    updated_criteria = next(c for c in criteria_list if c["id"] == criteria_id)
    
    total_normalized = sum(c["weight"] for c in updated_criteria["meta_data"]["components"])
    assert abs(total_normalized - 1.0) < 0.001

async def test_weight_precision(async_client, db_session):
    """Test that weights are stored with proper precision (0.05 increments)."""
    task_id = await _create_task(async_client)
    
    # Test various weight values
    test_weights = [0.05, 0.1, 0.15, 0.2, 0.25, 0.5, 0.75, 0.95, 1.0]
    
    for i, weight in enumerate(test_weights):
        criteria = await _create_criteria_with_components(
            async_client, task_id, f"Criteria {i+1}", weight,
            [{"name": "Component", "description": "Test", "weight": 1.0, "evaluation_method": "Review"}]
        )
        assert criteria["weight"] == weight
        
        # Update with 0.05 increment
        new_weight = min(1.0, weight + 0.05)
        resp = await async_client.put(f"/api/v1/agent-evaluations/criteria/{criteria['id']}",
                                      headers=HEADERS, json={"weight": new_weight})
        assert resp.status_code == 200
        updated = resp.json()
        assert updated["weight"] == new_weight

async def test_zero_weight_handling(async_client, db_session):
    """Test handling of zero or very small weights."""
    task_id = await _create_task(async_client)
    
    # Backend might accept zero weight, but frontend should prevent it
    criteria = await _create_criteria_with_components(
        async_client, task_id, "Zero Weight Test", 0.0,
        [{"name": "Component", "description": "Test", "weight": 0.0, "evaluation_method": "Review"}]
    )
    
    # The backend stores what's sent - validation is frontend responsibility
    assert criteria["weight"] == 0.0
    
    # Test updating to minimum weight (0.05)
    resp = await async_client.put(f"/api/v1/agent-evaluations/criteria/{criteria['id']}",
                                  headers=HEADERS, json={"weight": 0.05})
    assert resp.status_code == 200
    updated = resp.json()
    assert updated["weight"] == 0.05

async def test_batch_weight_update_performance(async_client, db_session):
    """Test performance of updating multiple criteria weights."""
    task_id = await _create_task(async_client)
    
    # Create 10 criteria
    criteria_ids = []
    for i in range(10):
        criteria = await _create_criteria_with_components(
            async_client, task_id, f"Criteria {i+1}", 0.1,
            [{"name": f"Component {i+1}", "description": "Test", "weight": 1.0, "evaluation_method": "Review"}]
        )
        criteria_ids.append(criteria["id"])
    
    # Update all weights in parallel (simulating frontend batch operation)
    import time
    start_time = time.time()
    
    update_tasks = []
    for i, crit_id in enumerate(criteria_ids):
        new_weight = (i + 1) * 0.1 if i < 9 else 0.1  # Sum to 1.0
        update_task = async_client.put(
            f"/api/v1/agent-evaluations/criteria/{crit_id}",
            headers=HEADERS,
            json={"weight": new_weight}
        )
        update_tasks.append(update_task)
    
    # Execute all updates concurrently
    responses = await asyncio.gather(*update_tasks)
    
    end_time = time.time()
    
    # All should succeed
    for resp in responses:
        assert resp.status_code == 200
    
    # Should complete reasonably quickly (under 2 seconds for 10 updates)
    assert end_time - start_time < 2.0
    
    # Verify total weight
    resp = await async_client.get(f"/api/v1/agent-evaluations/tasks/{task_id}/criteria")
    assert resp.status_code == 200
    all_criteria = resp.json()
    total_weight = sum(c["weight"] for c in all_criteria)
    assert abs(total_weight - 1.0) < 0.001

async def test_component_weight_edge_cases(async_client, db_session):
    """Test edge cases for component weight management."""
    task_id = await _create_task(async_client)
    
    # Test with many components
    many_components = []
    for i in range(20):
        many_components.append({
            "name": f"Component {i+1}",
            "description": f"Component number {i+1}",
            "weight": 0.05,  # 20 * 0.05 = 1.0
            "evaluation_method": "Review",
            "scoring_rubric": {
                "0-30": "Poor",
                "31-60": "Fair",
                "61-80": "Good",
                "81-100": "Excellent"
            }
        })
    
    criteria = await _create_criteria_with_components(
        async_client, task_id, "Many Components", 1.0, many_components
    )
    
    assert len(criteria["meta_data"]["components"]) == 20
    total_weight = sum(c["weight"] for c in criteria["meta_data"]["components"])
    assert abs(total_weight - 1.0) < 0.001
    
    # Test with single component (weight must be 1.0)
    single_component = [{
        "name": "Single Component",
        "description": "Only component",
        "weight": 1.0,
        "evaluation_method": "Comprehensive Review",
        "scoring_rubric": {
            "0-30": "Poor",
            "31-60": "Fair",
            "61-80": "Good",
            "81-100": "Excellent"
        }
    }]
    
    criteria2 = await _create_criteria_with_components(
        async_client, task_id, "Single Component Criteria", 1.0, single_component
    )
    
    assert len(criteria2["meta_data"]["components"]) == 1
    assert criteria2["meta_data"]["components"][0]["weight"] == 1.0