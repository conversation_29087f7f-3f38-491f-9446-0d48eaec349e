"""
Tests for enhanced human evaluation features including single score per criteria rule.
"""
import uuid
import pytest
import asyncio
from httpx import AsyncClient
from sqlalchemy import text

from app.main import app

pytestmark = pytest.mark.asyncio

# ---------- fixtures ----------
@pytest.fixture
async def client():
    async with Async<PERSON><PERSON>(app=app, base_url="http://test") as ac:
        yield ac

# ---------- helpers ----------
HEADERS_USER1 = {"X-User-ID": "test-user-evaluator-1"}
HEADERS_USER2 = {"X-User-ID": "test-user-evaluator-2"}

async def _create_task_with_criteria(client):
    """Create a task with evaluation criteria."""
    # Create task
    resp = await client.post("/api/v1/agent-evaluations/tasks",
                             headers=HEADERS_USER1,
                             json={"name": "pytest-human-eval-task", "evaluation_type": "0-1"})
    resp.raise_for_status()
    task_id = resp.json()["id"]
    
    # Create criteria
    criteria_data = [
        {
            "name": "Code Quality",
            "description": "Evaluate code quality",
            "weight": 0.5,
            "criteria_type": "quality",
            "meta_data": {
                "components": [
                    {
                        "name": "Readability",
                        "description": "Code readability",
                        "weight": 0.6,
                        "evaluation_method": "Review",
                        "scoring_rubric": {
                            "0-30": "Poor",
                            "31-60": "Fair",
                            "61-80": "Good",
                            "81-100": "Excellent"
                        }
                    },
                    {
                        "name": "Structure",
                        "description": "Code structure",
                        "weight": 0.4,
                        "evaluation_method": "Analysis",
                        "scoring_rubric": {
                            "0-30": "Poor",
                            "31-60": "Fair",
                            "61-80": "Good",
                            "81-100": "Excellent"
                        }
                    }
                ]
            }
        },
        {
            "name": "Performance",
            "description": "Evaluate performance",
            "weight": 0.5,
            "criteria_type": "performance",
            "meta_data": {
                "components": [
                    {
                        "name": "Speed",
                        "description": "Execution speed",
                        "weight": 1.0,
                        "evaluation_method": "Benchmark",
                        "scoring_rubric": {
                            "0-30": "Slow",
                            "31-60": "Moderate",
                            "61-80": "Fast",
                            "81-100": "Very Fast"
                        }
                    }
                ]
            }
        }
    ]
    
    criteria_ids = []
    for criteria in criteria_data:
        resp = await client.post(f"/api/v1/agent-evaluations/tasks/{task_id}/criteria",
                                 headers=HEADERS_USER1, json=criteria)
        resp.raise_for_status()
        criteria_ids.append(resp.json()["id"])
    
    return task_id, criteria_ids

async def _create_submission(db_session, task_id):
    """Create a submission for evaluation."""
    from app.models.agent_evaluation import AgentSubmission
    
    submission = AgentSubmission(
        id=str(uuid.uuid4()),
        task_id=task_id,
        agent_name="test-agent",
        agent_version="1.0",
        status="ready"
    )
    db_session.add(submission)
    await db_session.commit()
    return submission.id

# ---------- tests ----------
async def test_human_evaluation_submission(async_client, db_session):
    """Test submitting human evaluations for all criteria."""
    task_id, criteria_ids = await _create_task_with_criteria(async_client)
    submission_id = await _create_submission(db_session, task_id)
    
    # Submit human evaluations for each criterion
    evaluation_data = [
        {
            "submission_id": submission_id,
            "criterion_id": criteria_ids[0],
            "score": 85.0,
            "reasoning": "Code is well-structured and readable",
            "evidence": ["Clear variable names", "Good function organization"]
        },
        {
            "submission_id": submission_id,
            "criterion_id": criteria_ids[1],
            "score": 92.0,
            "reasoning": "Excellent performance metrics",
            "evidence": ["Fast execution time", "Low memory usage"]
        }
    ]
    
    evaluation_ids = []
    for eval_data in evaluation_data:
        resp = await async_client.post("/api/v1/agent-evaluations/human-evaluations",
                                      headers=HEADERS_USER1, json=eval_data)
        assert resp.status_code == 200
        result = resp.json()
        evaluation_ids.append(result["evaluation_id"])
        assert result["created_by_user_id"] == HEADERS_USER1["X-User-ID"]
    
    # Get human scores for the submission
    resp = await async_client.get(f"/api/v1/agent-evaluations/submissions/{submission_id}/human-scores",
                                  headers=HEADERS_USER1)
    assert resp.status_code == 200
    scores = resp.json()
    assert len(scores) == 2
    
    # Verify scores
    for score in scores:
        assert score["submission_id"] == submission_id
        assert score["criterion_id"] in criteria_ids
        assert score["evaluator"] == HEADERS_USER1["X-User-ID"]

async def test_single_human_score_per_criteria_rule(async_client, db_session):
    """Test that only one human score is allowed per criteria."""
    task_id, criteria_ids = await _create_task_with_criteria(async_client)
    submission_id = await _create_submission(async_client, task_id)
    
    # First user submits evaluation
    eval_data = {
        "submission_id": submission_id,
        "criterion_id": criteria_ids[0],
        "score": 80.0,
        "reasoning": "Good implementation",
        "evidence": ["Evidence 1"]
    }
    resp = await async_client.post("/api/v1/agent-evaluations/human-evaluations",
                                   headers=HEADERS_USER1, json=eval_data)
    assert resp.status_code == 200
    first_eval_id = resp.json()["evaluation_id"]
    
    # Same user tries to submit again - should update/replace
    eval_data["score"] = 85.0
    eval_data["reasoning"] = "Updated evaluation"
    resp = await async_client.post("/api/v1/agent-evaluations/human-evaluations",
                                   headers=HEADERS_USER1, json=eval_data)
    # Backend might create new or update - frontend handles the single score logic
    
    # Different user tries to submit - frontend should prevent this
    eval_data["score"] = 90.0
    resp = await async_client.post("/api/v1/agent-evaluations/human-evaluations",
                                   headers=HEADERS_USER2, json=eval_data)
    # Backend allows multiple evaluators - frontend enforces single score rule

async def test_human_evaluation_update(async_client, db_session):
    """Test updating existing human evaluations."""
    task_id, criteria_ids = await _create_task_with_criteria(async_client)
    submission_id = await _create_submission(async_client, task_id)
    
    # Submit initial evaluation
    eval_data = {
        "submission_id": submission_id,
        "criterion_id": criteria_ids[0],
        "score": 75.0,
        "reasoning": "Initial evaluation",
        "evidence": ["Initial evidence"]
    }
    resp = await async_client.post("/api/v1/agent-evaluations/human-evaluations",
                                   headers=HEADERS_USER1, json=eval_data)
    assert resp.status_code == 200
    eval_result = resp.json()
    result_id = eval_result["result_id"]
    
    # Update the evaluation
    update_data = {
        "score": 82.0,
        "reasoning": "Updated after further review",
        "evidence": ["New evidence 1", "New evidence 2"]
    }
    resp = await async_client.put(f"/api/v1/agent-evaluations/human-evaluations/{result_id}",
                                  headers=HEADERS_USER1, json=update_data)
    assert resp.status_code == 200
    updated = resp.json()
    assert updated["score"] == 82.0
    assert updated["reasoning"] == "Updated after further review"
    assert len(updated["evidence"]) == 2

async def test_human_evaluation_deletion(async_client, db_session):
    """Test deleting human evaluations."""
    task_id, criteria_ids = await _create_task_with_criteria(async_client)
    submission_id = await _create_submission(async_client, task_id)
    
    # Submit evaluation
    eval_data = {
        "submission_id": submission_id,
        "criterion_id": criteria_ids[0],
        "score": 88.0,
        "reasoning": "To be deleted",
        "evidence": []
    }
    resp = await async_client.post("/api/v1/agent-evaluations/human-evaluations",
                                   headers=HEADERS_USER1, json=eval_data)
    assert resp.status_code == 200
    result_id = resp.json()["result_id"]
    
    # Delete the evaluation
    resp = await async_client.delete(f"/api/v1/agent-evaluations/human-evaluations/{result_id}",
                                     headers=HEADERS_USER1)
    assert resp.status_code == 204
    
    # Verify deletion
    resp = await async_client.get(f"/api/v1/agent-evaluations/submissions/{submission_id}/human-scores",
                                  headers=HEADERS_USER1)
    assert resp.status_code == 200
    scores = resp.json()
    assert not any(s["result_id"] == result_id for s in scores)

async def test_human_evaluation_summary(async_client, db_session):
    """Test human evaluation summary endpoint."""
    task_id, criteria_ids = await _create_task_with_criteria(async_client)
    
    # Create multiple submissions
    submission_ids = []
    for i in range(3):
        submission_id = await _create_submission(async_client, task_id)
        submission_ids.append(submission_id)
        
        # Add human evaluations
        for j, criterion_id in enumerate(criteria_ids):
            eval_data = {
                "submission_id": submission_id,
                "criterion_id": criterion_id,
                "score": 70.0 + (i * 10) + (j * 5),  # Varying scores
                "reasoning": f"Evaluation for submission {i+1}",
                "evidence": []
            }
            resp = await async_client.post("/api/v1/agent-evaluations/human-evaluations",
                                          headers=HEADERS_USER1, json=eval_data)
            assert resp.status_code == 200
    
    # Get summary
    resp = await async_client.get(f"/api/v1/agent-evaluations/tasks/{task_id}/human-evaluations/summary",
                                  headers=HEADERS_USER1)
    assert resp.status_code == 200
    summary = resp.json()
    
    assert summary["total_submissions"] == 3
    assert summary["evaluated_submissions"] == 3
    assert "average_scores" in summary
    assert "score_distribution" in summary

async def test_human_evaluation_component_scores(async_client, db_session):
    """Test that human evaluations properly handle component-level information."""
    task_id, criteria_ids = await _create_task_with_criteria(async_client)
    submission_id = await _create_submission(async_client, task_id)
    
    # Get criteria details to see components
    resp = await async_client.get(f"/api/v1/agent-evaluations/tasks/{task_id}/criteria")
    assert resp.status_code == 200
    criteria_list = resp.json()
    
    # Submit evaluation with component awareness
    first_criteria = next(c for c in criteria_list if c["id"] == criteria_ids[0])
    components = first_criteria["meta_data"]["components"]
    
    eval_data = {
        "submission_id": submission_id,
        "criterion_id": criteria_ids[0],
        "score": 87.0,
        "reasoning": f"Evaluated based on {len(components)} components",
        "evidence": [f"Component {comp['name']} score considered" for comp in components]
    }
    
    resp = await async_client.post("/api/v1/agent-evaluations/human-evaluations",
                                   headers=HEADERS_USER1, json=eval_data)
    assert resp.status_code == 200
    
    # Verify the evaluation includes component context
    resp = await async_client.get(f"/api/v1/agent-evaluations/submissions/{submission_id}/human-scores",
                                  headers=HEADERS_USER1)
    assert resp.status_code == 200
    scores = resp.json()
    
    score_for_criteria = next(s for s in scores if s["criterion_id"] == criteria_ids[0])
    assert len(score_for_criteria["evidence"]) == len(components)

async def test_human_evaluation_access_control(async_client, db_session):
    """Test access control for human evaluations."""
    task_id, criteria_ids = await _create_task_with_criteria(async_client)
    submission_id = await _create_submission(async_client, task_id)
    
    # User 1 submits evaluation
    eval_data = {
        "submission_id": submission_id,
        "criterion_id": criteria_ids[0],
        "score": 90.0,
        "reasoning": "User 1 evaluation",
        "evidence": []
    }
    resp = await async_client.post("/api/v1/agent-evaluations/human-evaluations",
                                   headers=HEADERS_USER1, json=eval_data)
    assert resp.status_code == 200
    result_id = resp.json()["result_id"]
    
    # User 2 tries to update User 1's evaluation - should fail
    update_data = {"score": 50.0}
    resp = await async_client.put(f"/api/v1/agent-evaluations/human-evaluations/{result_id}",
                                  headers=HEADERS_USER2, json=update_data)
    # Should get permission error or not found
    assert resp.status_code in [403, 404]
    
    # User 2 tries to delete User 1's evaluation - should fail
    resp = await async_client.delete(f"/api/v1/agent-evaluations/human-evaluations/{result_id}",
                                     headers=HEADERS_USER2)
    assert resp.status_code in [403, 404]
    
    # User 1 can still access their evaluation
    resp = await async_client.get(f"/api/v1/agent-evaluations/submissions/{submission_id}/human-scores",
                                  headers=HEADERS_USER1)
    assert resp.status_code == 200
    scores = resp.json()
    assert any(s["result_id"] == result_id for s in scores)