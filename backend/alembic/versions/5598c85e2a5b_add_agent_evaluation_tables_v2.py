"""Add agent evaluation tables v2

Revision ID: 5598c85e2a5b
Revises: 89f418274c83
Create Date: 2025-06-13 13:33:45.538087

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '5598c85e2a5b'
down_revision: Union[str, None] = 'd70dbb5a6738'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Create agent_evaluation_tasks table
    op.create_table('agent_evaluation_tasks',
        sa.Column('id', sa.String(), nullable=False),
        sa.Column('user_id', sa.String(), nullable=False),
        sa.Column('evaluation_type', sa.String(), nullable=False),
        sa.Column('name', sa.String(), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('prompt', sa.Text(), nullable=True),
        sa.Column('base_repo_url', sa.String(), nullable=True),
        sa.Column('base_commit', sa.String(), nullable=True),
        sa.Column('configuration', sa.JSON(), nullable=True),
        sa.Column('status', sa.String(), nullable=True, server_default='created'),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_agent_tasks_user_type', 'agent_evaluation_tasks', ['user_id', 'evaluation_type'], unique=False)
    op.create_index('idx_agent_tasks_status', 'agent_evaluation_tasks', ['status'], unique=False)
    op.create_index(op.f('ix_agent_evaluation_tasks_id'), 'agent_evaluation_tasks', ['id'], unique=False)
    op.create_index(op.f('ix_agent_evaluation_tasks_user_id'), 'agent_evaluation_tasks', ['user_id'], unique=False)

    # Create agent_submissions table
    op.create_table('agent_submissions',
        sa.Column('id', sa.String(), nullable=False),
        sa.Column('task_id', sa.String(), nullable=False),
        sa.Column('agent_name', sa.String(), nullable=False),
        sa.Column('agent_version', sa.String(), nullable=True),
        sa.Column('folder_path', sa.String(), nullable=True),
        sa.Column('worktree_path', sa.String(), nullable=True),
        sa.Column('status', sa.String(), nullable=True, server_default='pending'),
        sa.Column('total_tokens', sa.Integer(), nullable=True, server_default='0'),
        sa.Column('meta_data', sa.JSON(), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.ForeignKeyConstraint(['task_id'], ['agent_evaluation_tasks.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_agent_submissions_agent', 'agent_submissions', ['agent_name', 'agent_version'], unique=False)
    op.create_index('idx_agent_submissions_task', 'agent_submissions', ['task_id'], unique=False)
    op.create_index(op.f('ix_agent_submissions_id'), 'agent_submissions', ['id'], unique=False)

    # Create evaluation_context_blobs table first (as it's referenced by evaluation_contexts)
    op.create_table('evaluation_context_blobs',
        sa.Column('id', sa.String(), nullable=False),
        sa.Column('blob_id', sa.String(), nullable=True),
        sa.Column('storage_backend', sa.String(), nullable=True, server_default='filesystem'),
        sa.Column('storage_path', sa.String(), nullable=True),
        sa.Column('compressed_size', sa.Integer(), nullable=True),
        sa.Column('compression_type', sa.String(), nullable=True, server_default='gzip'),
        sa.Column('meta_data', sa.JSON(), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_evaluation_context_blobs_blob_id'), 'evaluation_context_blobs', ['blob_id'], unique=True)
    op.create_index(op.f('ix_evaluation_context_blobs_id'), 'evaluation_context_blobs', ['id'], unique=False)

    # Create selected_commits table
    op.create_table('selected_commits',
        sa.Column('id', sa.String(), nullable=False),
        sa.Column('submission_id', sa.String(), nullable=False),
        sa.Column('commit_hash', sa.String(), nullable=False),
        sa.Column('commit_message', sa.Text(), nullable=True),
        sa.Column('commit_author', sa.String(), nullable=True),
        sa.Column('commit_date', sa.DateTime(timezone=True), nullable=True),
        sa.Column('commit_order', sa.Integer(), nullable=True),
        sa.Column('meta_data', sa.JSON(), nullable=True),
        sa.ForeignKeyConstraint(['submission_id'], ['agent_submissions.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_selected_commits_hash', 'selected_commits', ['commit_hash'], unique=False)
    op.create_index('idx_selected_commits_submission', 'selected_commits', ['submission_id'], unique=False)
    op.create_index(op.f('ix_selected_commits_id'), 'selected_commits', ['id'], unique=False)

    # Create evaluation_contexts table
    op.create_table('evaluation_contexts',
        sa.Column('id', sa.String(), nullable=False),
        sa.Column('submission_id', sa.String(), nullable=False),
        sa.Column('context_type', sa.String(), nullable=False),
        sa.Column('file_path', sa.String(), nullable=True),
        sa.Column('commit_hash', sa.String(), nullable=True),
        sa.Column('content', sa.Text(), nullable=True),
        sa.Column('content_hash', sa.String(), nullable=True),
        sa.Column('content_size', sa.Integer(), nullable=True),
        sa.Column('blob_id', sa.String(), nullable=True),
        sa.Column('storage_type', sa.String(), nullable=True, server_default='inline'),
        sa.Column('estimated_tokens', sa.Integer(), nullable=True, server_default='0'),
        sa.Column('meta_data', sa.JSON(), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.ForeignKeyConstraint(['blob_id'], ['evaluation_context_blobs.id'], ),
        sa.ForeignKeyConstraint(['submission_id'], ['agent_submissions.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_evaluation_contexts_commit', 'evaluation_contexts', ['commit_hash'], unique=False)
    op.create_index('idx_evaluation_contexts_submission', 'evaluation_contexts', ['submission_id'], unique=False)
    op.create_index('idx_evaluation_contexts_type', 'evaluation_contexts', ['context_type'], unique=False)
    op.create_index(op.f('ix_evaluation_contexts_content_hash'), 'evaluation_contexts', ['content_hash'], unique=False)
    op.create_index(op.f('ix_evaluation_contexts_id'), 'evaluation_contexts', ['id'], unique=False)

    # Create evaluation_criteria table
    op.create_table('evaluation_criteria',
        sa.Column('id', sa.String(), nullable=False),
        sa.Column('task_id', sa.String(), nullable=False),
        sa.Column('name', sa.String(), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('weight', sa.Float(), nullable=True, server_default='1.0'),
        sa.Column('criteria_type', sa.String(), nullable=True, server_default='quality'),
        sa.Column('evaluation_prompt', sa.Text(), nullable=True),
        sa.Column('meta_data', sa.JSON(), nullable=True),
        sa.ForeignKeyConstraint(['task_id'], ['agent_evaluation_tasks.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_evaluation_criteria_name', 'evaluation_criteria', ['task_id', 'name'], unique=True)
    op.create_index('idx_evaluation_criteria_task', 'evaluation_criteria', ['task_id'], unique=False)
    op.create_index(op.f('ix_evaluation_criteria_id'), 'evaluation_criteria', ['id'], unique=False)

    # Create agent_evaluations table
    op.create_table('agent_evaluations',
        sa.Column('id', sa.String(), nullable=False),
        sa.Column('task_id', sa.String(), nullable=False),
        sa.Column('submission_id', sa.String(), nullable=False),
        sa.Column('model_name', sa.String(), nullable=False),
        sa.Column('model_parameters', sa.JSON(), nullable=True),
        sa.Column('status', sa.String(), nullable=True, server_default='pending'),
        sa.Column('overall_score', sa.Float(), nullable=True),
        sa.Column('usage_stats', sa.JSON(), nullable=True),
        sa.Column('completion_time', sa.Float(), nullable=True),
        sa.Column('error_message', sa.Text(), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.Column('completed_at', sa.DateTime(timezone=True), nullable=True),
        sa.ForeignKeyConstraint(['submission_id'], ['agent_submissions.id'], ),
        sa.ForeignKeyConstraint(['task_id'], ['agent_evaluation_tasks.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_agent_evaluations_model', 'agent_evaluations', ['model_name'], unique=False)
    op.create_index('idx_agent_evaluations_status', 'agent_evaluations', ['status'], unique=False)
    op.create_index('idx_agent_evaluations_submission', 'agent_evaluations', ['submission_id'], unique=False)
    op.create_index('idx_agent_evaluations_task', 'agent_evaluations', ['task_id'], unique=False)
    op.create_index(op.f('ix_agent_evaluations_id'), 'agent_evaluations', ['id'], unique=False)

    # Create evaluation_results table
    op.create_table('evaluation_results',
        sa.Column('id', sa.String(), nullable=False),
        sa.Column('evaluation_id', sa.String(), nullable=False),
        sa.Column('criteria_id', sa.String(), nullable=False),
        sa.Column('score', sa.Float(), nullable=False),
        sa.Column('reasoning', sa.Text(), nullable=True),
        sa.Column('evidence', sa.JSON(), nullable=True),
        sa.Column('meta_data', sa.JSON(), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.ForeignKeyConstraint(['criteria_id'], ['evaluation_criteria.id'], ),
        sa.ForeignKeyConstraint(['evaluation_id'], ['agent_evaluations.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_evaluation_results_criteria', 'evaluation_results', ['evaluation_id', 'criteria_id'], unique=True)
    op.create_index('idx_evaluation_results_evaluation', 'evaluation_results', ['evaluation_id'], unique=False)
    op.create_index(op.f('ix_evaluation_results_id'), 'evaluation_results', ['id'], unique=False)


def downgrade() -> None:
    # Drop tables in reverse order
    op.drop_index(op.f('ix_evaluation_results_id'), table_name='evaluation_results')
    op.drop_index('idx_evaluation_results_evaluation', table_name='evaluation_results')
    op.drop_index('idx_evaluation_results_criteria', table_name='evaluation_results')
    op.drop_table('evaluation_results')
    
    op.drop_index(op.f('ix_agent_evaluations_id'), table_name='agent_evaluations')
    op.drop_index('idx_agent_evaluations_task', table_name='agent_evaluations')
    op.drop_index('idx_agent_evaluations_submission', table_name='agent_evaluations')
    op.drop_index('idx_agent_evaluations_status', table_name='agent_evaluations')
    op.drop_index('idx_agent_evaluations_model', table_name='agent_evaluations')
    op.drop_table('agent_evaluations')
    
    op.drop_index(op.f('ix_evaluation_criteria_id'), table_name='evaluation_criteria')
    op.drop_index('idx_evaluation_criteria_task', table_name='evaluation_criteria')
    op.drop_index('idx_evaluation_criteria_name', table_name='evaluation_criteria')
    op.drop_table('evaluation_criteria')
    
    op.drop_index(op.f('ix_evaluation_contexts_id'), table_name='evaluation_contexts')
    op.drop_index(op.f('ix_evaluation_contexts_content_hash'), table_name='evaluation_contexts')
    op.drop_index('idx_evaluation_contexts_type', table_name='evaluation_contexts')
    op.drop_index('idx_evaluation_contexts_submission', table_name='evaluation_contexts')
    op.drop_index('idx_evaluation_contexts_commit', table_name='evaluation_contexts')
    op.drop_table('evaluation_contexts')
    
    op.drop_index(op.f('ix_selected_commits_id'), table_name='selected_commits')
    op.drop_index('idx_selected_commits_submission', table_name='selected_commits')
    op.drop_index('idx_selected_commits_hash', table_name='selected_commits')
    op.drop_table('selected_commits')
    
    op.drop_index(op.f('ix_evaluation_context_blobs_id'), table_name='evaluation_context_blobs')
    op.drop_index(op.f('ix_evaluation_context_blobs_blob_id'), table_name='evaluation_context_blobs')
    op.drop_table('evaluation_context_blobs')
    
    op.drop_index(op.f('ix_agent_submissions_id'), table_name='agent_submissions')
    op.drop_index('idx_agent_submissions_task', table_name='agent_submissions')
    op.drop_index('idx_agent_submissions_agent', table_name='agent_submissions')
    op.drop_table('agent_submissions')
    
    op.drop_index(op.f('ix_agent_evaluation_tasks_user_id'), table_name='agent_evaluation_tasks')
    op.drop_index(op.f('ix_agent_evaluation_tasks_id'), table_name='agent_evaluation_tasks')
    op.drop_index('idx_agent_tasks_status', table_name='agent_evaluation_tasks')
    op.drop_index('idx_agent_tasks_user_type', table_name='agent_evaluation_tasks')
    op.drop_table('agent_evaluation_tasks')