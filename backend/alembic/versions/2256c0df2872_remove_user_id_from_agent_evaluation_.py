"""Remove user_id from agent evaluation tables

Revision ID: 2256c0df2872
Revises: 5598c85e2a5b
Create Date: 2025-06-13 07:35:56.502928

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2256c0df2872'
down_revision: Union[str, None] = '5598c85e2a5b'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # Drop the user_id index first
    op.drop_index('ix_agent_evaluation_tasks_user_id', table_name='agent_evaluation_tasks')
    
    # Drop the user_id column
    op.drop_column('agent_evaluation_tasks', 'user_id')


def downgrade() -> None:
    """Downgrade schema."""
    # Re-add the user_id column
    op.add_column('agent_evaluation_tasks', sa.Column('user_id', sa.String(), nullable=False))
    
    # Re-create the index
    op.create_index('ix_agent_evaluation_tasks_user_id', 'agent_evaluation_tasks', ['user_id'], unique=False)