"""add_batch_support_to_agent_evaluations

Revision ID: c61e8f7315e6
Revises: 2256c0df2872
Create Date: 2025-06-17 04:48:32.959449

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'c61e8f7315e6'
down_revision: Union[str, None] = '2256c0df2872'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('evaluation_batches',
    sa.Column('id', sa.String(), nullable=False),
    sa.Column('task_id', sa.String(), nullable=False),
    sa.Column('batch_number', sa.Integer(), nullable=False),
    sa.Column('status', sa.String(), nullable=True),
    sa.Column('models_used', sa.JSON(), nullable=True),
    sa.Column('submission_count', sa.Integer(), nullable=True),
    sa.Column('successful_evaluations', sa.Integer(), nullable=True),
    sa.Column('failed_evaluations', sa.Integer(), nullable=True),
    sa.Column('summary_stats', sa.JSON(), nullable=True),
    sa.Column('started_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('completed_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['task_id'], ['agent_evaluation_tasks.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_evaluation_batches_number', 'evaluation_batches', ['task_id', 'batch_number'], unique=True)
    op.create_index('idx_evaluation_batches_status', 'evaluation_batches', ['status'], unique=False)
    op.create_index('idx_evaluation_batches_task', 'evaluation_batches', ['task_id'], unique=False)
    op.create_index(op.f('ix_evaluation_batches_id'), 'evaluation_batches', ['id'], unique=False)
    op.alter_column('agent_evaluation_tasks', 'status',
               existing_type=sa.VARCHAR(),
               nullable=False,
               existing_server_default=sa.text("'created'::character varying"))
    op.create_index('idx_agent_tasks_evaluation_type', 'agent_evaluation_tasks', ['evaluation_type'], unique=False)
    op.add_column('agent_evaluations', sa.Column('batch_id', sa.String(), nullable=True))
    op.create_index('idx_agent_evaluations_batch', 'agent_evaluations', ['batch_id'], unique=False)
    op.create_foreign_key(None, 'agent_evaluations', 'evaluation_batches', ['batch_id'], ['id'])
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'agent_evaluations', type_='foreignkey')
    op.drop_index('idx_agent_evaluations_batch', table_name='agent_evaluations')
    op.drop_column('agent_evaluations', 'batch_id')
    op.drop_index('idx_agent_tasks_evaluation_type', table_name='agent_evaluation_tasks')
    op.alter_column('agent_evaluation_tasks', 'status',
               existing_type=sa.VARCHAR(),
               nullable=True,
               existing_server_default=sa.text("'created'::character varying"))
    op.drop_index(op.f('ix_evaluation_batches_id'), table_name='evaluation_batches')
    op.drop_index('idx_evaluation_batches_task', table_name='evaluation_batches')
    op.drop_index('idx_evaluation_batches_status', table_name='evaluation_batches')
    op.drop_index('idx_evaluation_batches_number', table_name='evaluation_batches')
    op.drop_table('evaluation_batches')
    # ### end Alembic commands ###
