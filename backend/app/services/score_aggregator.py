"""Score aggregation utilities for multi-model evaluations."""
import logging
from typing import Dict, List, Optional, Any, Tuple
from statistics import mean, median, stdev
from collections import defaultdict
import numpy as np

logger = logging.getLogger(__name__)


class ScoreAggregator:
    """Aggregate scores from multiple evaluation models."""
    
    # Aggregation methods
    METHODS = {
        'mean': 'Simple average of all scores',
        'weighted_mean': 'Weighted average based on model confidence',
        'median': 'Median score (robust to outliers)',
        'trimmed_mean': 'Mean after removing highest/lowest scores',
        'consensus': 'Score based on model agreement',
        'optimistic': 'Highest score with confidence threshold',
        'pessimistic': 'Lowest score with confidence threshold'
    }
    
    @staticmethod
    def aggregate_scores(
        scores: List[Dict[str, Any]],
        method: str = 'weighted_mean',
        confidence_threshold: float = 0.7
    ) -> Dict[str, Any]:
        """
        Aggregate scores using specified method.
        
        Args:
            scores: List of score dictionaries with 'score', 'confidence', 'model_name'
            method: Aggregation method
            confidence_threshold: Minimum confidence for certain methods
            
        Returns:
            Aggregated score with metadata
        """
        if not scores:
            return {
                'final_score': 0.0,
                'method': method,
                'confidence': 0.0,
                'models_used': 0
            }
        
        # Filter by confidence if needed
        valid_scores = [
            s for s in scores 
            if s.get('confidence', 1.0) >= confidence_threshold
        ]
        
        if not valid_scores:
            valid_scores = scores  # Use all if none meet threshold
        
        # Extract score values
        score_values = [s['score'] for s in valid_scores]
        confidences = [s.get('confidence', 1.0) for s in valid_scores]
        
        # Apply aggregation method
        if method == 'mean':
            final_score = mean(score_values)
            final_confidence = mean(confidences)
            
        elif method == 'weighted_mean':
            # Weight by confidence
            weighted_sum = sum(s * c for s, c in zip(score_values, confidences))
            total_weight = sum(confidences)
            final_score = weighted_sum / total_weight if total_weight > 0 else 0
            final_confidence = mean(confidences)
            
        elif method == 'median':
            final_score = median(score_values)
            final_confidence = median(confidences)
            
        elif method == 'trimmed_mean':
            # Remove highest and lowest if enough scores
            if len(score_values) >= 3:
                trimmed = sorted(score_values)[1:-1]
                final_score = mean(trimmed)
            else:
                final_score = mean(score_values)
            final_confidence = mean(confidences)
            
        elif method == 'consensus':
            # Score based on agreement (low variance = high consensus)
            final_score = mean(score_values)
            if len(score_values) > 1:
                score_std = stdev(score_values)
                # Convert variance to consensus score (0-1)
                consensus = 1.0 - min(score_std / 50, 1.0)  # Normalize by max expected std
                final_confidence = consensus * mean(confidences)
            else:
                final_confidence = mean(confidences)
                
        elif method == 'optimistic':
            # Highest score from confident models
            final_score = max(score_values)
            # Find confidence of the max score
            max_idx = score_values.index(final_score)
            final_confidence = confidences[max_idx]
            
        elif method == 'pessimistic':
            # Lowest score from confident models
            final_score = min(score_values)
            # Find confidence of the min score
            min_idx = score_values.index(final_score)
            final_confidence = confidences[min_idx]
            
        else:
            # Default to mean
            final_score = mean(score_values)
            final_confidence = mean(confidences)
        
        # Calculate additional statistics
        score_variance = 0.0
        if len(score_values) > 1:
            score_variance = max(score_values) - min(score_values)
        
        return {
            'final_score': round(final_score, 2),
            'method': method,
            'confidence': round(final_confidence, 3),
            'models_used': len(valid_scores),
            'score_variance': round(score_variance, 2),
            'individual_scores': [
                {
                    'model': s.get('model_name', 'unknown'),
                    'score': round(s['score'], 2),
                    'confidence': round(s.get('confidence', 1.0), 3)
                }
                for s in valid_scores
            ]
        }
    
    @staticmethod
    def aggregate_component_scores(
        component_scores_by_model: Dict[str, List[Dict[str, Any]]],
        component_definitions: List[Dict[str, Any]],
        method: str = 'weighted_mean'
    ) -> Dict[str, Any]:
        """
        Aggregate component scores across models.
        
        Args:
            component_scores_by_model: Dict mapping model names to component scores
            component_definitions: Component definitions with weights
            method: Aggregation method
            
        Returns:
            Aggregated component scores
        """
        # Group scores by component name
        scores_by_component = defaultdict(list)
        
        for model_name, components in component_scores_by_model.items():
            for comp in components:
                scores_by_component[comp['component_name']].append({
                    'score': comp['score'],
                    'model_name': model_name,
                    'confidence': 1.0  # Component level doesn't have confidence
                })
        
        # Aggregate each component
        aggregated_components = {}
        
        for comp_name, scores in scores_by_component.items():
            aggregated = ScoreAggregator.aggregate_scores(scores, method)
            aggregated_components[comp_name] = aggregated
        
        # Calculate weighted overall score
        total_score = 0.0
        total_weight = 0.0
        
        # Get weights from definitions
        weight_map = {
            comp['name']: comp.get('weight', 1.0)
            for comp in component_definitions
        }
        
        for comp_name, agg_data in aggregated_components.items():
            weight = weight_map.get(comp_name, 1.0)
            total_score += agg_data['final_score'] * weight
            total_weight += weight
        
        overall_score = total_score / total_weight if total_weight > 0 else 0
        
        return {
            'overall_score': round(overall_score, 2),
            'components': aggregated_components,
            'aggregation_method': method
        }
    
    @staticmethod
    def calculate_ranking_scores(
        submissions: List[Dict[str, Any]],
        ranking_method: str = 'score_based'
    ) -> List[Dict[str, Any]]:
        """
        Calculate ranking scores for submissions.
        
        Args:
            submissions: List of submissions with scores
            ranking_method: Method for ranking
            
        Returns:
            Ranked submissions
        """
        if ranking_method == 'score_based':
            # Simple score-based ranking
            ranked = sorted(
                submissions,
                key=lambda x: x.get('final_score', 0),
                reverse=True
            )
            
        elif ranking_method == 'confidence_weighted':
            # Consider both score and confidence
            ranked = sorted(
                submissions,
                key=lambda x: (
                    x.get('final_score', 0) * x.get('confidence', 1.0)
                ),
                reverse=True
            )
            
        elif ranking_method == 'consensus_based':
            # Prefer high scores with low variance
            ranked = sorted(
                submissions,
                key=lambda x: (
                    x.get('final_score', 0) - 
                    0.1 * x.get('score_variance', 0)  # Penalty for high variance
                ),
                reverse=True
            )
        else:
            ranked = submissions
        
        # Add rank positions
        for i, submission in enumerate(ranked, 1):
            submission['rank'] = i
            
            # Calculate rank confidence
            if i == 1 and len(ranked) > 1:
                # Gap between 1st and 2nd
                score_gap = (
                    submission.get('final_score', 0) - 
                    ranked[1].get('final_score', 0)
                )
                submission['rank_confidence'] = min(score_gap / 10, 1.0)
            else:
                submission['rank_confidence'] = 0.5  # Default
        
        return ranked
    
    @staticmethod
    def generate_comparative_analysis(
        rankings: List[Dict[str, Any]],
        criteria_results: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Generate comparative analysis of submissions.
        
        Args:
            rankings: Ranked submissions
            criteria_results: Detailed criteria evaluation results
            
        Returns:
            Comparative analysis
        """
        if not rankings:
            return {'error': 'No submissions to compare'}
        
        analysis = {
            'summary': {
                'total_submissions': len(rankings),
                'score_range': {
                    'min': min(r.get('final_score', 0) for r in rankings),
                    'max': max(r.get('final_score', 0) for r in rankings),
                    'mean': mean(r.get('final_score', 0) for r in rankings)
                }
            },
            'winner': {
                'agent_name': rankings[0].get('agent_name'),
                'score': rankings[0].get('final_score'),
                'margin': None
            },
            'detailed_comparison': []
        }
        
        # Calculate winner's margin
        if len(rankings) > 1:
            analysis['winner']['margin'] = (
                rankings[0].get('final_score', 0) - 
                rankings[1].get('final_score', 0)
            )
        
        # Detailed comparison
        for rank_data in rankings[:5]:  # Top 5 only
            submission_id = rank_data.get('submission_id')
            
            comparison = {
                'rank': rank_data.get('rank'),
                'agent_name': rank_data.get('agent_name'),
                'final_score': rank_data.get('final_score'),
                'strengths': [],
                'weaknesses': []
            }
            
            # Find strengths and weaknesses from criteria
            if submission_id in criteria_results:
                sub_results = criteria_results[submission_id]
                
                # Get all criterion scores
                criterion_scores = []
                for criterion_id, criterion_data in sub_results.items():
                    criterion_scores.append({
                        'name': criterion_data.get('name', 'Unknown'),
                        'score': criterion_data.get('mean_score', 0)
                    })
                
                # Sort by score
                criterion_scores.sort(key=lambda x: x['score'], reverse=True)
                
                # Top 2 are strengths
                comparison['strengths'] = [
                    f"{c['name']} ({c['score']:.1f})"
                    for c in criterion_scores[:2]
                ]
                
                # Bottom 2 are weaknesses
                comparison['weaknesses'] = [
                    f"{c['name']} ({c['score']:.1f})"
                    for c in criterion_scores[-2:]
                ]
            
            analysis['detailed_comparison'].append(comparison)
        
        return analysis