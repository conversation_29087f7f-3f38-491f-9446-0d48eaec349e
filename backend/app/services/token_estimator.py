"""Token estimation service for calculating approximate token counts."""
import os
from typing import Di<PERSON>, <PERSON><PERSON>, List
import tiktoken


class TokenEstimator:
    """Estimates token counts for various file types."""
    
    def __init__(self):
        """Initialize the token estimator with cl100k_base encoding."""
        # Use cl100k_base as default (GPT-4 tokenizer)
        self.encoding = tiktoken.get_encoding("cl100k_base")
        
        # Token multipliers by file type (based on empirical data)
        self.multipliers = {
            '.py': 0.28,     # Python code
            '.js': 0.30,     # JavaScript
            '.ts': 0.32,     # TypeScript (more verbose)
            '.jsx': 0.31,    # React JSX
            '.tsx': 0.33,    # React TSX
            '.java': 0.35,   # Java (verbose)
            '.cpp': 0.32,    # C++
            '.h': 0.30,      # C/C++ headers
            '.c': 0.30,      # C
            '.go': 0.29,     # Go
            '.rs': 0.31,     # Rust
            '.swift': 0.32,  # Swift
            '.kt': 0.34,     # Kotlin
            '.rb': 0.28,     # Ruby
            '.php': 0.32,    # PHP
            '.r': 0.30,      # R
            '.m': 0.32,      # Objective-C
            '.scala': 0.33,  # Scala
            '.md': 0.25,     # Markdown
            '.txt': 0.25,    # Plain text
            '.rst': 0.26,    # reStructuredText
            '.json': 0.35,   # JSON
            '.yaml': 0.33,   # YAML
            '.yml': 0.33,    # YAML
            '.toml': 0.32,   # TOML
            '.xml': 0.40,    # XML (very verbose)
            '.html': 0.38,   # HTML
            '.htm': 0.38,    # HTML
            '.css': 0.30,    # CSS
            '.scss': 0.31,   # SCSS
            '.less': 0.31,   # Less
            '.sql': 0.28,    # SQL
            '.sh': 0.27,     # Shell scripts
            '.bash': 0.27,   # Bash scripts
            '.ps1': 0.30,    # PowerShell
            '.dockerfile': 0.26,  # Dockerfile
            '.makefile': 0.28,    # Makefile
            '.cmake': 0.30,       # CMake
            '.gitignore': 0.24,   # Gitignore
            '.env': 0.25,         # Environment files
            '.ini': 0.26,         # INI files
            '.cfg': 0.26,         # Config files
            '.conf': 0.26,        # Config files
            '.properties': 0.28,  # Properties files
            '.gradle': 0.32,      # Gradle
            '.pom': 0.38,         # Maven POM (XML)
        }
        
    def estimate_tokens(self, content: str, file_path: str) -> Tuple[int, Dict]:
        """
        Estimate tokens with detailed breakdown.
        
        Args:
            content: File content as string
            file_path: Path to the file for extension detection
            
        Returns:
            Tuple of (token_count, details_dict)
        """
        # Get file extension
        ext = os.path.splitext(file_path)[1].lower()
        
        # For small files: exact count
        if len(content) < 10000:  # Less than 10KB
            try:
                exact_tokens = len(self.encoding.encode(content))
                return exact_tokens, {
                    'method': 'exact',
                    'characters': len(content),
                    'tokens': exact_tokens,
                    'file_extension': ext
                }
            except Exception:
                # Fallback to estimation if encoding fails
                pass
        
        # For large files: use sampling + multiplier
        sample_size = min(10000, len(content))
        sample = content[:sample_size]
        
        try:
            sample_tokens = len(self.encoding.encode(sample))
            tokens_per_char = sample_tokens / len(sample)
        except Exception:
            # Fallback to multiplier-only estimation
            tokens_per_char = self.multipliers.get(ext, 0.33)
        
        # Get base multiplier
        multiplier = self.multipliers.get(ext, 0.33)
        
        # Adjust for content characteristics
        if self._is_minified(content):
            multiplier *= 1.15
        if self._has_long_lines(content):
            multiplier *= 1.08
        if self._is_binary_like(content):
            multiplier *= 1.25
            
        # Calculate final estimate
        # Use the more accurate of: sample-based or multiplier-based
        if 'tokens_per_char' in locals():
            estimated_tokens = int(len(content) * tokens_per_char)
        else:
            estimated_tokens = int(len(content) * multiplier)
        
        return estimated_tokens, {
            'method': 'estimated',
            'characters': len(content),
            'tokens': estimated_tokens,
            'multiplier': multiplier,
            'file_extension': ext,
            'sample_ratio': tokens_per_char if 'tokens_per_char' in locals() else None
        }
    
    def estimate_context_size(self, contexts: List[Dict]) -> Dict:
        """
        Estimate total context size with breakdown.
        
        Args:
            contexts: List of dicts with 'content' and 'path' keys
            
        Returns:
            Dict with total tokens and breakdown
        """
        total_tokens = 0
        file_breakdown = []
        
        for ctx in contexts:
            tokens, details = self.estimate_tokens(ctx['content'], ctx['path'])
            total_tokens += tokens
            file_breakdown.append({
                'path': ctx['path'],
                'tokens': tokens,
                'details': details
            })
            
        # Add overhead for formatting (file markers, etc.)
        # Approximately 50 tokens per file for markers like "=== File: path ==="
        overhead = len(contexts) * 50
        total_tokens += overhead
        
        return {
            'total_tokens': total_tokens,
            'file_count': len(contexts),
            'overhead_tokens': overhead,
            'files': file_breakdown,
        }
    
    def _is_minified(self, content: str) -> bool:
        """Check if content appears to be minified."""
        if not content:
            return False
            
        # Sample first 1000 characters
        sample = content[:1000]
        lines = sample.split('\n')
        
        # Minified files typically have very long lines
        if lines and max(len(line) for line in lines) > 500:
            return True
            
        # Check for lack of whitespace (common in minified code)
        whitespace_ratio = sum(1 for c in sample if c.isspace()) / len(sample)
        return whitespace_ratio < 0.05
    
    def _has_long_lines(self, content: str) -> bool:
        """Check if content has unusually long lines."""
        if not content:
            return False
            
        # Sample first 5000 characters
        sample = content[:5000]
        lines = sample.split('\n')
        
        if not lines:
            return False
            
        avg_line_length = sum(len(line) for line in lines) / len(lines)
        return avg_line_length > 120
    
    def _is_binary_like(self, content: str) -> bool:
        """Check if content appears to have binary-like characteristics."""
        if not content:
            return False
            
        # Sample first 1000 characters
        sample = content[:1000]
        
        # Check for non-printable characters
        non_printable = sum(1 for c in sample if ord(c) < 32 and c not in '\n\r\t')
        
        # If more than 5% non-printable, likely binary-like
        return (non_printable / len(sample)) > 0.05