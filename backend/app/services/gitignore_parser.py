"""Gitignore parser for file exclusion during folder scanning."""
import fnmatch
import os
from pathlib import Path
from typing import List, Set
import logging

logger = logging.getLogger(__name__)


class GitignoreParser:
    """Parse and apply .gitignore patterns for file exclusion."""
    
    # Default patterns to always exclude
    DEFAULT_EXCLUDES = [
        # Python
        '__pycache__/', '*.pyc', '*.pyo', '*.pyd', '.Python',
        'pip-log.txt', 'pip-delete-this-directory.txt',
        '.tox/', '.coverage', '.coverage.*', '.cache',
        'nosetests.xml', 'coverage.xml', '*.cover', '.hypothesis/',
        '.pytest_cache/', '*.egg-info/', '.eggs/',
        
        # Virtual environments
        'venv/', 'env/', 'ENV/', '.venv/', 'virtualenv/',
        
        # Node.js
        'node_modules/', 'npm-debug.log*', 'yarn-debug.log*', 
        'yarn-error.log*', '.npm/', '.yarn-integrity',
        
        # Build outputs
        'dist/', 'build/', 'out/', 'target/', '*.egg',
        
        # IDE and editors
        '.idea/', '.vscode/', '*.sublime-*', '*.swp', '*.swo', '*~',
        '.project', '.classpath', '.settings/',
        
        # OS files
        '.DS_Store', 'Thumbs.db', 'desktop.ini', '.Spotlight-V100',
        '.Trashes', 'ehthumbs.db',
        
        # Version control
        '.git/', '.svn/', '.hg/', '.bzr/',
        
        # Environment and secrets
        '.env', '.env.*', '*.log', '*.sqlite', '*.db',
        '.secret*', '*.key', '*.pem',
        
        # Temporary files
        '*.tmp', '*.temp', '*.bak', '*.backup', '*.old',
        '.~lock.*',
    ]
    
    def __init__(self, root_path: str):
        """
        Initialize gitignore parser.
        
        Args:
            root_path: Root directory path to search for .gitignore
        """
        self.root_path = Path(root_path).resolve()
        self.patterns: List[str] = []
        self.negated_patterns: List[str] = []
        self._load_patterns()
    
    def _load_patterns(self):
        """Load patterns from .gitignore file and defaults."""
        # Start with default excludes
        self.patterns.extend(self.DEFAULT_EXCLUDES)
        
        # Load .gitignore if it exists
        gitignore_path = self.root_path / '.gitignore'
        if gitignore_path.exists():
            try:
                with open(gitignore_path, 'r', encoding='utf-8') as f:
                    for line in f:
                        line = line.strip()
                        
                        # Skip empty lines and comments
                        if not line or line.startswith('#'):
                            continue
                        
                        # Handle negated patterns (starting with !)
                        if line.startswith('!'):
                            self.negated_patterns.append(line[1:])
                        else:
                            self.patterns.append(line)
                            
                logger.info(f"Loaded {len(self.patterns)} patterns from .gitignore")
            except Exception as e:
                logger.warning(f"Failed to load .gitignore: {e}")
    
    def should_ignore(self, file_path: Path) -> bool:
        """
        Check if a file should be ignored based on gitignore patterns.
        
        Args:
            file_path: Path object to check
            
        Returns:
            True if file should be ignored
        """
        # Convert to Path object if needed
        if not isinstance(file_path, Path):
            file_path = Path(file_path)
        
        # Get relative path from root
        try:
            rel_path = file_path.relative_to(self.root_path)
        except ValueError:
            # File is outside root, don't ignore
            return False
        
        rel_path_str = str(rel_path).replace(os.sep, '/')
        
        # Check against patterns
        ignored = self._matches_patterns(rel_path_str, file_path)
        
        # Check negated patterns (un-ignore)
        if ignored and self.negated_patterns:
            for pattern in self.negated_patterns:
                if self._match_pattern(rel_path_str, pattern, file_path):
                    return False
        
        return ignored
    
    def _matches_patterns(self, rel_path_str: str, file_path: Path) -> bool:
        """Check if path matches any ignore pattern."""
        for pattern in self.patterns:
            if self._match_pattern(rel_path_str, pattern, file_path):
                return True
        return False
    
    def _match_pattern(self, rel_path_str: str, pattern: str, file_path: Path) -> bool:
        """
        Match a single pattern against a path.
        
        Handles various gitignore pattern types:
        - Directory patterns (ending with /)
        - Wildcard patterns (including **)
        - Absolute patterns (starting with /)
        - Relative patterns
        """
        # Handle ** patterns (match any number of directories)
        if '**' in pattern:
            import re
            # Convert pattern to regex
            regex_pattern = pattern
            
            # Save the * positions that should become [^/]*
            import string
            placeholder = ''.join(c for c in string.ascii_uppercase if c not in pattern)
            
            # First, replace single * with placeholder (not **)
            temp_pattern = regex_pattern.replace('**', f'{placeholder}{placeholder}')
            temp_pattern = temp_pattern.replace('*', placeholder)
            temp_pattern = temp_pattern.replace(f'{placeholder}{placeholder}', '**')
            regex_pattern = temp_pattern
            
            # Now handle ** patterns
            if regex_pattern.startswith('**/'):
                regex_pattern = '(?:.*/)?' + regex_pattern[3:]
            
            regex_pattern = regex_pattern.replace('/**', '/.*')
            regex_pattern = regex_pattern.replace('/**/', '/.*/') 
            regex_pattern = regex_pattern.replace('**', '.*')
            
            # Escape special regex characters
            regex_pattern = regex_pattern.replace('.', r'\.')
            regex_pattern = regex_pattern.replace('+', r'\+')
            regex_pattern = regex_pattern.replace('^', r'\^')
            regex_pattern = regex_pattern.replace('$', r'\$')
            regex_pattern = regex_pattern.replace('(', r'\(')
            regex_pattern = regex_pattern.replace(')', r'\)')
            
            # Now convert placeholders to [^/]*
            regex_pattern = regex_pattern.replace(placeholder, '[^/]*')
            regex_pattern = regex_pattern.replace('?', '[^/]')
            
            # Match the pattern
            if re.match(f'^{regex_pattern}$', rel_path_str):
                return True
            return False
        
        # Directory pattern (ending with /)
        if pattern.endswith('/'):
            dir_pattern = pattern[:-1]
            
            # Special handling for path patterns like src/temp/
            if '/' in dir_pattern:
                # Check if path starts with or contains this directory path
                if rel_path_str.startswith(pattern) or f'/{pattern}' in f'/{rel_path_str}':
                    return True
            else:
                # Simple directory name - check if any parent directory matches
                parts = rel_path_str.split('/')
                for i in range(len(parts)):
                    if fnmatch.fnmatch(parts[i], dir_pattern):
                        return True
                # Also check if the file itself is a directory matching the pattern
                if file_path.is_dir() and fnmatch.fnmatch(file_path.name, dir_pattern):
                    return True
        
        # Absolute pattern (from root)
        elif pattern.startswith('/'):
            abs_pattern = pattern[1:]
            return fnmatch.fnmatch(rel_path_str, abs_pattern)
        
        # Pattern with path separator - must match from root
        elif '/' in pattern:
            # For patterns like docs/**/*.tmp, handle specially
            if '*' in pattern:
                # Use fnmatch on the full path
                return fnmatch.fnmatch(rel_path_str, pattern)
            else:
                # Exact path match
                return rel_path_str == pattern or rel_path_str.startswith(pattern + '/')
        
        # Simple pattern - can match anywhere
        else:
            # Check against full path
            if fnmatch.fnmatch(rel_path_str, pattern):
                return True
            # Check against just the filename
            if fnmatch.fnmatch(file_path.name, pattern):
                return True
            # Check if any parent directory matches (for directory patterns without /)
            parts = rel_path_str.split('/')
            for part in parts[:-1]:  # Exclude filename
                if fnmatch.fnmatch(part, pattern):
                    return True
        
        return False
    
    def filter_files(self, file_paths: List[Path]) -> List[Path]:
        """
        Filter a list of file paths based on gitignore patterns.
        
        Args:
            file_paths: List of Path objects to filter
            
        Returns:
            List of paths that should not be ignored
        """
        return [p for p in file_paths if not self.should_ignore(p)]
    
    def get_patterns_summary(self) -> dict:
        """Get summary of loaded patterns."""
        return {
            'root_path': str(self.root_path),
            'total_patterns': len(self.patterns),
            'negated_patterns': len(self.negated_patterns),
            'default_patterns': len(self.DEFAULT_EXCLUDES),
            'custom_patterns': len(self.patterns) - len(self.DEFAULT_EXCLUDES)
        }