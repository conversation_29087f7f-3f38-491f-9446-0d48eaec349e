"""Git integration service for managing repositories and commits."""
import subprocess
import json
import logging
from pathlib import Path
from typing import List, Dict, Optional, Tuple
from datetime import datetime
import tempfile
import shutil

logger = logging.getLogger(__name__)


class GitIntegration:
    """Handle git operations for agent evaluation."""
    
    def __init__(self, repo_path: str):
        """
        Initialize git integration service.
        
        Args:
            repo_path: Path to git repository
        """
        self.repo_path = Path(repo_path).resolve()
        if not self._is_git_repo():
            raise ValueError(f"Not a git repository: {repo_path}")
    
    def _is_git_repo(self) -> bool:
        """Check if path is a git repository."""
        try:
            result = subprocess.run(
                ['git', 'rev-parse', '--git-dir'],
                cwd=self.repo_path,
                capture_output=True,
                text=True
            )
            return result.returncode == 0
        except Exception:
            return False
    
    def _run_git_command(self, args: List[str], check: bool = True) -> subprocess.CompletedProcess:
        """
        Run a git command and return the result.
        
        Args:
            args: Git command arguments
            check: Whether to raise on non-zero exit
            
        Returns:
            CompletedProcess result
        """
        cmd = ['git'] + args
        logger.debug(f"Running: {' '.join(cmd)}")
        
        result = subprocess.run(
            cmd,
            cwd=self.repo_path,
            capture_output=True,
            text=True,
            check=check
        )
        
        if result.returncode != 0:
            logger.error(f"Git command failed: {result.stderr}")
        
        return result
    
    def get_current_branch(self) -> str:
        """Get current branch name."""
        result = self._run_git_command(['branch', '--show-current'])
        return result.stdout.strip()
    
    def get_commit_info(self, commit_hash: str) -> Dict:
        """
        Get detailed information about a commit.
        
        Args:
            commit_hash: Commit hash or reference
            
        Returns:
            Dict with commit information
        """
        # Get commit metadata
        format_str = '%H%n%h%n%an%n%ae%n%at%n%cn%n%ce%n%ct%n%s%n%b'
        result = self._run_git_command(
            ['show', '--no-patch', f'--format={format_str}', commit_hash]
        )
        
        lines = result.stdout.strip().split('\n')
        
        commit_info = {
            'hash': lines[0],
            'short_hash': lines[1],
            'author_name': lines[2],
            'author_email': lines[3],
            'author_date': datetime.fromtimestamp(int(lines[4])).isoformat(),
            'committer_name': lines[5],
            'committer_email': lines[6],
            'commit_date': datetime.fromtimestamp(int(lines[7])).isoformat(),
            'subject': lines[8],
            'body': '\n'.join(lines[9:]) if len(lines) > 9 else ''
        }
        
        # Get diff stats
        stats_result = self._run_git_command(
            ['show', '--stat', '--format=', commit_hash]
        )
        commit_info['stats'] = stats_result.stdout.strip()
        
        # Get changed files
        files_result = self._run_git_command(
            ['show', '--name-status', '--format=', commit_hash]
        )
        
        changed_files = []
        for line in files_result.stdout.strip().split('\n'):
            if line:
                parts = line.split('\t')
                if len(parts) >= 2:
                    changed_files.append({
                        'status': parts[0],
                        'path': parts[1]
                    })
        
        commit_info['changed_files'] = changed_files
        
        return commit_info
    
    def get_commit_diff(self, commit_hash: str, context_lines: int = 3) -> str:
        """
        Get the diff for a specific commit.
        
        Args:
            commit_hash: Commit hash or reference
            context_lines: Number of context lines in diff
            
        Returns:
            Diff content as string
        """
        result = self._run_git_command(
            ['show', f'-U{context_lines}', commit_hash]
        )
        return result.stdout
    
    def get_commit_list(
        self, 
        branch: Optional[str] = None,
        since: Optional[str] = None,
        until: Optional[str] = None,
        author: Optional[str] = None,
        max_count: int = 100
    ) -> List[Dict]:
        """
        Get list of commits with filters.
        
        Args:
            branch: Branch name (default: current branch)
            since: Start date or commit
            until: End date or commit  
            author: Filter by author
            max_count: Maximum number of commits
            
        Returns:
            List of commit info dicts
        """
        args = ['log', f'--max-count={max_count}', '--format=%H|%h|%an|%ae|%at|%s']
        
        if branch:
            args.append(branch)
        if since:
            args.append(f'--since={since}')
        if until:
            args.append(f'--until={until}')
        if author:
            args.append(f'--author={author}')
        
        result = self._run_git_command(args)
        
        commits = []
        for line in result.stdout.strip().split('\n'):
            if line:
                parts = line.split('|')
                if len(parts) >= 6:
                    commits.append({
                        'hash': parts[0],
                        'short_hash': parts[1],
                        'author_name': parts[2],
                        'author_email': parts[3],
                        'date': datetime.fromtimestamp(int(parts[4])).isoformat(),
                        'subject': parts[5]
                    })
        
        return commits
    
    def get_file_at_commit(self, file_path: str, commit_hash: str) -> Optional[str]:
        """
        Get file content at a specific commit.
        
        Args:
            file_path: Path to file relative to repo root
            commit_hash: Commit hash or reference
            
        Returns:
            File content or None if file doesn't exist
        """
        try:
            result = self._run_git_command(
                ['show', f'{commit_hash}:{file_path}'],
                check=False
            )
            if result.returncode == 0:
                return result.stdout
            return None
        except Exception as e:
            logger.error(f"Failed to get file {file_path} at {commit_hash}: {e}")
            return None
    
    def create_worktree(self, worktree_name: str, base_commit: Optional[str] = None) -> Path:
        """
        Create a new git worktree.
        
        Args:
            worktree_name: Name for the worktree
            base_commit: Base commit/branch (default: current HEAD)
            
        Returns:
            Path to the new worktree
        """
        # Create temporary directory for worktree
        worktree_base = Path(tempfile.gettempdir()) / 'agent_eval_worktrees'
        worktree_base.mkdir(exist_ok=True)
        
        worktree_path = worktree_base / worktree_name
        
        # Remove if exists
        if worktree_path.exists():
            self.remove_worktree(worktree_name)
        
        # Create worktree
        args = ['worktree', 'add', str(worktree_path)]
        if base_commit:
            args.extend(['-b', f'agent_{worktree_name}', base_commit])
        
        self._run_git_command(args)
        
        logger.info(f"Created worktree at {worktree_path}")
        return worktree_path
    
    def remove_worktree(self, worktree_name: str):
        """
        Remove a git worktree.
        
        Args:
            worktree_name: Name of the worktree to remove
        """
        # First remove from git
        self._run_git_command(['worktree', 'remove', '--force', worktree_name], check=False)
        
        # Also remove directory if it exists
        worktree_base = Path(tempfile.gettempdir()) / 'agent_eval_worktrees'
        worktree_path = worktree_base / worktree_name
        
        if worktree_path.exists():
            shutil.rmtree(worktree_path)
            logger.info(f"Removed worktree at {worktree_path}")
    
    def list_worktrees(self) -> List[Dict]:
        """
        List all worktrees for this repository.
        
        Returns:
            List of worktree info dicts
        """
        result = self._run_git_command(['worktree', 'list', '--porcelain'])
        
        worktrees = []
        current_worktree = {}
        
        for line in result.stdout.strip().split('\n'):
            if not line:
                if current_worktree:
                    worktrees.append(current_worktree)
                    current_worktree = {}
            elif line.startswith('worktree '):
                current_worktree['path'] = line[9:]
            elif line.startswith('HEAD '):
                current_worktree['head'] = line[5:]
            elif line.startswith('branch '):
                current_worktree['branch'] = line[7:]
            elif line == 'bare':
                current_worktree['bare'] = True
            elif line == 'detached':
                current_worktree['detached'] = True
        
        if current_worktree:
            worktrees.append(current_worktree)
        
        return worktrees
    
    def get_diff_between_commits(
        self, 
        commit1: str, 
        commit2: str,
        context_lines: int = 3
    ) -> Dict:
        """
        Get diff between two commits.
        
        Args:
            commit1: First commit (older)
            commit2: Second commit (newer)
            context_lines: Number of context lines
            
        Returns:
            Dict with diff information
        """
        # Get diff
        diff_result = self._run_git_command(
            ['diff', f'-U{context_lines}', commit1, commit2]
        )
        
        # Get stats
        stats_result = self._run_git_command(
            ['diff', '--stat', commit1, commit2]
        )
        
        # Get changed files
        files_result = self._run_git_command(
            ['diff', '--name-status', commit1, commit2]
        )
        
        changed_files = []
        for line in files_result.stdout.strip().split('\n'):
            if line:
                parts = line.split('\t')
                if len(parts) >= 2:
                    changed_files.append({
                        'status': parts[0],
                        'path': parts[1]
                    })
        
        return {
            'diff': diff_result.stdout,
            'stats': stats_result.stdout,
            'changed_files': changed_files,
            'commit1': commit1,
            'commit2': commit2
        }
    
    def get_file_history(
        self, 
        file_path: str,
        max_count: int = 50
    ) -> List[Dict]:
        """
        Get commit history for a specific file.
        
        Args:
            file_path: Path to file
            max_count: Maximum commits to return
            
        Returns:
            List of commits that modified this file
        """
        result = self._run_git_command([
            'log',
            f'--max-count={max_count}',
            '--format=%H|%h|%an|%at|%s',
            '--',
            file_path
        ])
        
        commits = []
        for line in result.stdout.strip().split('\n'):
            if line:
                parts = line.split('|')
                if len(parts) >= 5:
                    commits.append({
                        'hash': parts[0],
                        'short_hash': parts[1],
                        'author': parts[2],
                        'date': datetime.fromtimestamp(int(parts[3])).isoformat(),
                        'subject': parts[4]
                    })
        
        return commits
    
    def cleanup_worktrees(self):
        """Clean up all worktrees created by this service."""
        worktrees = self.list_worktrees()
        
        for worktree in worktrees:
            path = Path(worktree['path'])
            # Only clean up our temporary worktrees
            if 'agent_eval_worktrees' in str(path):
                try:
                    self._run_git_command(['worktree', 'remove', '--force', str(path)], check=False)
                    if path.exists():
                        shutil.rmtree(path)
                    logger.info(f"Cleaned up worktree: {path}")
                except Exception as e:
                    logger.error(f"Failed to clean up worktree {path}: {e}")