"""Service for extracting various archive formats."""
import os
import zipfile
import tarfile
import tempfile
import shutil
from pathlib import Path
from typing import Optional, List
import logging

logger = logging.getLogger(__name__)


class ArchiveExtractor:
    """Extract various archive formats to temporary directories."""
    
    SUPPORTED_FORMATS = {
        '.zip': 'zip',
        '.tar': 'tar',
        '.tar.gz': 'tar.gz',
        '.tgz': 'tar.gz',
        '.tar.bz2': 'tar.bz2',
        '.tbz2': 'tar.bz2'
    }
    
    def __init__(self, temp_base_dir: Optional[str] = None):
        """Initialize extractor with optional base directory for temp files."""
        self.temp_base_dir = temp_base_dir or tempfile.gettempdir()
    
    async def extract_archive(self, file_path: str, target_dir: Optional[str] = None) -> str:
        """
        Extract archive to target directory.
        
        Args:
            file_path: Path to archive file
            target_dir: Target directory (creates temp dir if not provided)
            
        Returns:
            Path to extracted content
        """
        if not os.path.exists(file_path):
            raise ValueError(f"Archive file not found: {file_path}")
        
        # Determine archive format
        file_ext = self._get_file_extension(file_path)
        if file_ext not in self.SUPPORTED_FORMATS:
            raise ValueError(f"Unsupported archive format: {file_ext}")
        
        # Create target directory if not provided
        if target_dir is None:
            target_dir = tempfile.mkdtemp(dir=self.temp_base_dir)
        else:
            os.makedirs(target_dir, exist_ok=True)
        
        # Extract based on format
        format_type = self.SUPPORTED_FORMATS[file_ext]
        
        try:
            if format_type == 'zip':
                await self._extract_zip(file_path, target_dir)
            else:
                await self._extract_tar(file_path, target_dir, format_type)
            
            # Find the root directory of extracted content
            extracted_root = self._find_extracted_root(target_dir)
            
            logger.info(f"Successfully extracted {file_path} to {extracted_root}")
            return extracted_root
            
        except Exception as e:
            # Clean up on failure
            if target_dir and os.path.exists(target_dir):
                shutil.rmtree(target_dir)
            raise Exception(f"Failed to extract archive: {str(e)}")
    
    async def _extract_zip(self, file_path: str, target_dir: str):
        """Extract ZIP archive."""
        with zipfile.ZipFile(file_path, 'r') as zip_ref:
            # Check for dangerous paths
            for member in zip_ref.namelist():
                if os.path.isabs(member) or ".." in member:
                    raise ValueError(f"Dangerous path in archive: {member}")
            
            zip_ref.extractall(target_dir)
    
    async def _extract_tar(self, file_path: str, target_dir: str, format_type: str):
        """Extract TAR archive with various compressions."""
        mode = 'r:gz' if 'gz' in format_type else 'r:bz2' if 'bz2' in format_type else 'r'
        
        with tarfile.open(file_path, mode) as tar_ref:
            # Check for dangerous paths
            for member in tar_ref.getmembers():
                if os.path.isabs(member.name) or ".." in member.name:
                    raise ValueError(f"Dangerous path in archive: {member.name}")
            
            tar_ref.extractall(target_dir)
    
    def _get_file_extension(self, file_path: str) -> str:
        """Get file extension, handling double extensions like .tar.gz."""
        name = os.path.basename(file_path).lower()
        
        # Check for double extensions
        for ext in ['.tar.gz', '.tar.bz2']:
            if name.endswith(ext):
                return ext
        
        # Single extension
        return os.path.splitext(name)[1]
    
    def _find_extracted_root(self, target_dir: str) -> str:
        """
        Find the root directory of extracted content.
        
        If archive contains a single root directory, return that.
        Otherwise return the target directory itself.
        """
        contents = os.listdir(target_dir)
        
        # If single directory at root, use that
        if len(contents) == 1:
            single_item = os.path.join(target_dir, contents[0])
            if os.path.isdir(single_item):
                return single_item
        
        return target_dir
    
    def cleanup_directory(self, directory: str):
        """Clean up temporary directory."""
        try:
            if os.path.exists(directory) and directory.startswith(self.temp_base_dir):
                shutil.rmtree(directory)
                logger.info(f"Cleaned up temporary directory: {directory}")
        except Exception as e:
            logger.error(f"Failed to cleanup directory {directory}: {e}")
    
    async def extract_files_from_list(self, file_paths: List[str], target_dir: str) -> str:
        """
        Copy multiple files to target directory, preserving structure.
        
        Args:
            file_paths: List of file paths
            target_dir: Target directory
            
        Returns:
            Path to target directory
        """
        os.makedirs(target_dir, exist_ok=True)
        
        for file_path in file_paths:
            if not os.path.exists(file_path):
                logger.warning(f"File not found: {file_path}")
                continue
            
            # Copy file to target directory
            filename = os.path.basename(file_path)
            target_path = os.path.join(target_dir, filename)
            
            shutil.copy2(file_path, target_path)
            logger.debug(f"Copied {file_path} to {target_path}")
        
        return target_dir