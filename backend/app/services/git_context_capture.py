"""Git context capture service for 90-100 evaluation."""
import logging
from pathlib import Path
from typing import Dict, List, Optional
from app.services.git_integration import GitIntegration
from app.services.token_estimator import TokenEstimator

logger = logging.getLogger(__name__)


class GitContextCapture:
    """Capture context from git commits for 90-100 agent evaluation."""
    
    def __init__(self, token_estimator: Optional[TokenEstimator] = None):
        """
        Initialize git context capture service.
        
        Args:
            token_estimator: Token estimator instance
        """
        self.token_estimator = token_estimator or TokenEstimator()
    
    async def capture_commit_context(
        self, 
        repo_path: str,
        commit_hash: str,
        submission_id: str,
        include_diff: bool = True,
        include_files: bool = True,
        context_lines: int = 3
    ) -> Dict:
        """
        Capture context from a specific git commit.
        
        Args:
            repo_path: Path to git repository
            commit_hash: Commit hash to capture
            submission_id: ID of the submission this context belongs to
            include_diff: Whether to include commit diff
            include_files: Whether to include full file contents
            context_lines: Number of context lines in diff
            
        Returns:
            Dict with captured context
        """
        git_service = GitIntegration(repo_path)
        
        # Get commit information
        commit_info = git_service.get_commit_info(commit_hash)
        
        contexts = []
        total_tokens = 0
        
        # Capture commit metadata as context
        metadata_context = {
            'submission_id': submission_id,
            'context_type': 'commit_metadata',
            'commit_hash': commit_info['hash'],
            'content': self._format_commit_metadata(commit_info),
            'file_metadata': {
                'type': 'commit_metadata',
                'commit_hash': commit_info['hash'],
                'author': commit_info['author_name'],
                'date': commit_info['commit_date']
            }
        }
        
        # Estimate tokens for metadata
        tokens, _ = self.token_estimator.estimate_tokens(
            metadata_context['content'], 
            'commit_metadata.txt'
        )
        metadata_context['estimated_tokens'] = tokens
        total_tokens += tokens
        contexts.append(metadata_context)
        
        # Capture diff if requested
        if include_diff:
            diff_content = git_service.get_commit_diff(commit_hash, context_lines)
            
            diff_context = {
                'submission_id': submission_id,
                'context_type': 'commit_diff',
                'commit_hash': commit_info['hash'],
                'content': diff_content,
                'file_metadata': {
                    'type': 'diff',
                    'commit_hash': commit_info['hash'],
                    'context_lines': context_lines
                }
            }
            
            # Estimate tokens for diff
            tokens, _ = self.token_estimator.estimate_tokens(
                diff_content,
                'commit.diff'
            )
            diff_context['estimated_tokens'] = tokens
            total_tokens += tokens
            contexts.append(diff_context)
        
        # Capture changed files if requested
        if include_files:
            for file_change in commit_info['changed_files']:
                file_path = file_change['path']
                status = file_change['status']
                
                # Skip deleted files
                if status == 'D':
                    continue
                
                # Get file content at this commit
                file_content = git_service.get_file_at_commit(file_path, commit_hash)
                if file_content is None:
                    continue
                
                file_context = {
                    'submission_id': submission_id,
                    'context_type': 'file',
                    'commit_hash': commit_info['hash'],
                    'file_path': file_path,
                    'content': file_content,
                    'file_metadata': {
                        'type': self._get_file_type(file_path),
                        'change_status': status,
                        'commit_hash': commit_info['hash']
                    }
                }
                
                # Estimate tokens
                tokens, token_details = self.token_estimator.estimate_tokens(
                    file_content,
                    file_path
                )
                file_context['estimated_tokens'] = tokens
                file_context['file_metadata']['tokens'] = tokens
                file_context['file_metadata']['token_details'] = token_details
                
                total_tokens += tokens
                contexts.append(file_context)
        
        return {
            'submission_id': submission_id,
            'commit_hash': commit_hash,
            'contexts': contexts,
            'total_tokens': total_tokens,
            'commit_info': commit_info
        }
    
    async def capture_commit_range_context(
        self,
        repo_path: str,
        base_commit: str,
        target_commit: str,
        submission_id: str,
        include_intermediate: bool = True
    ) -> Dict:
        """
        Capture context from a range of commits.
        
        Args:
            repo_path: Path to git repository
            base_commit: Base commit (start of range)
            target_commit: Target commit (end of range)
            submission_id: ID of the submission
            include_intermediate: Whether to include intermediate commits
            
        Returns:
            Dict with captured context
        """
        git_service = GitIntegration(repo_path)
        
        contexts = []
        total_tokens = 0
        commits_captured = []
        
        # Get diff between commits
        diff_info = git_service.get_diff_between_commits(
            base_commit,
            target_commit
        )
        
        # Capture range diff
        range_diff_context = {
            'submission_id': submission_id,
            'context_type': 'range_diff',
            'content': diff_info['diff'],
            'file_metadata': {
                'type': 'range_diff',
                'base_commit': base_commit,
                'target_commit': target_commit,
                'stats': diff_info['stats']
            }
        }
        
        tokens, _ = self.token_estimator.estimate_tokens(
            diff_info['diff'],
            'range.diff'
        )
        range_diff_context['estimated_tokens'] = tokens
        total_tokens += tokens
        contexts.append(range_diff_context)
        
        # Capture individual commits if requested
        if include_intermediate:
            # Get commits in range
            # Use git log with commit range syntax
            git_result = git_service._run_git_command([
                'log',
                '--format=%H',
                f'{base_commit}..{target_commit}'
            ])
            
            commit_hashes = [
                h.strip() for h in git_result.stdout.strip().split('\n')
                if h.strip()
            ]
            
            # Capture each commit
            for commit_hash in reversed(commit_hashes):  # Process in chronological order
                commit_context = await self.capture_commit_context(
                    repo_path,
                    commit_hash,
                    submission_id,
                    include_diff=True,
                    include_files=False  # Don't include full files for each commit
                )
                
                contexts.extend(commit_context['contexts'])
                total_tokens += commit_context['total_tokens']
                commits_captured.append(commit_hash)
        
        # Capture final state of changed files
        for file_change in diff_info['changed_files']:
            file_path = file_change['path']
            status = file_change['status']
            
            if status == 'D':
                continue
            
            # Get file at target commit
            file_content = git_service.get_file_at_commit(file_path, target_commit)
            if file_content is None:
                continue
            
            file_context = {
                'submission_id': submission_id,
                'context_type': 'file',
                'file_path': file_path,
                'content': file_content,
                'file_metadata': {
                    'type': self._get_file_type(file_path),
                    'change_status': status,
                    'at_commit': target_commit
                }
            }
            
            tokens, token_details = self.token_estimator.estimate_tokens(
                file_content,
                file_path
            )
            file_context['estimated_tokens'] = tokens
            file_context['file_metadata']['tokens'] = tokens
            file_context['file_metadata']['token_details'] = token_details
            
            total_tokens += tokens
            contexts.append(file_context)
        
        return {
            'submission_id': submission_id,
            'base_commit': base_commit,
            'target_commit': target_commit,
            'contexts': contexts,
            'total_tokens': total_tokens,
            'commits_captured': commits_captured,
            'changed_files': diff_info['changed_files']
        }
    
    async def estimate_commit_tokens(
        self,
        repo_path: str,
        commit_hash: str,
        include_diff: bool = True,
        include_files: bool = True
    ) -> Dict:
        """
        Estimate tokens for a commit without full capture.
        
        Args:
            repo_path: Path to git repository
            commit_hash: Commit to estimate
            include_diff: Whether to include diff
            include_files: Whether to include file contents
            
        Returns:
            Token estimation summary
        """
        git_service = GitIntegration(repo_path)
        
        commit_info = git_service.get_commit_info(commit_hash)
        total_tokens = 0
        
        # Estimate metadata tokens
        metadata_text = self._format_commit_metadata(commit_info)
        metadata_tokens, _ = self.token_estimator.estimate_tokens(
            metadata_text,
            'metadata.txt'
        )
        total_tokens += metadata_tokens
        
        # Estimate diff tokens
        diff_tokens = 0
        if include_diff:
            diff_content = git_service.get_commit_diff(commit_hash)
            diff_tokens, _ = self.token_estimator.estimate_tokens(
                diff_content,
                'commit.diff'
            )
            total_tokens += diff_tokens
        
        # Estimate file tokens
        file_tokens = 0
        file_count = 0
        if include_files:
            for file_change in commit_info['changed_files']:
                if file_change['status'] == 'D':
                    continue
                
                file_content = git_service.get_file_at_commit(
                    file_change['path'],
                    commit_hash
                )
                if file_content:
                    tokens, _ = self.token_estimator.estimate_tokens(
                        file_content,
                        file_change['path']
                    )
                    file_tokens += tokens
                    file_count += 1
        
        return {
            'commit_hash': commit_hash,
            'total_tokens': total_tokens,
            'metadata_tokens': metadata_tokens,
            'diff_tokens': diff_tokens,
            'file_tokens': file_tokens,
            'file_count': file_count,
            'changed_files': len(commit_info['changed_files'])
        }
    
    def _format_commit_metadata(self, commit_info: Dict) -> str:
        """Format commit metadata as readable text."""
        lines = [
            f"Commit: {commit_info['hash']}",
            f"Author: {commit_info['author_name']} <{commit_info['author_email']}>",
            f"Date: {commit_info['author_date']}",
            f"",
            f"    {commit_info['subject']}",
        ]
        
        if commit_info.get('body'):
            lines.extend([
                "",
                *[f"    {line}" for line in commit_info['body'].split('\n')]
            ])
        
        if commit_info.get('stats'):
            lines.extend([
                "",
                "Stats:",
                *[f"  {line}" for line in commit_info['stats'].split('\n') if line]
            ])
        
        return '\n'.join(lines)
    
    def _get_file_type(self, file_path: str) -> str:
        """Determine file type from path."""
        path = Path(file_path)
        ext = path.suffix.lower()
        
        type_map = {
            '.py': 'python',
            '.js': 'javascript',
            '.ts': 'typescript',
            '.jsx': 'javascript',
            '.tsx': 'typescript',
            '.java': 'java',
            '.cpp': 'cpp',
            '.c': 'c',
            '.h': 'header',
            '.go': 'go',
            '.rs': 'rust',
            '.rb': 'ruby',
            '.php': 'php',
            '.cs': 'csharp',
            '.swift': 'swift',
            '.kt': 'kotlin',
            '.scala': 'scala',
            '.html': 'html',
            '.css': 'css',
            '.scss': 'scss',
            '.json': 'json',
            '.yaml': 'yaml',
            '.yml': 'yaml',
            '.xml': 'xml',
            '.md': 'markdown',
            '.txt': 'text',
            '.sh': 'shell',
            '.bash': 'bash',
            '.dockerfile': 'dockerfile',
            '.makefile': 'makefile',
        }
        
        file_type = type_map.get(ext, 'unknown')
        
        # Check special filenames
        name_lower = path.name.lower()
        if name_lower == 'dockerfile':
            file_type = 'dockerfile'
        elif name_lower == 'makefile':
            file_type = 'makefile'
        elif name_lower.endswith('.dockerfile'):
            file_type = 'dockerfile'
        
        return file_type