"""Streaming support for real-time evaluation progress."""
import asyncio
import json
import logging
from typing import Dict, Any, Optional, AsyncGenerator
from datetime import datetime
from uuid import UUID
from collections import defaultdict
from asyncio import Queue

logger = logging.getLogger(__name__)


class EvaluationStreamManager:
    """Manage streaming connections for evaluation progress."""
    
    def __init__(self):
        # Map task_id to list of client queues
        self._subscribers: Dict[str, list[Queue]] = defaultdict(list)
        # Map task_id to latest progress
        self._latest_progress: Dict[str, Dict[str, Any]] = {}
        # Lock for thread safety
        self._lock = asyncio.Lock()
    
    async def subscribe(
        self,
        task_id: str,
        client_id: Optional[str] = None
    ) -> AsyncGenerator[str, None]:
        """
        Subscribe to evaluation progress updates.
        
        Args:
            task_id: Task ID to subscribe to
            client_id: Optional client identifier
            
        Yields:
            SSE formatted progress updates
        """
        queue = Queue(maxsize=100)
        
        async with self._lock:
            self._subscribers[task_id].append(queue)
            
            # Send initial progress if available
            if task_id in self._latest_progress:
                await queue.put(self._format_sse(
                    "progress",
                    self._latest_progress[task_id]
                ))
        
        try:
            # Send initial connection message
            yield self._format_sse("connected", {
                "task_id": task_id,
                "client_id": client_id,
                "timestamp": datetime.utcnow().isoformat()
            })
            
            # Stream updates
            while True:
                try:
                    # Wait for message with timeout
                    message = await asyncio.wait_for(
                        queue.get(),
                        timeout=30.0  # 30 second timeout
                    )
                    
                    if message is None:  # Sentinel for completion
                        break
                        
                    yield message
                    
                except asyncio.TimeoutError:
                    # Send keepalive
                    yield self._format_sse("keepalive", {
                        "timestamp": datetime.utcnow().isoformat()
                    })
                    
        except asyncio.CancelledError:
            logger.info(f"Client disconnected from task {task_id}")
            raise
            
        finally:
            # Clean up subscription
            async with self._lock:
                if queue in self._subscribers[task_id]:
                    self._subscribers[task_id].remove(queue)
                if not self._subscribers[task_id]:
                    del self._subscribers[task_id]
                    if task_id in self._latest_progress:
                        del self._latest_progress[task_id]
    
    async def publish_progress(
        self,
        task_id: str,
        progress_data: Dict[str, Any]
    ) -> None:
        """
        Publish progress update to all subscribers.
        
        Args:
            task_id: Task ID
            progress_data: Progress data to publish
        """
        async with self._lock:
            # Store latest progress
            self._latest_progress[task_id] = progress_data
            
            # Send to all subscribers
            if task_id in self._subscribers:
                message = self._format_sse("progress", progress_data)
                
                # Send to all queues, removing failed ones
                failed_queues = []
                for queue in self._subscribers[task_id]:
                    try:
                        # Non-blocking put
                        queue.put_nowait(message)
                    except asyncio.QueueFull:
                        logger.warning(f"Queue full for task {task_id}")
                        failed_queues.append(queue)
                
                # Remove failed queues
                for queue in failed_queues:
                    self._subscribers[task_id].remove(queue)
    
    async def publish_completion(
        self,
        task_id: str,
        result_data: Dict[str, Any]
    ) -> None:
        """
        Publish evaluation completion.
        
        Args:
            task_id: Task ID
            result_data: Final result data
        """
        async with self._lock:
            if task_id in self._subscribers:
                # Send completion message
                message = self._format_sse("completed", result_data)
                
                for queue in self._subscribers[task_id]:
                    try:
                        await queue.put(message)
                        # Send sentinel to close connection
                        await queue.put(None)
                    except Exception as e:
                        logger.error(f"Failed to send completion: {e}")
    
    async def publish_error(
        self,
        task_id: str,
        error_data: Dict[str, Any]
    ) -> None:
        """
        Publish evaluation error.
        
        Args:
            task_id: Task ID
            error_data: Error details
        """
        async with self._lock:
            if task_id in self._subscribers:
                message = self._format_sse("error", error_data)
                
                for queue in self._subscribers[task_id]:
                    try:
                        await queue.put(message)
                    except Exception as e:
                        logger.error(f"Failed to send error: {e}")
    
    def _format_sse(
        self,
        event: str,
        data: Dict[str, Any]
    ) -> str:
        """Format message as Server-Sent Event."""
        return f"event: {event}\ndata: {json.dumps(data)}\n\n"
    
    def get_subscriber_count(self, task_id: str) -> int:
        """Get number of subscribers for a task."""
        return len(self._subscribers.get(task_id, []))
    
    def get_active_tasks(self) -> list[str]:
        """Get list of tasks with active subscribers."""
        return list(self._subscribers.keys())


class ProgressReporter:
    """Helper class for reporting evaluation progress."""
    
    def __init__(
        self,
        stream_manager: EvaluationStreamManager,
        task_id: str
    ):
        """
        Initialize progress reporter.
        
        Args:
            stream_manager: Stream manager instance
            task_id: Task ID to report progress for
        """
        self.stream_manager = stream_manager
        self.task_id = task_id
        self.start_time = datetime.now()
        self.current_step = 0
        self.total_steps = 0
        self.step_times = {}
    
    async def start(
        self,
        total_steps: int,
        initial_message: str = "Starting evaluation"
    ) -> None:
        """Start progress reporting."""
        self.total_steps = total_steps
        await self._report({
            'status': 'started',
            'message': initial_message,
            'total_steps': total_steps
        })
    
    async def update_step(
        self,
        step: int,
        message: str,
        details: Optional[Dict[str, Any]] = None
    ) -> None:
        """Update current step progress."""
        self.current_step = step
        step_start = self.step_times.get(step - 1, self.start_time)
        step_duration = (datetime.now() - step_start).total_seconds()
        
        self.step_times[step] = datetime.now()
        
        progress_data = {
            'status': 'in_progress',
            'current_step': step,
            'total_steps': self.total_steps,
            'progress': step / self.total_steps if self.total_steps > 0 else 0,
            'message': message,
            'step_duration': step_duration,
            'total_duration': (datetime.now() - self.start_time).total_seconds()
        }
        
        if details:
            progress_data['details'] = details
        
        await self._report(progress_data)
    
    async def add_log(
        self,
        level: str,
        message: str,
        details: Optional[Dict[str, Any]] = None
    ) -> None:
        """Add log message to progress."""
        log_data = {
            'status': 'log',
            'level': level,
            'message': message,
            'timestamp': datetime.now().isoformat()
        }
        
        if details:
            log_data['details'] = details
        
        await self._report(log_data)
    
    async def complete(
        self,
        message: str = "Evaluation completed",
        results: Optional[Dict[str, Any]] = None
    ) -> None:
        """Mark evaluation as complete."""
        total_duration = (datetime.now() - self.start_time).total_seconds()
        
        completion_data = {
            'status': 'completed',
            'message': message,
            'total_duration': total_duration,
            'completed_at': datetime.now().isoformat()
        }
        
        if results:
            completion_data['results'] = results
        
        await self.stream_manager.publish_completion(
            self.task_id,
            completion_data
        )
    
    async def error(
        self,
        error_message: str,
        error_details: Optional[Dict[str, Any]] = None
    ) -> None:
        """Report evaluation error."""
        error_data = {
            'status': 'error',
            'message': error_message,
            'error_at_step': self.current_step,
            'total_duration': (datetime.now() - self.start_time).total_seconds(),
            'timestamp': datetime.now().isoformat()
        }
        
        if error_details:
            error_data['details'] = error_details
        
        await self.stream_manager.publish_error(
            self.task_id,
            error_data
        )
    
    async def _report(self, data: Dict[str, Any]) -> None:
        """Internal method to report progress."""
        await self.stream_manager.publish_progress(
            self.task_id,
            data
        )


# Global stream manager instance
stream_manager = EvaluationStreamManager()


def get_stream_manager() -> EvaluationStreamManager:
    """Get the global stream manager instance."""
    return stream_manager