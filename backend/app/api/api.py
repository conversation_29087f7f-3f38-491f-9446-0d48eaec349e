from fastapi import APIRouter

from app.api.endpoints import tasks, health, agent_evaluations_router

api_router = APIRouter()

# Include routers from endpoint modules
api_router.include_router(tasks.router, prefix="/tasks", tags=["Tasks & Evaluations"])
api_router.include_router(health.router, tags=["Health"])
api_router.include_router(agent_evaluations_router.router, prefix="/agent-evaluations", tags=["Agent Evaluations"])
# Add other routers here if needed 