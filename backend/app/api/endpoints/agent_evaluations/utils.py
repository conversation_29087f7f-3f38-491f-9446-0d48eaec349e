"""Utility functions for agent evaluation endpoints."""
from typing import Union
from uuid import UUID


def ensure_uuid_str(id_value: Union[str, UUID]) -> str:
    """Ensure ID is a string representation of UUID."""
    if isinstance(id_value, UUID):
        return str(id_value)
    return id_value


def ensure_uuid(id_value: Union[str, UUID]) -> UUID:
    """Ensure ID is a UUID object."""
    if isinstance(id_value, str):
        return UUID(id_value)
    return id_value