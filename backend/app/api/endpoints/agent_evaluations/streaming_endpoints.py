"""Streaming and progress tracking endpoints for AI agent evaluation system."""
import logging
from uuid import UUID
from fastapi import APIRouter, Depends, HTTPException
from fastapi.responses import StreamingResponse
from sqlalchemy.ext.asyncio import AsyncSession

from app.db.base import get_db
from app.crud.agent_evaluation import agent_task as crud_task
from app.services.evaluation_streaming import get_stream_manager
from .exceptions import TaskNotFoundException

logger = logging.getLogger(__name__)

router = APIRouter()

# Service instances
stream_manager = get_stream_manager()


@router.get("/stream/{task_id}")
async def stream_evaluation_progress(task_id: UUID, db: AsyncSession = Depends(get_db)) -> StreamingResponse:
    """Stream evaluation progress updates via Server-Sent Events."""
    if not await crud_task.get(db, id=task_id):
        raise TaskNotFoundException(str(task_id))
    
    async def event_stream():
        async for message in stream_manager.subscribe(str(task_id)):
            yield message
    
    return StreamingResponse(event_stream(), media_type="text/event-stream")