"""Task-related endpoints for AI agent evaluation system."""
import logging
from typing import List, Optional, Any, Dict
from uuid import UUID
from fastapi import APIRouter, Depends, HTTPException, Body, Request
from sqlalchemy.ext.asyncio import AsyncSession

from app.db.base import get_db
from app.schemas.agent_evaluation import (
    AgentEvaluationTaskCreate,
    AgentEvaluationTaskInDB,
    EvaluationCriteriaInDB,
    EvaluationCriteriaCreate,
    EvaluationCriteriaUpdate,
)
from app.crud.agent_evaluation import (
    agent_task as crud_task,
    evaluation_criteria as crud_criteria,
)
from .exceptions import TaskNotFoundException, InvalidTaskStatusException
from .state_machine import TaskStateMachine, StatusTransitionManager

logger = logging.getLogger(__name__)

router = APIRouter()
status_manager = StatusTransitionManager()


@router.post("/tasks", response_model=AgentEvaluationTaskInDB)
async def create_evaluation_task(
    *,
    db: AsyncSession = Depends(get_db),
    task_in: AgentEvaluationTaskCreate
) -> Any:
    """Create a new agent evaluation task."""
    task = await crud_task.create(db, obj_in=task_in)
    return task


@router.get("/tasks", response_model=List[AgentEvaluationTaskInDB])
async def list_evaluation_tasks(
    db: AsyncSession = Depends(get_db),
    skip: int = 0,
    limit: int = 100,
    evaluation_type: Optional[str] = None,
    status: Optional[str] = None
) -> Any:
    """List all evaluation tasks."""
    tasks = await crud_task.get_multi(db, skip=skip, limit=limit)
    
    # Apply filters after fetching
    filtered_tasks = []
    for t in tasks:
        # Use getattr to safely access attributes and avoid Column type issues
        task_eval_type = getattr(t, 'evaluation_type', '')
        task_status = getattr(t, 'status', '')
        
        if evaluation_type and task_eval_type != evaluation_type:
            continue
        if status and task_status != status:
            continue
        filtered_tasks.append(t)
    
    return filtered_tasks


@router.get("/tasks/{task_id}", response_model=AgentEvaluationTaskInDB)
async def get_evaluation_task(
    *,
    db: AsyncSession = Depends(get_db),
    task_id: UUID
) -> Any:
    """Get evaluation task details."""
    task = await crud_task.get(db, id=task_id)
    if not task:
        raise TaskNotFoundException(str(task_id))
    return task


@router.delete("/tasks/{task_id}", status_code=204)
async def delete_evaluation_task(
    *,
    db: AsyncSession = Depends(get_db),
    task_id: UUID
):
    """Delete an evaluation task and all its associated data."""
    task = await crud_task.get(db, id=task_id)
    if not task:
        raise TaskNotFoundException(str(task_id))
    await crud_task.remove(db, id=task_id)
    logger.info(f"Deleted agent evaluation task {task_id}")


@router.patch("/tasks/{task_id}")
async def update_task_status(
    *,
    db: AsyncSession = Depends(get_db),
    task_id: UUID,
    status: str = Body(..., embed=True)
) -> Any:
    """Update task status."""
    task = await crud_task.get(db, id=task_id)
    if not task:
        raise TaskNotFoundException(str(task_id))
    
    old_status = getattr(task, 'status', 'Unknown')
    
    # Validate status transition
    if not await status_manager.transition_task_status(
        str(task_id), old_status, status
    ):
        allowed = TaskStateMachine.get_allowed_transitions(old_status)
        raise InvalidTaskStatusException(
            old_status, 
            f"Allowed transitions: {', '.join(sorted(allowed))}"
        )
    
    updated_task = await crud_task.update(db, db_obj=task, obj_in={'status': status})
    await db.commit()
    new_status = getattr(updated_task, 'status', status)
    return {"task_id": str(task_id), "old_status": old_status, "new_status": new_status}


@router.get("/tasks/{task_id}/criteria", response_model=List[EvaluationCriteriaInDB])
async def get_task_criteria(
    *,
    db: AsyncSession = Depends(get_db),
    task_id: UUID
) -> Any:
    """Get all evaluation criteria for a task."""
    criteria_models = await crud_criteria.get_by_task(db, task_id=task_id)
    return [EvaluationCriteriaInDB.model_validate(c) for c in criteria_models]


@router.post("/tasks/{task_id}/criteria", response_model=EvaluationCriteriaInDB)
async def create_criteria(
    *,
    db: AsyncSession = Depends(get_db),
    task_id: UUID,
    criteria_in: EvaluationCriteriaCreate,
    request: Request
) -> Any:
    """Create new evaluation criteria for a task."""
    # Check if task exists
    task = await crud_task.get(db, id=task_id)
    if not task:
        raise TaskNotFoundException(str(task_id))
    
    # Get user ID from request
    user_id = getattr(request.state, "user_id", None)
    
    # Create criteria with task_id
    criteria_in.task_id = str(task_id)
    criteria = await crud_criteria.create(db, obj_in=criteria_in, user_id=user_id)
    # Convert to response model explicitly to avoid alias issue
    return EvaluationCriteriaInDB.model_validate(criteria, from_attributes=True)


@router.put("/criteria/{criteria_id}", response_model=EvaluationCriteriaInDB)
async def update_criteria(
    *,
    db: AsyncSession = Depends(get_db),
    criteria_id: UUID,
    criteria_in: EvaluationCriteriaUpdate,
    request: Request
) -> Any:
    """Update evaluation criteria."""
    # Get existing criteria
    criteria = await crud_criteria.get(db, id=criteria_id)
    if not criteria:
        raise HTTPException(status_code=404, detail=f"Criteria {criteria_id} not found")
    
    # Get user ID from request
    user_id = getattr(request.state, "user_id", None)
    
    # Update criteria
    update_data = criteria_in.model_dump(exclude_unset=True)
    criteria = await crud_criteria.update(db, db_obj=criteria, obj_in=update_data, user_id=user_id)
    return EvaluationCriteriaInDB.model_validate(criteria, from_attributes=True)


@router.get("/criteria/{criteria_id}/usage")
async def get_criteria_usage(
    *,
    db: AsyncSession = Depends(get_db),
    criteria_id: UUID
) -> Any:
    """Check if criteria has been used in evaluations."""
    criteria = await crud_criteria.get(db, id=criteria_id)
    if not criteria:
        raise HTTPException(status_code=404, detail=f"Criteria {criteria_id} not found")
    
    has_results = await crud_criteria.has_results(db, criteria_id=criteria_id)
    
    return {
        "criteria_id": str(criteria_id),
        "has_results": has_results,
        "can_delete": not has_results,
        "can_modify": True  # Always allow modification, but may create new version
    }


@router.get("/criteria/{criteria_id}/history")
async def get_criteria_history(
    *,
    db: AsyncSession = Depends(get_db),
    criteria_id: UUID
) -> Any:
    """Get modification history for criteria."""
    criteria = await crud_criteria.get(db, id=criteria_id)
    if not criteria:
        raise HTTPException(status_code=404, detail=f"Criteria {criteria_id} not found")
    
    # Get all related criteria (versions) by tracking original_criteria_id
    history = await crud_criteria.get_history(db, criteria_id=criteria_id)
    
    # Format history entries
    history_entries = []
    for idx, entry in enumerate(history):
        history_entries.append({
            "id": str(entry.id),
            "criteria_id": str(entry.id),
            "modified_at": entry.modified_at or entry.created_at,
            "modified_by_user_id": entry.modified_by_user_id or entry.created_by_user_id or "system",
            "source": entry.source,
            "version_number": idx + 1,
            "is_current": str(entry.id) == str(criteria_id),
            "changes": []  # Would need more sophisticated tracking for detailed changes
        })
    
    return history_entries


@router.delete("/criteria/{criteria_id}", status_code=204)
async def delete_criteria(
    *,
    db: AsyncSession = Depends(get_db),
    criteria_id: UUID
) -> None:
    """Delete evaluation criteria."""
    criteria = await crud_criteria.get(db, id=criteria_id)
    if not criteria:
        raise HTTPException(status_code=404, detail=f"Criteria {criteria_id} not found")
    
    # Check if criteria has been used in evaluations using async method
    if await crud_criteria.has_results(db, criteria_id=criteria_id):
        raise HTTPException(
            status_code=400, 
            detail="Cannot delete criteria that has been used in evaluations"
        )
    
    await crud_criteria.remove(db, id=criteria_id)
    logger.info(f"Deleted evaluation criteria {criteria_id}")


@router.post("/criteria/{criteria_id}/components/batch")
async def batch_update_components(
    *,
    db: AsyncSession = Depends(get_db),
    criteria_id: UUID,
    components: List[Dict[str, Any]] = Body(...),
    request: Request
) -> Any:
    """Batch update components for a criteria."""
    # Get existing criteria
    criteria = await crud_criteria.get(db, id=criteria_id)
    if not criteria:
        raise HTTPException(status_code=404, detail=f"Criteria {criteria_id} not found")
    
    # Get user ID from request
    user_id = getattr(request.state, "user_id", None)
    
    # Update meta_data with new components
    existing_meta = getattr(criteria, 'meta_data', {}) or {}
    update_data = {
        "meta_data": {
            **existing_meta,
            "components": components
        }
    }
    
    # Update criteria
    criteria = await crud_criteria.update(db, db_obj=criteria, obj_in=update_data, user_id=user_id)
    return {"status": "success", "updated_components": len(components)}


@router.get("/tasks/{task_id}/capabilities")
async def get_task_capabilities(
    *,
    db: AsyncSession = Depends(get_db),
    task_id: UUID
) -> Any:
    """Get current capabilities based on task status."""
    task = await crud_task.get(db, id=task_id)
    if not task:
        raise TaskNotFoundException(str(task_id))
    
    task_status = getattr(task, 'status', 'Unknown')
    capabilities = status_manager.get_task_capabilities(task_status)
    
    return {
        "task_id": str(task_id),
        "current_status": task_status,
        "capabilities": capabilities
    }