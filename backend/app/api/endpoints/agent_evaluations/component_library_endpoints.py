"""Component library endpoints for criteria management."""
import logging
from typing import List, Optional, Any, Dict
from uuid import UUID
from fastapi import APIRouter, Depends, HTTPException, Body, Request, Query
from sqlalchemy.ext.asyncio import AsyncSession

from app.db.base import get_db
from app.crud.component_library import component_library as crud_component_library
from app.models.agent_evaluation import CriteriaComponentLibrary

logger = logging.getLogger(__name__)

router = APIRouter()


@router.get("/library/components", response_model=List[Dict[str, Any]])
async def list_components(
    *,
    db: AsyncSession = Depends(get_db),
    request: Request,
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=500),
    category: Optional[str] = None,
    visibility: Optional[str] = None,
    search: Optional[str] = None
) -> Any:
    """List component library with filtering."""
    # Get user ID from request
    user_id = getattr(request.state, "user_id", None)
    
    components = await crud_component_library.get_multi(
        db,
        skip=skip,
        limit=limit,
        user_id=user_id,
        category=category,
        visibility=visibility,
        search=search
    )
    
    # Convert to dict for response
    return [
        {
            "id": comp.id,
            "name": comp.name,
            "description": comp.description,
            "category": comp.category,
            "evaluation_method": comp.evaluation_method,
            "scoring_rubric": getattr(comp, 'scoring_rubric', {}),
            "tags": getattr(comp, 'tags', []) or [],
            "created_by_user_id": getattr(comp, 'created_by_user_id', None),
            "visibility": getattr(comp, 'visibility', 'private'),
            "is_public": getattr(comp, 'is_public', False),
            "usage_count": getattr(comp, 'usage_count', 0),
            "created_at": getattr(comp, 'created_at').isoformat() if getattr(comp, 'created_at', None) is not None else None,
            "updated_at": getattr(comp, 'updated_at').isoformat() if getattr(comp, 'updated_at', None) is not None else None
        }
        for comp in components
    ]


@router.get("/library/components/categories", response_model=List[str])
async def get_component_categories(
    *,
    db: AsyncSession = Depends(get_db)
) -> Any:
    """Get all available component categories."""
    categories = await crud_component_library.get_categories(db)
    return categories


@router.get("/library/components/{component_id}", response_model=Dict[str, Any])
async def get_component(
    *,
    db: AsyncSession = Depends(get_db),
    component_id: UUID,
    request: Request
) -> Any:
    """Get a specific component by ID."""
    component = await crud_component_library.get(db, id=component_id)
    if not component:
        raise HTTPException(status_code=404, detail=f"Component {component_id} not found")
    
    # Check access permissions
    user_id = getattr(request.state, "user_id", None)
    if not getattr(component, 'is_public', False) and getattr(component, 'created_by_user_id', None) != user_id:
        raise HTTPException(status_code=403, detail="Access denied to private component")
    
    return {
        "id": component.id,
        "name": component.name,
        "description": component.description,
        "category": component.category,
        "evaluation_method": component.evaluation_method,
        "scoring_rubric": getattr(component, 'scoring_rubric', {}),
        "tags": getattr(component, 'tags', []) or [],
        "created_by_user_id": getattr(component, 'created_by_user_id', None),
        "visibility": getattr(component, 'visibility', 'private'),
        "is_public": getattr(component, 'is_public', False),
        "usage_count": getattr(component, 'usage_count', 0),
        "meta_data": getattr(component, 'meta_data', {}),
        "created_at": getattr(component, 'created_at').isoformat() if getattr(component, 'created_at', None) else None,
        "updated_at": getattr(component, 'updated_at').isoformat() if getattr(component, 'updated_at', None) else None
    }


@router.post("/library/components", response_model=Dict[str, Any])
async def create_component(
    *,
    db: AsyncSession = Depends(get_db),
    component_data: Dict[str, Any] = Body(...),
    request: Request
) -> Any:
    """Create a new component in the library."""
    # Get user ID from request
    user_id = getattr(request.state, "user_id", None)
    
    # Validate required fields
    required_fields = ["name", "category"]
    for field in required_fields:
        if field not in component_data:
            raise HTTPException(status_code=400, detail=f"Missing required field: {field}")
    
    # Separate top-level fields from meta_data
    known_fields = [
        "name", "description", "category", "evaluation_method",
        "scoring_rubric", "tags", "visibility", "is_public"
    ]
    
    db_data = {
        field: component_data.pop(field)
        for field in known_fields 
        if field in component_data
    }
    
    # Remaining data is meta_data
    db_data["meta_data"] = component_data
    
    # Create component
    component = await crud_component_library.create(
        db,
        component_data=db_data,
        user_id=user_id
    )
    
    logger.info(f"Created component {component.id} by user {user_id}")
    
    return {
        "id": component.id,
        "name": component.name,
        "description": component.description,
        "category": component.category,
        "evaluation_method": component.evaluation_method,
        "scoring_rubric": component.scoring_rubric,
        "tags": component.tags or [],
        "created_by_user_id": component.created_by_user_id,
        "visibility": component.visibility,
        "is_public": component.is_public,
        "usage_count": component.usage_count,
        "created_at": component.created_at.isoformat() if component.created_at is not None else None
    }


@router.put("/library/components/{component_id}", response_model=Dict[str, Any])
async def update_component(
    *,
    db: AsyncSession = Depends(get_db),
    component_id: UUID,
    component_update: Dict[str, Any] = Body(...),
    request: Request
) -> Any:
    """Update a component in the library."""
    # Get existing component
    component = await crud_component_library.get(db, id=component_id)
    if not component:
        raise HTTPException(status_code=404, detail=f"Component {component_id} not found")
    
    # Check ownership
    user_id = getattr(request.state, "user_id", None)
    if component.created_by_user_id != user_id:
        raise HTTPException(status_code=403, detail="Only the creator can update a component")
    
    # Update component
    component = await crud_component_library.update(
        db,
        db_obj=component,
        obj_in=component_update
    )
    
    return {
        "id": component.id,
        "name": component.name,
        "description": component.description,
        "category": component.category,
        "evaluation_method": component.evaluation_method,
        "scoring_rubric": component.scoring_rubric,
        "tags": component.tags or [],
        "created_by_user_id": component.created_by_user_id,
        "visibility": component.visibility,
        "is_public": component.is_public,
        "usage_count": component.usage_count,
        "updated_at": component.updated_at.isoformat() if component.updated_at is not None else None
    }


@router.delete("/library/components/{component_id}", status_code=204)
async def delete_component(
    *,
    db: AsyncSession = Depends(get_db),
    component_id: UUID,
    request: Request
) -> None:
    """Delete a component from the library."""
    # Get existing component
    component = await crud_component_library.get(db, id=component_id)
    if not component:
        raise HTTPException(status_code=404, detail=f"Component {component_id} not found")
    
    # Check ownership
    user_id = getattr(request.state, "user_id", None)
    if component.created_by_user_id != user_id:
        raise HTTPException(status_code=403, detail="Only the creator can delete a component")
    
    # Check if component is being used
    if getattr(component, 'usage_count', 0) > 0:
        raise HTTPException(
            status_code=400,
            detail=f"Cannot delete component that has been used {component.usage_count} times"
        )
    
    await crud_component_library.remove(db, id=component_id)
    logger.info(f"Deleted component {component_id} by user {user_id}")


@router.post("/library/components/{component_id}/duplicate", response_model=Dict[str, Any])
async def duplicate_component(
    *,
    db: AsyncSession = Depends(get_db),
    component_id: UUID,
    request: Request,
    new_name: Optional[str] = Body(None, embed=True)
) -> Any:
    """Duplicate a component for customization."""
    # Get original component
    original = await crud_component_library.get(db, id=component_id)
    if not original:
        raise HTTPException(status_code=404, detail=f"Component {component_id} not found")
    
    # Check access
    user_id = getattr(request.state, "user_id", None)
    if not getattr(original, 'is_public', False) and original.created_by_user_id != user_id:
        raise HTTPException(status_code=403, detail="Access denied to private component")
    
    # Create duplicate
    duplicate_data = {
        "name": new_name or f"{original.name} (Copy)",
        "description": original.description,
        "category": original.category,
        "evaluation_method": original.evaluation_method,
        "scoring_rubric": original.scoring_rubric,
        "tags": original.tags or [],
        "visibility": "private",  # Duplicates start as private
        "is_public": False,
        "meta_data": {
            **getattr(original, 'meta_data', {}),
            "duplicated_from": original.id
        }
    }
    
    duplicate = await crud_component_library.create(
        db,
        component_data=duplicate_data,
        user_id=user_id
    )
    
    # Increment usage count of original
    await crud_component_library.increment_usage(db, id=component_id)
    
    logger.info(f"Duplicated component {component_id} to {duplicate.id} by user {user_id}")
    
    return {
        "id": duplicate.id,
        "name": duplicate.name,
        "description": duplicate.description,
        "category": duplicate.category,
        "evaluation_method": duplicate.evaluation_method,
        "scoring_rubric": duplicate.scoring_rubric,
        "tags": duplicate.tags or [],
        "created_by_user_id": duplicate.created_by_user_id,
        "visibility": duplicate.visibility,
        "is_public": duplicate.is_public,
        "created_at": duplicate.created_at.isoformat() if duplicate.created_at is not None else None
    }


@router.post("/library/components/from-criteria/{criteria_id}")
async def save_criteria_component_to_library(
    *,
    db: AsyncSession = Depends(get_db),
    criteria_id: UUID,
    component_index: int = Body(..., embed=True),
    request: Request
) -> Any:
    """Save a component from existing criteria to the library."""
    from app.crud.agent_evaluation import evaluation_criteria as crud_criteria
    
    # Get criteria
    criteria = await crud_criteria.get(db, id=criteria_id)
    if not criteria:
        raise HTTPException(status_code=404, detail=f"Criteria {criteria_id} not found")
    
    # Get user ID
    user_id = getattr(request.state, "user_id", None)
    
    # Extract component from criteria meta_data
    components = criteria.meta_data.get("components", [])
    if component_index < 0 or component_index >= len(components):
        raise HTTPException(status_code=400, detail="Invalid component index")
    
    component_data = components[component_index]
    
    # Create library component
    # Ensure tags is a proper list
    tags_list = [criteria.name, "extracted"]
    
    library_component_data = {
        "name": component_data.get("name", f"Component from {criteria.name}"),
        "description": component_data.get("description", ""),
        "category": criteria.criteria_type,
        "evaluation_method": component_data.get("evaluation_method", ""),
        "scoring_rubric": component_data.get("scoring_rubric", {}),
        "tags": tags_list,
        "visibility": "private",
        "is_public": False,
        "meta_data": {
            "source_criteria_id": str(criteria_id),
            "source_criteria_name": criteria.name
        }
    }
    
    logger.info(f"Creating component with tags: {tags_list}, type: {type(tags_list)}")
    
    try:
        component = await crud_component_library.create(
            db,
            component_data=library_component_data,
            user_id=user_id
        )
    except Exception as e:
        logger.error(f"Error creating component: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to create component: {str(e)}")
    
    logger.info(f"Saved component from criteria {criteria_id} to library as {component.id}")
    
    return {
        "id": component.id,
        "name": component.name,
        "description": component.description,
        "category": component.category,
        "message": "Component saved to library successfully"
    }