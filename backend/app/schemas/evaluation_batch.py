"""Schemas for evaluation batches."""
from typing import Optional, List, Dict, Any
from datetime import datetime
from pydantic import BaseModel, Field


class EvaluationBatchBase(BaseModel):
    """Base schema for evaluation batch."""
    task_id: str
    status: str = "running"
    models_used: List[str] = Field(default_factory=list)
    submission_count: int = 0
    successful_evaluations: int = 0
    failed_evaluations: int = 0
    summary_stats: Dict[str, Any] = Field(default_factory=dict)


class EvaluationBatchCreate(EvaluationBatchBase):
    """Schema for creating evaluation batch."""
    batch_number: int


class EvaluationBatchUpdate(BaseModel):
    """Schema for updating evaluation batch."""
    status: Optional[str] = None
    models_used: Optional[List[str]] = None
    submission_count: Optional[int] = None
    successful_evaluations: Optional[int] = None
    failed_evaluations: Optional[int] = None
    summary_stats: Optional[Dict[str, Any]] = None
    completed_at: Optional[datetime] = None


class EvaluationBatchInDBBase(EvaluationBatchBase):
    """Base schema for evaluation batch in DB."""
    id: str
    batch_number: int
    started_at: datetime
    completed_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True


class EvaluationBatch(EvaluationBatchInDBBase):
    """Schema for evaluation batch."""
    pass


class EvaluationBatchWithDetails(EvaluationBatch):
    """Schema for evaluation batch with additional details."""
    evaluation_count: int = 0
    avg_score: Optional[float] = None
    winner: Optional[Dict[str, Any]] = None  # Agent name, version, score