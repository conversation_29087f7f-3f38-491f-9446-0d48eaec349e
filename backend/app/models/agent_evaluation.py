"""SQLAlchemy models for agent evaluation."""
from sqlalchemy import Column, String, Text, Integer, Float, Boolean, DateTime, ForeignKey, JSON, Index
from sqlalchemy.dialects.postgresql import ARRAY
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.db.base import Base
from datetime import datetime


class AgentEvaluationTask(Base):
    """Model for agent evaluation tasks."""
    __tablename__ = "agent_evaluation_tasks"
    
    id = Column(String, primary_key=True, index=True)
    evaluation_type = Column(String, nullable=False)  # '0-1' or '90-100'
    name = Column(String, nullable=False)
    description = Column(Text)
    prompt = Column(Text)  # For 0-1 evaluation
    base_repo_url = Column(String)  # For 90-100 evaluation
    base_commit = Column(String)  # For 90-100 evaluation
    configuration = Column(JSON, default={})
    status = Column(String, nullable=False, default="draft")  # draft, generating, ready, running, completed, failed
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    submissions = relationship("AgentSubmission", back_populates="task", cascade="all, delete-orphan")
    criteria = relationship("EvaluationCriteria", back_populates="task", cascade="all, delete-orphan")
    evaluations = relationship("AgentEvaluation", back_populates="task", cascade="all, delete-orphan")
    batches = relationship("EvaluationBatch", back_populates="task", cascade="all, delete-orphan")
    
    # Indexes
    __table_args__ = (
        Index('idx_agent_tasks_evaluation_type', 'evaluation_type'),
        Index('idx_agent_tasks_status', 'status'),
    )


class AgentSubmission(Base):
    """Model for agent submissions."""
    __tablename__ = "agent_submissions"
    
    id = Column(String, primary_key=True, index=True)
    task_id = Column(String, ForeignKey("agent_evaluation_tasks.id"), nullable=False)
    agent_name = Column(String, nullable=False)
    agent_version = Column(String)
    folder_path = Column(String)  # For 0-1 evaluation
    worktree_path = Column(String)  # For 90-100 evaluation
    status = Column(String, default="pending")
    total_tokens = Column(Integer, default=0)
    meta_data = Column(JSON, default={})
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    task = relationship("AgentEvaluationTask", back_populates="submissions")
    selected_commits = relationship("SelectedCommit", back_populates="submission", cascade="all, delete-orphan")
    contexts = relationship("EvaluationContext", back_populates="submission", cascade="all, delete-orphan")
    evaluations = relationship("AgentEvaluation", back_populates="submission")
    
    # Indexes
    __table_args__ = (
        Index('idx_agent_submissions_task', 'task_id'),
        Index('idx_agent_submissions_agent', 'agent_name', 'agent_version'),
    )


class SelectedCommit(Base):
    """Model for selected commits in 90-100 evaluation."""
    __tablename__ = "selected_commits"
    
    id = Column(String, primary_key=True, index=True)
    submission_id = Column(String, ForeignKey("agent_submissions.id"), nullable=False)
    commit_hash = Column(String, nullable=False)
    commit_message = Column(Text)
    commit_author = Column(String)
    commit_date = Column(DateTime(timezone=True))
    commit_order = Column(Integer)  # Order of commits
    meta_data = Column(JSON, default={})
    
    # Relationships
    submission = relationship("AgentSubmission", back_populates="selected_commits")
    
    # Indexes
    __table_args__ = (
        Index('idx_selected_commits_submission', 'submission_id'),
        Index('idx_selected_commits_hash', 'commit_hash'),
    )


class EvaluationContext(Base):
    """Model for evaluation context storage."""
    __tablename__ = "evaluation_contexts"
    
    id = Column(String, primary_key=True, index=True)
    submission_id = Column(String, ForeignKey("agent_submissions.id"), nullable=False)
    context_type = Column(String, nullable=False)  # 'file', 'diff', 'meta_data', etc.
    file_path = Column(String)
    commit_hash = Column(String)
    content = Column(Text)  # For inline storage
    content_hash = Column(String, index=True)
    content_size = Column(Integer)
    blob_id = Column(String, ForeignKey("evaluation_context_blobs.id"))
    storage_type = Column(String, default="inline")  # 'inline' or 'blob'
    estimated_tokens = Column(Integer, default=0)
    meta_data = Column(JSON, default={})
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    submission = relationship("AgentSubmission", back_populates="contexts")
    blob = relationship("EvaluationContextBlob", back_populates="contexts")
    
    # Indexes
    __table_args__ = (
        Index('idx_evaluation_contexts_submission', 'submission_id'),
        Index('idx_evaluation_contexts_type', 'context_type'),
        Index('idx_evaluation_contexts_commit', 'commit_hash'),
    )


class EvaluationContextBlob(Base):
    """Model for large context blob storage."""
    __tablename__ = "evaluation_context_blobs"
    
    id = Column(String, primary_key=True, index=True)
    blob_id = Column(String, unique=True, index=True)
    storage_backend = Column(String, default="filesystem")
    storage_path = Column(String)
    compressed_size = Column(Integer)
    compression_type = Column(String, default="gzip")
    meta_data = Column(JSON, default={})
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    contexts = relationship("EvaluationContext", back_populates="blob")


class EvaluationCriteria(Base):
    """Model for evaluation criteria."""
    __tablename__ = "evaluation_criteria"
    
    id = Column(String, primary_key=True, index=True)
    task_id = Column(String, ForeignKey("agent_evaluation_tasks.id"), nullable=False)
    name = Column(String, nullable=False)
    description = Column(Text)
    weight = Column(Float, default=1.0)
    criteria_type = Column(String, default="quality")
    evaluation_prompt = Column(Text)
    meta_data = Column(JSON, default={})
    
    # New fields for editing support
    source = Column(String, default="GENERATED", nullable=False)  # GENERATED, GENERATED_EDITED, USER_CREATED
    created_by_user_id = Column(String, nullable=True)
    modified_by_user_id = Column(String, nullable=True)
    modified_at = Column(DateTime(timezone=True), nullable=True)
    original_criteria_id = Column(String, nullable=True)
    is_template = Column(Boolean, default=False)
    
    # Relationships
    task = relationship("AgentEvaluationTask", back_populates="criteria")
    results = relationship("EvaluationResult", back_populates="criteria")
    
    # Indexes
    __table_args__ = (
        Index('idx_evaluation_criteria_task', 'task_id'),
        Index('idx_evaluation_criteria_name', 'task_id', 'name', unique=True),
        Index('idx_criteria_source', 'source'),
        Index('idx_criteria_created_by', 'created_by_user_id'),
        Index('idx_criteria_template', 'is_template'),
    )


class AgentEvaluation(Base):
    """Model for agent evaluations."""
    __tablename__ = "agent_evaluations"
    
    id = Column(String, primary_key=True, index=True)
    task_id = Column(String, ForeignKey("agent_evaluation_tasks.id"), nullable=False)
    submission_id = Column(String, ForeignKey("agent_submissions.id"), nullable=False)
    batch_id = Column(String, ForeignKey("evaluation_batches.id"), nullable=True)  # Optional for backward compatibility
    model_name = Column(String, nullable=False)
    model_parameters = Column(JSON, default={})
    status = Column(String, default="pending")
    overall_score = Column(Float)
    usage_stats = Column(JSON, default={})
    completion_time = Column(Float)  # Seconds
    error_message = Column(Text)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    completed_at = Column(DateTime(timezone=True))
    
    # New field for human evaluations
    created_by_user_id = Column(String, nullable=True)
    
    # Relationships
    task = relationship("AgentEvaluationTask", back_populates="evaluations")
    submission = relationship("AgentSubmission", back_populates="evaluations")
    batch = relationship("EvaluationBatch", back_populates="evaluations")
    results = relationship("EvaluationResult", back_populates="evaluation", cascade="all, delete-orphan")
    
    # Indexes
    __table_args__ = (
        Index('idx_agent_evaluations_task', 'task_id'),
        Index('idx_agent_evaluations_submission', 'submission_id'),
        Index('idx_agent_evaluations_batch', 'batch_id'),
        Index('idx_agent_evaluations_model', 'model_name'),
        Index('idx_agent_evaluations_status', 'status'),
        Index('idx_evaluations_created_by', 'created_by_user_id'),
    )


class EvaluationResult(Base):
    """Model for evaluation results per criteria."""
    __tablename__ = "evaluation_results"
    
    id = Column(String, primary_key=True, index=True)
    evaluation_id = Column(String, ForeignKey("agent_evaluations.id"), nullable=False)
    criteria_id = Column(String, ForeignKey("evaluation_criteria.id"), nullable=False)
    score = Column(Float, nullable=False)  # 0-100
    reasoning = Column(Text)
    evidence = Column(JSON, default=[])  # List of supporting evidence
    meta_data = Column(JSON, default={})
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # New field for human evaluations
    created_by_user_id = Column(String, nullable=True)
    
    # Relationships
    evaluation = relationship("AgentEvaluation", back_populates="results")
    criteria = relationship("EvaluationCriteria", back_populates="results")
    
    # Indexes
    __table_args__ = (
        Index('idx_evaluation_results_evaluation', 'evaluation_id'),
        Index('idx_evaluation_results_criteria', 'evaluation_id', 'criteria_id', unique=True),
        Index('idx_results_created_by', 'created_by_user_id'),
    )


class EvaluationBatch(Base):
    """Model for evaluation batches."""
    __tablename__ = "evaluation_batches"
    
    id = Column(String, primary_key=True, index=True)
    task_id = Column(String, ForeignKey("agent_evaluation_tasks.id"), nullable=False)
    batch_number = Column(Integer, nullable=False)  # Sequential batch number for this task
    status = Column(String, default="running")  # running, completed, failed
    models_used = Column(JSON, default=[])  # List of models used in this batch
    submission_count = Column(Integer, default=0)
    successful_evaluations = Column(Integer, default=0)
    failed_evaluations = Column(Integer, default=0)
    summary_stats = Column(JSON, default={})  # Avg score, winner, etc.
    started_at = Column(DateTime(timezone=True), server_default=func.now())
    completed_at = Column(DateTime(timezone=True))
    
    # Relationships
    task = relationship("AgentEvaluationTask", back_populates="batches")
    evaluations = relationship("AgentEvaluation", back_populates="batch")
    
    # Indexes
    __table_args__ = (
        Index('idx_evaluation_batches_task', 'task_id'),
        Index('idx_evaluation_batches_status', 'status'),
        Index('idx_evaluation_batches_number', 'task_id', 'batch_number', unique=True),
    )


class CriteriaComponentLibrary(Base):
    """Model for criteria component library."""
    __tablename__ = "criteria_component_library"
    
    id = Column(String, primary_key=True, index=True)
    name = Column(String(255), nullable=False)
    description = Column(Text)
    category = Column(String(100), nullable=False)
    evaluation_method = Column(Text)
    scoring_rubric = Column(JSON)  # {"0-30": "...", "31-60": "...", etc.}
    tags = Column(ARRAY(String), default=list)  # Array of strings
    created_by_user_id = Column(String(100), nullable=True)
    is_public = Column(Boolean, default=False)
    visibility = Column(String(50), default="private")  # private, team, public
    usage_count = Column(Integer, default=0)
    meta_data = Column(JSON, default={})
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Indexes
    __table_args__ = (
        Index('idx_comp_lib_category', 'category'),
        Index('idx_comp_lib_user', 'created_by_user_id'),
        Index('idx_comp_lib_visibility', 'visibility'),
        Index('idx_comp_lib_public', 'is_public'),
    )


class CriteriaModifications(Base):
    """Model for tracking criteria and component modifications."""
    __tablename__ = "criteria_modifications"
    
    id = Column(String, primary_key=True, index=True)
    entity_type = Column(String(50), nullable=False)  # 'criteria' or 'component'
    entity_id = Column(String, nullable=False)
    field_path = Column(String(255), nullable=False)  # e.g., 'description', 'components.0.scoring_rubric'
    old_value = Column(Text)
    new_value = Column(Text)
    change_type = Column(String(50))  # 'edit', 'add_component', 'remove_component', etc.
    modified_by = Column(String(100), nullable=True)
    modified_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Indexes
    __table_args__ = (
        Index('idx_modifications_entity', 'entity_type', 'entity_id'),
    )