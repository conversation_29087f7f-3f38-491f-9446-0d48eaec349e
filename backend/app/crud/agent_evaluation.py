"""CRUD operations for agent evaluation models."""
from typing import List, Optional, Dict, Any
from uuid import UUID, uuid4
from datetime import datetime, timezone
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy.orm import selectinload, aliased
from sqlalchemy import func, or_

from app.models.agent_evaluation import (
    AgentEvaluationTask,
    AgentSubmission,
    EvaluationCriteria,
    AgentEvaluation,
    EvaluationResult,
    EvaluationContext,
    EvaluationContextBlob
)
from app.schemas.agent_evaluation import (
    AgentEvaluationTaskCreate,
    AgentSubmissionCreate,
    EvaluationCriteriaCreate,
    AgentEvaluationCreate,
    EvaluationResultCreate
)


class CRUDAgentTask:
    """CRUD operations for agent evaluation tasks."""
    
    async def create(self, db: AsyncSession, *, obj_in: AgentEvaluationTaskCreate) -> AgentEvaluationTask:
        """Create a new agent evaluation task."""
        db_obj = AgentEvaluationTask(
            id=str(uuid4()),
            evaluation_type=obj_in.evaluation_type,
            name=obj_in.name,
            description=obj_in.description,
            prompt=obj_in.prompt,
            base_repo_url=obj_in.base_repo_url,
            base_commit=obj_in.base_commit,
            configuration=obj_in.configuration or {},
            status="draft"
        )
        db.add(db_obj)
        await db.commit()
        await db.refresh(db_obj)
        return db_obj
    
    async def get(self, db: AsyncSession, id: UUID) -> Optional[AgentEvaluationTask]:
        """Get a task by ID."""
        result = await db.execute(
            select(AgentEvaluationTask).where(AgentEvaluationTask.id == str(id))
        )
        return result.scalar_one_or_none()
    
    async def get_multi(
        self, db: AsyncSession, *, skip: int = 0, limit: int = 100
    ) -> List[AgentEvaluationTask]:
        """Get multiple tasks."""
        result = await db.execute(
            select(AgentEvaluationTask)
            .offset(skip)
            .limit(limit)
            .order_by(AgentEvaluationTask.created_at.desc())
        )
        return list(result.scalars().all())
    
    async def update(
        self, db: AsyncSession, *, db_obj: AgentEvaluationTask, obj_in: Dict[str, Any]
    ) -> AgentEvaluationTask:
        """Update a task."""
        for field, value in obj_in.items():
            setattr(db_obj, field, value)
        await db.commit()
        await db.refresh(db_obj)
        return db_obj

    async def remove(self, db: AsyncSession, *, id: UUID) -> Optional[AgentEvaluationTask]:
        """Remove a task by ID. Related records will be cascade deleted."""
        task = await self.get(db, id=id)
        if task:
            await db.delete(task)
            await db.commit()
        return task


class CRUDAgentSubmission:
    """CRUD operations for agent submissions."""
    
    async def create(self, db: AsyncSession, *, obj_in: AgentSubmissionCreate) -> AgentSubmission:
        """Create a new agent submission."""
        db_obj = AgentSubmission(
            id=str(uuid4()),
            task_id=obj_in.task_id,
            agent_name=obj_in.agent_name,
            agent_version=obj_in.agent_version,
            folder_path=obj_in.folder_path,
            worktree_path=obj_in.worktree_path,
            meta_data=getattr(obj_in, 'meta_data', None) or {},
            status="pending",
            total_tokens=0
        )
        db.add(db_obj)
        await db.commit()
        await db.refresh(db_obj)
        return db_obj
    
    async def get(self, db: AsyncSession, id: UUID) -> Optional[AgentSubmission]:
        """Get a submission by ID."""
        result = await db.execute(
            select(AgentSubmission).where(AgentSubmission.id == str(id))
        )
        return result.scalar_one_or_none()
    
    async def get_by_task(self, db: AsyncSession, *, task_id: UUID) -> List[AgentSubmission]:
        """Get all submissions for a task, including the count of related evaluations."""
        
        # Subquery to count evaluations for each submission
        evaluation_count_subquery = (
            select(
                AgentEvaluation.submission_id,
                func.count(AgentEvaluation.id).label("evaluation_count")
            )
            .group_by(AgentEvaluation.submission_id)
            .subquery()
        )
        
        # Alias the subquery to join against it
        evaluation_count_alias = aliased(evaluation_count_subquery)
        
        # Main query to get submissions and join with the evaluation count
        result = await db.execute(
            select(
                AgentSubmission,
                evaluation_count_alias.c.evaluation_count
            )
            .outerjoin(
                evaluation_count_alias,
                AgentSubmission.id == evaluation_count_alias.c.submission_id
            )
            .where(AgentSubmission.task_id == str(task_id))
            .order_by(AgentSubmission.created_at)
        )
        
        # Process results and attach the count to each submission object
        submissions = []
        for submission, count in result.all():
            submission.evaluation_count = count or 0
            submissions.append(submission)
            
        return submissions
    
    async def update(
        self, db: AsyncSession, *, db_obj: AgentSubmission, obj_in: Dict[str, Any]
    ) -> AgentSubmission:
        """Update a submission."""
        for field, value in obj_in.items():
            setattr(db_obj, field, value)
        await db.commit()
        await db.refresh(db_obj)
        return db_obj

    async def remove(self, db: AsyncSession, *, id: UUID) -> Optional[AgentSubmission]:
        """Remove a submission by ID."""
        submission = await self.get(db, id=id)
        if submission:
            await db.delete(submission)
            await db.commit()
        return submission


class CRUDEvaluationCriteria:
    """CRUD operations for evaluation criteria."""
    
    async def create(self, db: AsyncSession, *, obj_in: EvaluationCriteriaCreate, user_id: Optional[str] = None) -> EvaluationCriteria:
        """Create new evaluation criteria."""
        db_obj = EvaluationCriteria(
            id=str(uuid4()),
            task_id=obj_in.task_id,
            name=obj_in.name,
            description=obj_in.description,
            weight=obj_in.weight,
            criteria_type=obj_in.criteria_type,
            evaluation_prompt=obj_in.evaluation_prompt,
            meta_data=getattr(obj_in, 'meta_data', None) or {},
            source=getattr(obj_in, 'source', 'USER_CREATED'),
            created_by_user_id=user_id
        )
        db.add(db_obj)
        await db.commit()
        await db.refresh(db_obj)
        return db_obj
    
    async def get(self, db: AsyncSession, id: UUID) -> Optional[EvaluationCriteria]:
        """Get a criteria by ID."""
        result = await db.execute(
            select(EvaluationCriteria).where(EvaluationCriteria.id == str(id))
        )
        return result.scalar_one_or_none()
    
    async def get_by_task(self, db: AsyncSession, *, task_id: UUID) -> List[EvaluationCriteria]:
        """Get all criteria for a task."""
        result = await db.execute(
            select(EvaluationCriteria)
            .where(EvaluationCriteria.task_id == str(task_id))
            .order_by(EvaluationCriteria.name)
        )
        return list(result.scalars().all())
    
    async def update(
        self, db: AsyncSession, *, db_obj: EvaluationCriteria, obj_in: Dict[str, Any], user_id: Optional[str] = None
    ) -> EvaluationCriteria:
        """Update evaluation criteria."""
        # Track source changes - check the actual value, not the SQLAlchemy column
        current_source = getattr(db_obj, 'source', 'GENERATED')
        if current_source == "GENERATED" and any(field in obj_in for field in ["name", "description", "weight", "meta_data"]):
            obj_in['source'] = "GENERATED_EDITED"
        
        # Track modification
        if user_id:
            obj_in['modified_by_user_id'] = user_id
            obj_in['modified_at'] = datetime.now(timezone.utc)
        
        # Update fields
        for field, value in obj_in.items():
            setattr(db_obj, field, value)
        
        await db.commit()
        await db.refresh(db_obj)
        return db_obj
    
    async def has_results(self, db: AsyncSession, *, criteria_id: UUID) -> bool:
        """Check if criteria has any associated evaluation results."""
        result = await db.execute(
            select(func.count(EvaluationResult.id))
            .where(EvaluationResult.criteria_id == str(criteria_id))
        )
        count = result.scalar()
        return count is not None and count > 0
    
    async def get_history(self, db: AsyncSession, *, criteria_id: UUID) -> List[EvaluationCriteria]:
        """Get modification history for criteria."""
        criteria = await self.get(db, id=criteria_id)
        if not criteria:
            return []
        
        # Get the original criteria ID to find all versions
        original_id = getattr(criteria, 'original_criteria_id', None) or str(criteria_id)
        
        # Find all criteria that are either:
        # 1. The original criteria
        # 2. Have this criteria as their original
        # 3. Share the same original criteria
        result = await db.execute(
            select(EvaluationCriteria)
            .where(
                or_(
                    EvaluationCriteria.id == original_id,
                    EvaluationCriteria.original_criteria_id == original_id,
                    EvaluationCriteria.original_criteria_id == str(criteria_id),
                    EvaluationCriteria.id == str(criteria_id)
                )
            )
            .order_by(EvaluationCriteria.created_at)
        )
        
        return list(result.scalars().all())
    
    async def remove(self, db: AsyncSession, *, id: UUID) -> Optional[EvaluationCriteria]:
        """Remove a criteria by ID."""
        criteria = await self.get(db, id=id)
        if criteria:
            await db.delete(criteria)
            await db.commit()
        return criteria


class CRUDAgentEvaluation:
    """CRUD operations for agent evaluations."""
    
    async def create(self, db: AsyncSession, *, obj_in: AgentEvaluationCreate) -> AgentEvaluation:
        """Create a new agent evaluation."""
        db_obj = AgentEvaluation(
            id=str(uuid4()),
            task_id=obj_in.task_id,
            submission_id=obj_in.submission_id,
            batch_id=obj_in.batch_id,  # Include batch_id from the schema
            model_name=obj_in.model_name,
            model_parameters=obj_in.model_parameters or {},
            usage_stats=obj_in.usage_stats or {},
            status="pending"
        )
        db.add(db_obj)
        await db.commit()
        await db.refresh(db_obj)
        return db_obj
    
    async def get(self, db: AsyncSession, id: UUID) -> Optional[AgentEvaluation]:
        """Get an evaluation by ID."""
        result = await db.execute(
            select(AgentEvaluation).where(AgentEvaluation.id == str(id))
        )
        return result.scalar_one_or_none()
    
    async def get_by_submission(self, db: AsyncSession, *, submission_id: str) -> List[AgentEvaluation]:
        """Get all evaluations for a submission."""
        result = await db.execute(
            select(AgentEvaluation)
            .where(AgentEvaluation.submission_id == submission_id)
            .options(selectinload(AgentEvaluation.results))
            .order_by(AgentEvaluation.created_at)
        )
        return list(result.scalars().all())
    
    async def get_by_batch(self, db: AsyncSession, *, batch_id: str) -> List[AgentEvaluation]:
        """Get all evaluations for a batch."""
        result = await db.execute(
            select(AgentEvaluation)
            .where(AgentEvaluation.batch_id == batch_id)
            .options(selectinload(AgentEvaluation.results))
            .order_by(AgentEvaluation.created_at)
        )
        return list(result.scalars().all())
    
    async def get_by_task(self, db: AsyncSession, *, task_id: UUID) -> List[AgentEvaluation]:
        """Get all evaluations for a task."""
        result = await db.execute(
            select(AgentEvaluation)
            .where(AgentEvaluation.task_id == str(task_id))
            .order_by(AgentEvaluation.created_at)
        )
        return list(result.scalars().all())
    
    async def update(
        self, db: AsyncSession, *, db_obj: AgentEvaluation, obj_in: Dict[str, Any]
    ) -> AgentEvaluation:
        """Update an evaluation."""
        for field, value in obj_in.items():
            setattr(db_obj, field, value)
        await db.commit()
        await db.refresh(db_obj)
        return db_obj
    
    async def remove(self, db: AsyncSession, *, id: UUID) -> Optional[AgentEvaluation]:
        """Remove an evaluation by ID."""
        evaluation = await self.get(db, id=id)
        if evaluation:
            await db.delete(evaluation)
            await db.commit()
        return evaluation


class CRUDEvaluationResult:
    """CRUD operations for evaluation results."""
    
    async def create(self, db: AsyncSession, *, obj_in: EvaluationResultCreate) -> EvaluationResult:
        """Create a new evaluation result."""
        db_obj = EvaluationResult(
            id=str(uuid4()),
            evaluation_id=obj_in.evaluation_id,
            criteria_id=obj_in.criteria_id,
            score=obj_in.score,
            reasoning=obj_in.reasoning,
            evidence=obj_in.evidence or [],
            meta_data=getattr(obj_in, 'meta_data', None) or {}
        )
        db.add(db_obj)
        await db.commit()
        await db.refresh(db_obj)
        return db_obj
    
    async def get_by_evaluation(self, db: AsyncSession, *, evaluation_id: str) -> List[EvaluationResult]:
        """Get all results for an evaluation."""
        result = await db.execute(
            select(EvaluationResult)
            .where(EvaluationResult.evaluation_id == evaluation_id)
            .order_by(EvaluationResult.created_at)
        )
        return list(result.scalars().all())


class CRUDEvaluationContext:
    """CRUD operations for evaluation contexts."""
    model = EvaluationContext
    
    async def create(self, db: AsyncSession, *, obj_in: Dict[str, Any]) -> EvaluationContext:
        """Create a new evaluation context."""
        db_obj = EvaluationContext(
            id=str(uuid4()),
            submission_id=obj_in['submission_id'],
            context_type=obj_in['context_type'],
            file_path=obj_in.get('file_path'),
            commit_hash=obj_in.get('commit_hash'),
            content=obj_in.get('content'),
            content_hash=obj_in.get('content_hash'),
            content_size=obj_in.get('content_size', 0),
            storage_type=obj_in.get('storage_type', 'inline'),
            blob_id=obj_in.get('blob_id'),
            estimated_tokens=obj_in.get('estimated_tokens', 0),
            meta_data=getattr(obj_in, 'meta_data', None) or {}
        )
        db.add(db_obj)
        await db.commit()
        await db.refresh(db_obj)
        return db_obj
    
    async def get(self, db: AsyncSession, id: str) -> Optional[EvaluationContext]:
        """Get a context by ID."""
        result = await db.execute(
            select(EvaluationContext).where(EvaluationContext.id == id)
        )
        return result.scalar_one_or_none()
    
    async def get_by_submission(self, db: AsyncSession, *, submission_id: str, context_type: Optional[str] = None, skip: int = 0, limit: int = 10000) -> List[EvaluationContext]:
        """Get all contexts for a submission."""
        query = select(self.model).where(self.model.submission_id == submission_id)
        
        if context_type:
            query = query.where(self.model.context_type == context_type)
        
        result = await db.execute(
            query.order_by(self.model.created_at).offset(skip).limit(limit)
        )
        return list(result.scalars().all())
    
    async def remove(self, db: AsyncSession, *, id: str) -> Optional[EvaluationContext]:
        """Remove a context by ID."""
        context = await self.get(db, id=id)
        if context:
            await db.delete(context)
            await db.commit()
        return context


class CRUDEvaluationContextBlob:
    """CRUD operations for evaluation context blobs."""
    
    async def create(self, db: AsyncSession, *, obj_in: Dict[str, Any]) -> EvaluationContextBlob:
        """Create a new evaluation context blob."""
        db_obj = EvaluationContextBlob(
            id=str(uuid4()),
            blob_id=obj_in['blob_id'],
            storage_backend=obj_in.get('storage_backend', 'filesystem'),
            storage_path=obj_in.get('storage_path'),
            compressed_size=obj_in.get('compressed_size', 0),
            compression_type=obj_in.get('compression_type', 'gzip'),
            meta_data=getattr(obj_in, 'meta_data', None) or {}
        )
        db.add(db_obj)
        await db.commit()
        await db.refresh(db_obj)
        return db_obj
    
    async def get(self, db: AsyncSession, id: str) -> Optional[EvaluationContextBlob]:
        """Get a blob by ID."""
        result = await db.execute(
            select(EvaluationContextBlob).where(EvaluationContextBlob.id == id)
        )
        return result.scalar_one_or_none()
    
    async def get_by_blob_id(self, db: AsyncSession, blob_id: str) -> Optional[EvaluationContextBlob]:
        """Get a blob by its blob_id (content hash)."""
        result = await db.execute(
            select(EvaluationContextBlob).where(EvaluationContextBlob.blob_id == blob_id)
        )
        return result.scalar_one_or_none()
    
    async def remove(self, db: AsyncSession, *, id: str) -> Optional[EvaluationContextBlob]:
        """Remove a blob by ID."""
        blob = await self.get(db, id=id)
        if blob:
            await db.delete(blob)
            await db.commit()
        return blob


# Create instances
agent_task = CRUDAgentTask()
agent_submission = CRUDAgentSubmission()
evaluation_criteria = CRUDEvaluationCriteria()
agent_evaluation = CRUDAgentEvaluation()
evaluation_result = CRUDEvaluationResult()
evaluation_context = CRUDEvaluationContext()
evaluation_context_blob = CRUDEvaluationContextBlob()