[tool:pytest]
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*
addopts = 
    -v
    --tb=short
    --strict-markers
    --disable-warnings
    --asyncio-mode=auto
markers =
    asyncio: mark test as asyncio
    slow: mark test as slow running
    unit: mark test as unit test
    integration: mark test as integration test
    api: mark test as API test
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning